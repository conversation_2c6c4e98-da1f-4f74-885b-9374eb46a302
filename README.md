# 🏢 MDM设备管理系统

**基于Django + Vue.js的现代化企业级设备管理系统**

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.11+-green.svg)](https://python.org)
[![Node.js](https://img.shields.io/badge/node.js-20.x-green.svg)](https://nodejs.org)
[![Docker](https://img.shields.io/badge/docker-20.10+-blue.svg)](https://docker.com)

## ✨ 功能特性

- 🔐 **多角色权限管理** - 普通用户、设备管理员、超级管理员
- 📱 **设备全生命周期管理** - 添加、借用、归还、维护、报废
- 🎯 **标准化配置选项** - 18种品牌、5种分类、12种特殊屏幕、4种操作系统预定义选项
- 📊 **数据统计分析** - 设备使用率、借用趋势、成本分析
- 🔔 **智能通知系统** - 到期提醒、审批通知、状态变更
- 📋 **审批工作流** - 灵活的借用审批流程
- 🏷️ **标签分类管理** - 多维度设备分类和标签
- 📝 **批量导入导出** - Excel模板批量导入，支持配置选项验证
- 📈 **可视化报表** - 丰富的图表和数据展示
- 🔒 **企业级安全** - 数据加密、访问控制、审计日志

## ⚠️ 重要提醒

**在开始之前，请务必阅读 [已知问题文档](docs/KNOWN_ISSUES.md) 以避免常见问题！**

## ⚡ 快速启动

### 🎯 一键启动（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd mdm

# 2. 选择环境启动
start-dev.bat        # 开发环境（推荐新手）
start-prod.bat       # 完整生产环境
start-prod-lite.bat  # 精简生产环境

# 3. 停止环境
stop-all.bat
```

### 🌐 访问地址

| 环境 | 前端地址 | 后端地址 | 管理后台 |
|------|----------|----------|----------|
| **开发环境** | http://localhost:3000 | http://localhost:8000 | http://localhost:8000/admin/ |
| **生产环境** | http://localhost | http://localhost/api/ | http://localhost/admin/ |

### 🔑 默认管理员账户

```bash
# 开发环境 (自动创建)
用户名: admin
密码: admin123456
邮箱: <EMAIL>

# ⚠️ 重要安全提醒
# 1. 开发环境会自动创建默认管理员账户
# 2. 生产环境请务必修改默认密码！
# 3. 建议启用双因素认证

# 生产环境 (需要手动创建)
详见: docs/ADMIN_SETUP_GUIDE.md
```

## 🏗️ 技术架构

### 核心技术栈
- **后端**: Django 4.2 + Django REST Framework + PostgreSQL + Redis
- **前端**: Vue.js 3.5 + TypeScript + Vite + Element Plus
- **容器化**: Docker + Docker Compose
- **监控**: 健康检查 + 日志管理

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue.js 前端   │────│   Django 后端   │────│  PostgreSQL DB  │
│   (端口: 3000)  │    │   (端口: 8000)  │    │   (端口: 5432)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   Redis 缓存    │
                       │   (端口: 6379)  │
                       └─────────────────┘
```

## 📁 项目结构

```
mdm/
├── backend/                    # Django后端应用
├── frontend/                   # Vue.js前端应用
├── nginx/                      # Nginx配置文件
├── scripts/                    # 开发和部署脚本
├── docs/                       # 完整技术文档
├── docker-compose.*.yml        # 容器编排配置
├── start-*.bat                 # 一键启动脚本
└── README.md                   # 项目说明文档
```

## 📚 文档导航

### 🚀 快速上手
- [管理员配置指南](docs/ADMIN_SETUP_GUIDE.md) ⭐ - 管理员账户配置
- [开发指南](docs/development/DEV-GUIDE.md) - 完整开发环境搭建

### 🏗️ 系统设计
- [技术架构](docs/architecture/technical_architecture.md) - 系统架构设计
- [API设计](docs/architecture/api-design.md) - RESTful API规范
- [数据库设计](docs/architecture/database-design.md) - 数据模型设计

### 🚀 部署运维
- [生产环境部署](docs/deployment/PRODUCTION_DEPLOYMENT.md) - 完整部署流程
- [健康检查](docs/monitoring/HEALTH_CHECKS.md) - 容器健康监控
- [Docker最佳实践](docs/deployment/DOCKER_BEST_PRACTICES.md) - 容器化指南

### 👥 用户文档
- [用户操作手册](docs/user/user-guide.md) - 系统使用说明
- [编码规范](docs/development/coding-standards.md) - 代码质量标准

### 📋 项目管理
- [快速参考指南](docs/QUICK_REFERENCE_GUIDE.md) - 常用命令和检查清单
- [问题管理](docs/maintenance/ISSUE_MANAGEMENT.md) - 问题跟踪流程

## 🔧 开发工具

```bash
# 开发环境管理
dev.bat                    # 交互式开发环境启动
test.bat                   # 测试工具集

# 快速脚本
scripts/dev/               # 开发辅助脚本
scripts/test/              # 测试工具集
```

## 🤝 贡献指南

1. **Fork** 项目
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

## 📞 技术支持

- **文档中心**: [docs/](docs/)
- **问题反馈**: GitHub Issues
- **技术支持**: <EMAIL>
- **开发团队**: <EMAIL>

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

*🚀 开始您的MDM设备管理之旅！*
