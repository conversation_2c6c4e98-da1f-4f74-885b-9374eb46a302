# 生产环境配置文件
# 请根据实际部署环境修改以下配置

# Django基础配置
DEBUG=False
SECRET_KEY=prod-mdm-secret-key-2025-verification-environment
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,nginx,backend

# 数据库配置
DATABASE_URL=************************************************/mdm_production
DB_NAME=mdm_production
DB_USER=mdm_user
DB_PASSWORD=mdm_prod_pass_2025
DB_HOST=db
DB_PORT=5432

# Redis配置
REDIS_URL=redis://:redis_prod_2025@redis:6379/0
CACHE_REDIS_URL=redis://:redis_prod_2025@redis:6379/1
SESSION_REDIS_URL=redis://:redis_prod_2025@redis:6379/2

# 邮件配置 (验收环境使用控制台输出)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=localhost
EMAIL_PORT=587
EMAIL_HOST_USER=mdm@localhost
EMAIL_HOST_PASSWORD=test_password
EMAIL_USE_TLS=False
DEFAULT_FROM_EMAIL=设备管理系统 <mdm@localhost>

# 安全配置 (验收环境适当放宽)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
X_FRAME_OPTIONS=SAMEORIGIN

# 静态文件和媒体文件
STATIC_URL=/static/
STATIC_ROOT=/var/www/mdm/static/
MEDIA_URL=/media/
MEDIA_ROOT=/var/www/mdm/media/

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/mdm/django.log

# Celery配置
CELERY_BROKER_URL=redis://:redis_prod_2025@redis:6379/3
CELERY_RESULT_BACKEND=redis://:redis_prod_2025@redis:6379/4

# 监控配置 (验收环境禁用外部服务)
SENTRY_DSN=
PROMETHEUS_METRICS_ENABLED=False

# 应用配置
ENVIRONMENT=production
VERSION=1.0.0
COMPANY_NAME=您的公司名称
SYSTEM_NAME=设备管理系统

# 文件上传限制
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE=5242880  # 5MB

# 缓存配置
CACHE_TTL=3600  # 1小时
SESSION_CACHE_ALIAS=default

# API配置
API_THROTTLE_RATE=1000/hour
API_PAGE_SIZE=20
API_MAX_PAGE_SIZE=100

# 备份配置
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
