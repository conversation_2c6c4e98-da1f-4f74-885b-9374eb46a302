# 设备管理系统生产环境 Docker Compose 配置 (精简版)
# 适用于预发布环境和快速验收测试
# 包含核心服务，去除监控和复杂配置

services:
  # PostgreSQL 数据库
  db:
    image: postgres:15-alpine
    container_name: mdm_prod_lite_db
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_prod_lite_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: mdm_prod_lite_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_prod_lite_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Django 后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    container_name: mdm_prod_lite_backend
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
    volumes:
      - static_prod_lite_volume:/app/staticfiles
      - media_prod_lite_volume:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Vue.js 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      target: production
      args:
        - NODE_ENV=production
        - VITE_API_BASE_URL=http://localhost:8000/api
    container_name: mdm_prod_lite_frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Nginx 反向代理 (简化配置)
  nginx:
    image: nginx:alpine
    container_name: mdm_prod_lite_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d/prod.conf:/etc/nginx/conf.d/default.conf:ro
      - static_prod_lite_volume:/var/www/static:ro
      - media_prod_lite_volume:/var/www/media:ro
    depends_on:
      - backend
      - frontend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Celery 异步任务处理 (简化配置)
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    container_name: mdm_prod_lite_celery
    command: celery -A config worker -l info --concurrency=2
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

  # Celery Beat 定时任务 (精简配置)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    container_name: mdm_prod_lite_celery_beat
    command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/app/scripts/celery_beat_healthcheck.py"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - mdm_prod_lite_network

volumes:
  postgres_prod_lite_data:
    driver: local
  redis_prod_lite_data:
    driver: local
  static_prod_lite_volume:
    driver: local
  media_prod_lite_volume:
    driver: local

networks:
  mdm_prod_lite_network:
    driver: bridge
    name: mdm_prod_lite_network
