import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, type User, type LoginData } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(localStorage.getItem('access_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => {
    if (!accessToken.value || !user.value) return false

    // 检查token是否过期（简单检查）
    try {
      const tokenPayload = JSON.parse(atob(accessToken.value.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return tokenPayload.exp > currentTime
    } catch (error) {
      console.warn('Token解析失败:', error)
      return false
    }
  })
  const isAdmin = computed(() => user.value?.role === 'super_admin' || user.value?.role === 'device_admin')
  const isDeviceOwner = computed(() => {
    const role = user.value?.role
    return role === 'super_admin' || role === 'device_admin' || role === 'device_owner'
  })

  // 设置认证信息
  const setAuth = (tokens: { access: string; refresh: string }, userData: User) => {
    accessToken.value = tokens.access
    refreshToken.value = tokens.refresh
    user.value = userData

    localStorage.setItem('access_token', tokens.access)
    localStorage.setItem('refresh_token', tokens.refresh)
  }

  // 清除认证信息
  const clearAuth = () => {
    accessToken.value = null
    refreshToken.value = null
    user.value = null

    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  }

  // 登录
  const login = async (loginData: LoginData) => {
    try {
      isLoading.value = true
      const response = await authAPI.login(loginData)
      
      setAuth(
        { access: response.access, refresh: response.refresh },
        response.user
      )
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      ElMessage.error('登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 设置登出状态标记，避免显示401错误消息
      sessionStorage.setItem('logging_out', 'true')

      // 立即清除认证信息，阻止新的API请求
      const tempRefreshToken = refreshToken.value
      clearAuth()

      // 只有在有有效token的情况下才调用后端登出API
      if (tempRefreshToken) {
        try {
          await authAPI.logout(tempRefreshToken)
        } catch (apiError) {
          // 如果后端登出失败，记录错误但不阻止前端登出
          console.warn('后端登出API调用失败，但继续执行前端登出:', apiError)
        }
      }

      ElMessage.success('已退出登录')
    } catch (error) {
      console.error('登出过程中发生错误:', error)
      // 确保即使出错也清除认证信息
      clearAuth()
      ElMessage.success('已退出登录')
    } finally {
      // 清除登出状态标记
      sessionStorage.removeItem('logging_out')
    }
  }

  // 获取用户信息
  const fetchUserProfile = async () => {
    try {
      const userData = await authAPI.getProfile()
      user.value = userData
      return userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，清除认证信息
      clearAuth()
      throw error
    }
  }

  // 更新用户信息
  const updateProfile = async (data: Partial<User>) => {
    try {
      const updatedUser = await authAPI.updateProfile(data)
      user.value = updatedUser
      ElMessage.success('个人信息更新成功')
      return updatedUser
    } catch (error) {
      ElMessage.error('更新个人信息失败')
      throw error
    }
  }

  // 修改密码
  const changePassword = async (data: {
    old_password: string
    new_password: string
    new_password_confirm: string
  }) => {
    try {
      await authAPI.changePassword(data)
      ElMessage.success('密码修改成功')
    } catch (error) {
      ElMessage.error('密码修改失败')
      throw error
    }
  }

  // 刷新token
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }
      
      const response = await authAPI.refreshToken(refreshToken.value)
      accessToken.value = response.access
      localStorage.setItem('access_token', response.access)
      
      return response.access
    } catch (error) {
      console.error('刷新token失败:', error)
      clearAuth()
      throw error
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (accessToken.value) {
      try {
        await fetchUserProfile()
      } catch (error) {
        console.error('初始化认证状态失败:', error)
        clearAuth()
      }
    }
  }

  return {
    // 状态
    user,
    accessToken,
    refreshToken,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isDeviceOwner,
    
    // 方法
    setAuth,
    clearAuth,
    login,
    logout,
    fetchUserProfile,
    updateProfile,
    changePassword,
    refreshAccessToken,
    initAuth
  }
})
