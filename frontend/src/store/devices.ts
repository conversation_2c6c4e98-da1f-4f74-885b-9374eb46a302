import { defineStore } from 'pinia'
import { ref } from 'vue'
import { deviceAPI, type Device, type DeviceCategory } from '@/api/devices'
import { ElMessage } from 'element-plus'

export const useDeviceStore = defineStore('devices', () => {
  // 状态
  const devices = ref<Device[]>([])
  const categories = ref<DeviceCategory[]>([])
  const currentDevice = ref<Device | null>(null)
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0
  })

  // 获取设备列表
  const fetchDevices = async (params?: any) => {
    try {
      isLoading.value = true
      const response = await deviceAPI.getDeviceList(params)
      
      devices.value = response.results
      pagination.value.total = response.count
      
      return response
    } catch (error) {
      ElMessage.error('获取设备列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取设备详情
  const fetchDeviceDetail = async (id: string) => {
    try {
      isLoading.value = true
      const device = await deviceAPI.getDeviceDetail(id)
      currentDevice.value = device
      return device
    } catch (error) {
      ElMessage.error('获取设备详情失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 创建设备
  const createDevice = async (data: Partial<Device>) => {
    try {
      console.log('🔧 Store: 开始创建设备')
      console.log('🔧 Store: 设备数据:', data)

      const device = await deviceAPI.createDevice(data)
      console.log('✅ Store: 设备创建成功:', device)

      devices.value.unshift(device)
      ElMessage.success('设备创建成功')
      return device
    } catch (error) {
      console.error('❌ Store: 设备创建失败:', error)
      ElMessage.error('设备创建失败')
      throw error
    }
  }

  // 更新设备
  const updateDevice = async (id: string, data: Partial<Device>) => {
    try {
      const device = await deviceAPI.updateDevice(id, data)
      
      // 更新列表中的设备
      const index = devices.value.findIndex(d => d.id === id)
      if (index !== -1) {
        devices.value[index] = device
      }
      
      // 更新当前设备
      if (currentDevice.value?.id === id) {
        currentDevice.value = device
      }
      
      ElMessage.success('设备更新成功')
      return device
    } catch (error) {
      ElMessage.error('设备更新失败')
      throw error
    }
  }

  // 删除设备
  const deleteDevice = async (id: string) => {
    try {
      await deviceAPI.deleteDevice(id)
      
      // 从列表中移除设备
      devices.value = devices.value.filter(d => d.id !== id)
      
      // 清除当前设备
      if (currentDevice.value?.id === id) {
        currentDevice.value = null
      }
      
      ElMessage.success('设备删除成功')
    } catch (error) {
      ElMessage.error('设备删除失败')
      throw error
    }
  }

  // 变更设备状态
  const changeDeviceStatus = async (id: string, newStatus: string, reason?: string) => {
    try {
      const response = await deviceAPI.changeDeviceStatus(id, {
        new_status: newStatus,
        reason
      })
      
      // 更新设备状态
      const device = devices.value.find(d => d.id === id)
      if (device) {
        device.status = newStatus
        device.status_display = response.new_status
      }
      
      if (currentDevice.value?.id === id) {
        currentDevice.value.status = newStatus
      }
      
      ElMessage.success(response.message)
      return response
    } catch (error) {
      ElMessage.error('设备状态变更失败')
      throw error
    }
  }

  // 分配设备归属者
  const assignDeviceOwner = async (id: string, ownerId: string, reason?: string) => {
    try {
      const response = await deviceAPI.assignDeviceOwner(id, {
        owner_id: ownerId,
        reason
      })
      
      // 更新设备归属者
      const device = devices.value.find(d => d.id === id)
      if (device) {
        device.owner = ownerId
      }
      
      if (currentDevice.value?.id === id) {
        currentDevice.value.owner = ownerId
      }
      
      ElMessage.success(response.message)
      return response
    } catch (error) {
      ElMessage.error('分配设备归属者失败')
      throw error
    }
  }

  // 批量操作设备
  const batchOperation = async (data: {
    device_ids: string[]
    operation: 'assign_owner' | 'change_status' | 'delete'
    owner_id?: string
    new_status?: string
    reason?: string
  }) => {
    try {
      const response = await deviceAPI.batchOperation(data)
      
      // 根据操作类型更新本地状态
      if (data.operation === 'delete') {
        devices.value = devices.value.filter(d => !data.device_ids.includes(d.id))
      } else {
        // 重新获取设备列表以确保数据一致性
        await fetchDevices()
      }
      
      ElMessage.success(response.message)
      return response
    } catch (error) {
      ElMessage.error('批量操作失败')
      throw error
    }
  }

  // 获取设备分类列表
  const fetchCategories = async (params?: any) => {
    try {
      const response = await deviceAPI.getCategoryList(params)
      categories.value = response.results
      return response
    } catch (error) {
      ElMessage.error('获取设备分类失败')
      throw error
    }
  }

  // 创建设备分类
  const createCategory = async (data: Partial<DeviceCategory>) => {
    try {
      const category = await deviceAPI.createCategory(data)
      categories.value.push(category)
      ElMessage.success('设备分类创建成功')
      return category
    } catch (error) {
      ElMessage.error('设备分类创建失败')
      throw error
    }
  }

  // 更新设备分类
  const updateCategory = async (id: string, data: Partial<DeviceCategory>) => {
    try {
      const category = await deviceAPI.updateCategory(id, data)
      
      const index = categories.value.findIndex(c => c.id === id)
      if (index !== -1) {
        categories.value[index] = category
      }
      
      ElMessage.success('设备分类更新成功')
      return category
    } catch (error) {
      ElMessage.error('设备分类更新失败')
      throw error
    }
  }

  // 删除设备分类
  const deleteCategory = async (id: string) => {
    try {
      await deviceAPI.deleteCategory(id)
      categories.value = categories.value.filter(c => c.id !== id)
      ElMessage.success('设备分类删除成功')
    } catch (error) {
      ElMessage.error('设备分类删除失败')
      throw error
    }
  }

  // 批量导入设备
  const batchImportDevices = async (deviceData: any[]) => {
    try {
      console.log('🔧 批量导入设备:', deviceData.length, '条')

      const response = await deviceAPI.batchImportDevices(deviceData)
      console.log('✅ 批量导入成功:', response)

      // 重新加载设备列表
      await fetchDevices()

      ElMessage.success(`成功导入 ${deviceData.length} 条设备数据`)
      return response
    } catch (error) {
      console.error('❌ 批量导入失败:', error)
      ElMessage.error('批量导入失败')
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    devices.value = []
    categories.value = []
    currentDevice.value = null
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }

  return {
    // 状态
    devices,
    categories,
    currentDevice,
    isLoading,
    pagination,
    
    // 方法
    fetchDevices,
    fetchDeviceDetail,
    createDevice,
    updateDevice,
    deleteDevice,
    changeDeviceStatus,
    assignDeviceOwner,
    batchOperation,
    batchImportDevices,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    resetState
  }
})
