import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { authAPI } from '@/api/auth'
import type { User } from '@/api/auth'

export const useUserStore = defineStore('users', () => {
  // 状态
  const users = ref<User[]>([])
  const currentUser = ref<User | null>(null)
  const isLoading = ref(false)
  const total = ref(0)

  // 计算属性
  const userCount = computed(() => users.value.length)
  const activeUserCount = computed(() => users.value.filter(u => u.is_active).length)
  const inactiveUserCount = computed(() => users.value.filter(u => !u.is_active).length)

  // 获取用户列表
  const fetchUsers = async (params?: any) => {
    try {
      isLoading.value = true
      console.log('🔍 获取用户列表:', params)
      
      const response = await authAPI.getUserList(params)
      console.log('✅ 用户列表响应:', response)
      
      users.value = response.results
      total.value = response.count
      
      return response
    } catch (error) {
      console.error('❌ 获取用户列表失败:', error)
      ElMessage.error('获取用户列表失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户详情
  const fetchUserDetail = async (id: string) => {
    try {
      isLoading.value = true
      console.log('🔍 获取用户详情:', id)
      
      const user = await authAPI.getUserDetail(id)
      console.log('✅ 用户详情:', user)
      
      currentUser.value = user
      return user
    } catch (error) {
      console.error('❌ 获取用户详情失败:', error)
      ElMessage.error('获取用户详情失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 创建用户
  const createUser = async (userData: any) => {
    try {
      console.log('🔧 创建用户:', userData)
      
      const user = await authAPI.register(userData)
      console.log('✅ 用户创建成功:', user)
      
      // 添加到列表开头
      users.value.unshift(user)
      total.value += 1
      
      ElMessage.success('用户创建成功')
      return user
    } catch (error) {
      console.error('❌ 用户创建失败:', error)
      ElMessage.error('用户创建失败')
      throw error
    }
  }

  // 更新用户
  const updateUser = async (id: string, userData: any) => {
    try {
      console.log('🔧 更新用户:', id, userData)
      
      const user = await authAPI.updateUser(id, userData)
      console.log('✅ 用户更新成功:', user)
      
      // 更新列表中的用户
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index] = user
      }
      
      // 更新当前用户
      if (currentUser.value?.id === id) {
        currentUser.value = user
      }
      
      ElMessage.success('用户更新成功')
      return user
    } catch (error) {
      console.error('❌ 用户更新失败:', error)
      ElMessage.error('用户更新失败')
      throw error
    }
  }

  // 禁用/启用用户
  const toggleUserStatus = async (id: string, isActive: boolean) => {
    try {
      console.log('🔧 切换用户状态:', id, isActive ? '启用' : '禁用')
      
      const user = await authAPI.updateUser(id, { is_active: isActive })
      console.log('✅ 用户状态更新成功:', user)
      
      // 更新列表中的用户状态
      const index = users.value.findIndex(u => u.id === id)
      if (index !== -1) {
        users.value[index].is_active = isActive
      }
      
      ElMessage.success(`用户${isActive ? '启用' : '禁用'}成功`)
      return user
    } catch (error) {
      console.error('❌ 用户状态更新失败:', error)
      ElMessage.error(`用户${isActive ? '启用' : '禁用'}失败`)
      throw error
    }
  }

  // 删除用户（软删除）
  const deleteUser = async (id: string) => {
    try {
      console.log('🗑️ 删除用户:', id)
      
      await authAPI.deleteUser(id)
      console.log('✅ 用户删除成功')
      
      // 从列表中移除用户
      users.value = users.value.filter(u => u.id !== id)
      total.value -= 1
      
      // 清除当前用户
      if (currentUser.value?.id === id) {
        currentUser.value = null
      }
      
      ElMessage.success('用户删除成功')
    } catch (error) {
      console.error('❌ 用户删除失败:', error)
      ElMessage.error('用户删除失败')
      throw error
    }
  }

  // 重置密码
  const resetUserPassword = async (id: string, newPassword: string) => {
    try {
      console.log('🔧 重置用户密码:', id)
      
      const result = await authAPI.resetPassword(id, newPassword)
      console.log('✅ 密码重置成功')
      
      ElMessage.success('密码重置成功')
      return result
    } catch (error) {
      console.error('❌ 密码重置失败:', error)
      ElMessage.error('密码重置失败')
      throw error
    }
  }

  // 清空状态
  const clearUsers = () => {
    users.value = []
    currentUser.value = null
    total.value = 0
  }

  // 根据角色获取用户
  const getUsersByRole = (role: string) => {
    return users.value.filter(user => user.role === role)
  }

  // 根据部门获取用户
  const getUsersByDepartment = (department: string) => {
    return users.value.filter(user => user.department === department)
  }

  // 搜索用户
  const searchUsers = (keyword: string) => {
    if (!keyword) return users.value
    
    const lowerKeyword = keyword.toLowerCase()
    return users.value.filter(user => 
      user.username.toLowerCase().includes(lowerKeyword) ||
      user.email.toLowerCase().includes(lowerKeyword) ||
      user.first_name?.toLowerCase().includes(lowerKeyword) ||
      user.last_name?.toLowerCase().includes(lowerKeyword) ||
      user.department?.toLowerCase().includes(lowerKeyword) ||
      user.employee_id?.toLowerCase().includes(lowerKeyword)
    )
  }

  return {
    // 状态
    users,
    currentUser,
    isLoading,
    total,
    
    // 计算属性
    userCount,
    activeUserCount,
    inactiveUserCount,
    
    // 方法
    fetchUsers,
    fetchUserDetail,
    createUser,
    updateUser,
    toggleUserStatus,
    deleteUser,
    resetUserPassword,
    clearUsers,
    getUsersByRole,
    getUsersByDepartment,
    searchUsers
  }
})
