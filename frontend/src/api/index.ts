import axios from 'axios'
import { ElMessage } from 'element-plus'

// 请求队列管理
class RequestQueue {
  private queue = new Map<string, Promise<any>>()

  // 防止重复请求
  dedupe<T>(key: string, request: () => Promise<T>): Promise<T> {
    if (this.queue.has(key)) {
      return this.queue.get(key)!
    }

    const promise = request().finally(() => {
      this.queue.delete(key)
    })

    this.queue.set(key, promise)
    return promise
  }

  // 取消所有请求
  cancelAll() {
    this.queue.clear()
  }
}

const requestQueue = new RequestQueue()

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 15000, // 增加超时时间
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳用于性能监控
    config.metadata = { startTime: Date.now() }

    // 添加请求ID用于去重
    const requestId = `${config.method}_${config.url}_${JSON.stringify(config.params || {})}`
    config.metadata.requestId = requestId

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 性能监控
    const config = response.config as any
    if (config.metadata?.startTime) {
      const duration = Date.now() - config.metadata.startTime
      if (duration > 2000) {
        console.warn(`慢请求警告: ${config.url} 耗时 ${duration}ms`)
      }
    }

    return response.data
  },
  (error) => {
    // 性能监控
    const config = error.config as any
    if (config?.metadata?.startTime) {
      const duration = Date.now() - config.metadata.startTime
      console.error(`请求失败: ${config.url} 耗时 ${duration}ms`)
    }

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')

          // 避免在登录页面重复跳转
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }

          // 如果用户正在登出过程中，不显示错误消息
          const isLoggingOut = sessionStorage.getItem('logging_out') === 'true'
          if (!isLoggingOut) {
            ElMessage.error('登录已过期，请重新登录')
          }
          break

        case 403:
          ElMessage.error('权限不足，请联系管理员')
          break

        case 404:
          ElMessage.error('请求的资源不存在')
          break

        case 400:
          // 表单验证错误 - 使用统一的错误处理
          // 这里不处理，让具体的组件处理
          break

        case 422:
          // 表单验证错误
          if (data?.errors) {
            const firstError = Object.values(data.errors)[0]
            ElMessage.error(Array.isArray(firstError) ? firstError[0] : firstError)
          } else {
            ElMessage.error(data?.detail || '数据验证失败')
          }
          break

        case 429:
          ElMessage.error('请求过于频繁，请稍后重试')
          break

        case 500:
        case 502:
        case 503:
        case 504:
          ElMessage.error('服务器错误，请稍后重试')
          break

        default:
          // 优先显示后端返回的错误信息
          const errorMessage =
            data?.detail ||
            data?.message ||
            data?.error ||
            '请求失败'
          ElMessage.error(errorMessage)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }

    return Promise.reject(error)
  }
)

// 优化的API方法
export const optimizedApi = {
  // 带缓存的GET请求
  getCached: <T = any>(url: string, config?: any, cacheTime = 5 * 60 * 1000): Promise<T> => {
    const cacheKey = `${url}_${JSON.stringify(config?.params || {})}`

    return requestQueue.dedupe(cacheKey, async () => {
      // 这里可以添加缓存逻辑
      return api.get(url, config)
    })
  },

  // 防抖的POST请求
  postDebounced: <T = any>(url: string, data?: any, config?: any): Promise<T> => {
    const requestKey = `${url}_${JSON.stringify(data)}`

    return requestQueue.dedupe(requestKey, () => {
      return api.post(url, data, config)
    })
  },

  // 批量请求
  batchRequest: async <T = any>(requests: Array<() => Promise<T>>): Promise<T[]> => {
    const batchSize = 5 // 每批最多5个请求
    const results: T[] = []

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize)
      const batchResults = await Promise.all(batch.map(req => req()))
      results.push(...batchResults)

      // 批次间延迟，避免服务器压力过大
      if (i + batchSize < requests.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    return results
  },

  // 重试请求
  retryRequest: async <T = any>(
    requestFn: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> => {
    let lastError: any

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error

        if (i < maxRetries) {
          console.warn(`请求失败，${delay}ms后重试 (${i + 1}/${maxRetries})`)
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }

    throw lastError
  }
}

export { requestQueue }
export default api
