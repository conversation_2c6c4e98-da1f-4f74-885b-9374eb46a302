import api from './index'

export interface Device {
  id: string
  name: string
  model: string
  serial_number: string
  brand: string
  device_number: string
  cpu?: string
  gpu?: string
  memory?: string
  storage?: string
  resolution?: string
  screen_size?: string
  special_screen?: string
  os?: string
  os_version?: string
  status: string
  status_display: string
  owner?: string
  owner_name?: string
  current_user?: string
  current_user_name?: string
  category?: string
  category_name?: string
  purchase_price?: number
  purchase_date?: string
  warranty_period?: string
  special_notes?: string
  image?: string
  qr_code?: string
  is_deleted: boolean
  created_at: string
  updated_at: string
}

export interface DeviceCategory {
  id: string
  name: string
  description: string
  parent?: string
  created_at: string
  updated_at: string
}

export interface DeviceStatusLog {
  id: string
  device: string
  device_name: string
  old_status: string
  old_status_display: string
  new_status: string
  new_status_display: string
  operator: string
  operator_name: string
  reason: string
  created_at: string
}

// 设备管理API
export const deviceAPI = {
  // 获取设备列表
  getDeviceList: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: Device[]
  }> => {
    return api.get('/devices/', { params })
  },

  // 获取设备详情
  getDeviceDetail: (id: string): Promise<Device> => {
    return api.get(`/devices/${id}/`)
  },

  // 创建设备
  createDevice: (data: Partial<Device>): Promise<Device> => {
    console.log('🔧 API: 发送创建设备请求')
    console.log('🔧 API: 请求数据:', JSON.stringify(data, null, 2))
    console.log('🔧 API: 请求URL:', '/devices/')
    console.log('🔧 API: 请求头:', {
      'Authorization': localStorage.getItem('access_token') ? 'Bearer ***' : '未设置',
      'Content-Type': 'application/json'
    })

    return api.post('/devices/', data).then(response => {
      console.log('✅ API: 创建设备响应:', response)
      return response
    }).catch(error => {
      console.error('❌ API: 创建设备失败:', error)
      console.error('❌ API: 错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      })
      throw error
    })
  },

  // 更新设备
  updateDevice: (id: string, data: Partial<Device>): Promise<Device> => {
    return api.put(`/devices/${id}/`, data)
  },

  // 删除设备
  deleteDevice: (id: string): Promise<{ message: string }> => {
    return api.delete(`/devices/${id}/`)
  },

  // 变更设备状态
  changeDeviceStatus: (id: string, data: {
    status: string
    reason?: string
  }): Promise<{
    message: string
    old_status: string
    new_status: string
    device_id: string
  }> => {
    return api.post(`/devices/${id}/change-status/`, data)
  },

  // 设备入库
  warehouseInDevice: (id: string, data?: {
    reason?: string
  }): Promise<{
    message: string
    old_status: string
    new_status: string
    device_id: string
  }> => {
    return api.post(`/devices/${id}/warehouse-in/`, data || {})
  },

  // 分配设备归属者
  assignDeviceOwner: (id: string, data: {
    owner_id: string
    reason?: string
  }): Promise<{
    message: string
    old_owner: string | null
    new_owner: string
  }> => {
    return api.post(`/devices/${id}/assign-owner/`, data)
  },

  // 批量操作设备
  batchOperation: (data: {
    device_ids: string[]
    operation: 'assign_owner' | 'change_status' | 'delete'
    owner_id?: string
    new_status?: string
    reason?: string
  }): Promise<{
    message: string
    results: Array<{
      device_id: string
      device_name: string
      status: string
    }>
  }> => {
    return api.post('/devices/batch-operation/', data)
  },

  // 获取设备状态日志
  getDeviceStatusLogs: (deviceId?: string, params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: DeviceStatusLog[]
  }> => {
    const url = deviceId ? `/devices/${deviceId}/status-logs/` : '/devices/status-logs/'
    return api.get(url, { params })
  },

  // 获取设备分类列表
  getCategoryList: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: DeviceCategory[]
  }> => {
    return api.get('/devices/categories/', { params })
  },

  // 创建设备分类
  createCategory: (data: Partial<DeviceCategory>): Promise<DeviceCategory> => {
    return api.post('/devices/categories/', data)
  },

  // 更新设备分类
  updateCategory: (id: string, data: Partial<DeviceCategory>): Promise<DeviceCategory> => {
    return api.put(`/devices/categories/${id}/`, data)
  },

  // 删除设备分类
  deleteCategory: (id: string): Promise<void> => {
    return api.delete(`/devices/categories/${id}/`)
  },

  // 批量导入设备
  batchImportDevices: (deviceData: any[]): Promise<{ message: string, imported_count: number }> => {
    return api.post('/devices/batch-import/', { devices: deviceData })
  },

  // 获取设备配置选项
  getConfigOptions: (): Promise<{
    brands: { value: string, label: string }[]
    categories: { value: string, label: string }[]
    special_screens: { value: string, label: string }[]
    operating_systems: { value: string, label: string }[]
    statuses: { value: string, label: string }[]
  }> => {
    return api.get('/devices/config-options/')
  }
}
