import api from './index'

export interface LoanApplication {
  id: string
  device: string
  device_info: {
    id: string
    name: string
    model: string
    serial_number: string
    device_number: string
  }
  borrower: string
  borrower_info: {
    id: string
    username: string
    email: string
    department: string
  }
  reason: string
  expected_start_date: string
  expected_end_date: string
  priority: string
  priority_display: string
  status: string
  status_display: string
  approver?: string
  approver_info?: {
    id: string
    username: string
    email: string
  }
  approved_at?: string
  approval_notes?: string
  actual_start_date?: string
  actual_end_date?: string
  return_condition?: string
  damage_report?: string
  is_overdue: boolean
  duration_days: number
  created_at: string
  updated_at: string
}

export interface LoanExtension {
  id: string
  loan_application: string
  loan_info: {
    id: string
    device_name: string
    borrower_name: string
  }
  original_end_date: string
  new_end_date: string
  reason: string
  status: string
  status_display: string
  approver?: string
  approver_info?: {
    id: string
    username: string
  }
  approved_at?: string
  approval_notes?: string
  created_at: string
}

export interface LoanHistory {
  id: string
  device: string
  device_info: {
    id: string
    name: string
    model: string
    device_number: string
  }
  borrower: string
  borrower_info: {
    id: string
    username: string
    department: string
  }
  start_date: string
  end_date?: string
  expected_end_date: string
  reason: string
  return_condition?: string
  damage_report?: string
  is_overdue: boolean
  overdue_days: number
  duration_days: number
  created_at: string
}

export interface LoanStatistics {
  total_applications: number
  pending_applications: number
  approved_applications: number
  active_loans: number
  overdue_loans: number
  total_devices: number
  available_devices: number
  borrowed_devices: number
  user_loan_count: number
  user_overdue_count: number
  loans_this_month: number
  loans_this_week: number
}

// 借用管理API
export const loanAPI = {
  // 获取借用申请列表
  getApplicationList: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanApplication[]
  }> => {
    return api.get('/loans/applications/', { params })
  },

  // 获取借用申请详情
  getApplicationDetail: (id: string): Promise<LoanApplication> => {
    return api.get(`/loans/applications/${id}/`)
  },

  // 创建借用申请
  createApplication: (data: {
    device: string
    reason: string
    expected_start_date: string
    expected_end_date: string
    priority?: string
  }): Promise<LoanApplication> => {
    return api.post('/loans/applications/', data)
  },

  // 更新借用申请
  updateApplication: (id: string, data: Partial<LoanApplication>): Promise<LoanApplication> => {
    return api.put(`/loans/applications/${id}/`, data)
  },

  // 取消借用申请
  cancelApplication: (id: string): Promise<{ message: string }> => {
    return api.delete(`/loans/applications/${id}/`)
  },

  // 审批借用申请
  approveApplication: (id: string, data: {
    action: 'approve' | 'reject'
    notes?: string
  }): Promise<{
    message: string
    status: string
  }> => {
    return api.post(`/loans/applications/${id}/approve/`, data)
  },

  // 开始借用
  startLoan: (id: string, data?: {
    notes?: string
  }): Promise<{
    message: string
    actual_start_date: string
  }> => {
    return api.post(`/loans/applications/${id}/start/`, data)
  },

  // 归还设备
  returnDevice: (id: string, data?: {
    condition?: string
    damage_report?: string
  }): Promise<{
    message: string
    actual_end_date: string
  }> => {
    return api.post(`/loans/applications/${id}/return/`, data)
  },

  // 获取我的借用列表
  getMyLoans: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanApplication[]
  }> => {
    return api.get('/loans/my-loans/', { params })
  },

  // 获取我的申请列表
  getMyApplications: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanApplication[]
  }> => {
    return api.get('/loans/my-applications/', { params })
  },

  // 获取待审批申请列表
  getPendingApprovals: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanApplication[]
  }> => {
    return api.get('/loans/pending-approvals/', { params })
  },

  // 获取借用延期申请列表
  getExtensionList: (loanId: string, params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanExtension[]
  }> => {
    return api.get(`/loans/applications/${loanId}/extensions/`, { params })
  },

  // 创建借用延期申请
  createExtension: (loanId: string, data: {
    new_end_date: string
    reason: string
  }): Promise<LoanExtension> => {
    return api.post(`/loans/applications/${loanId}/extensions/`, data)
  },

  // 审批延期申请
  approveExtension: (id: string, data: {
    action: 'approve' | 'reject'
    notes?: string
  }): Promise<{ message: string }> => {
    return api.post(`/loans/extensions/${id}/approve/`, data)
  },

  // 获取借用历史
  getLoanHistory: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: LoanHistory[]
  }> => {
    return api.get('/loans/history/', { params })
  },

  // 获取借用统计
  getLoanStatistics: (): Promise<LoanStatistics> => {
    return api.get('/loans/statistics/')
  }
}
