import { request } from './request'

// 仪表盘统计数据接口
export interface DashboardStats {
  device_stats: {
    total: number
    available: number
    borrowed: number
    maintenance: number
    utilization_rate: number
  }
  loan_stats: {
    total: number
    active: number
    pending: number
    overdue: number
    this_month: number
    this_week: number
  }
  user_stats: {
    total: number
    active_borrowers: number
  }
}

// 设备统计数据接口
export interface DeviceStats {
  status_distribution: Array<{
    status: string
    count: number
  }>
  category_distribution: Array<{
    category__name: string
    count: number
    available_count: number
    borrowed_count: number
  }>
  owner_distribution: Array<{
    owner__username: string
    count: number
    available_count: number
    borrowed_count: number
  }>
  utilization_stats: Array<{
    device_name: string
    device_number: string
    utilization_rate: number
  }>
}

// 借用统计数据接口
export interface LoanStats {
  monthly_trend: Array<{
    month: string
    loan_count: number
  }>
  success_rate: number
  average_duration_days: number
  overdue_count: number
  popular_devices: Array<{
    device__name: string
    device__model: string
    loan_count: number
  }>
}

// 用户统计数据接口
export interface UserStats {
  role_distribution: Array<{
    role: string
    count: number
  }>
  active_users_count: number
  user_ranking: Array<{
    borrower__username: string
    borrower__department: string
    loan_count: number
    success_count: number
  }>
  department_stats: Array<{
    borrower__department: string
    loan_count: number
    user_count: number
  }>
}

// 报表API类
export class ReportsAPI {
  // 获取仪表盘统计数据
  static async getDashboardStats(): Promise<DashboardStats> {
    const response = await request.get('/reports/dashboard/')
    return response.data
  }

  // 获取设备统计数据
  static async getDeviceStats(): Promise<DeviceStats> {
    const response = await request.get('/reports/devices/')
    return response.data
  }

  // 获取借用统计数据
  static async getLoanStats(): Promise<LoanStats> {
    const response = await request.get('/reports/loans/')
    return response.data
  }

  // 获取用户统计数据
  static async getUserStats(): Promise<UserStats> {
    const response = await request.get('/reports/users/')
    return response.data
  }

  // 导出报表
  static async exportReport(params: {
    report_type: 'device' | 'loan' | 'user' | 'dashboard'
    start_date?: string
    end_date?: string
    format?: 'excel' | 'pdf'
  }): Promise<Blob> {
    const response = await request.post('/reports/export/', params, {
      responseType: 'blob'
    })
    return response.data
  }
}

// 图表配置工具类
export class ChartUtils {
  // 生成饼图配置
  static generatePieChartOption(title: string, data: Array<{name: string, value: number}>) {
    return {
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }

  // 生成柱状图配置
  static generateBarChartOption(title: string, xAxisData: string[], seriesData: number[]) {
    return {
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: title,
          type: 'bar',
          data: seriesData,
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    }
  }

  // 生成折线图配置
  static generateLineChartOption(title: string, xAxisData: string[], seriesData: number[]) {
    return {
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: title,
          type: 'line',
          data: seriesData,
          smooth: true,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    }
  }

  // 生成环形图配置
  static generateDoughnutChartOption(title: string, data: Array<{name: string, value: number}>) {
    return {
      title: {
        text: title,
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['40%', '70%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }

  // 状态颜色映射
  static getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      'available': '#67C23A',
      'borrowed': '#E6A23C',
      'maintenance': '#F56C6C',
      'in_stock': '#909399',
      'idle': '#C0C4CC',
      'locked': '#F56C6C',
      'lost': '#F56C6C',
      'scrapped': '#909399'
    }
    return colorMap[status] || '#409EFF'
  }

  // 角色颜色映射
  static getRoleColor(role: string): string {
    const colorMap: Record<string, string> = {
      'super_admin': '#F56C6C',
      'device_admin': '#E6A23C',
      'device_owner': '#409EFF',
      'normal_user': '#67C23A'
    }
    return colorMap[role] || '#909399'
  }
}
