import api from './index'

export interface LoginData {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  password_confirm: string
  first_name?: string
  last_name?: string
  phone?: string
  department?: string
  employee_id?: string
}

export interface UserProfile {
  bio: string
  location: string
  website: string
  email_notifications: boolean
  sms_notifications: boolean
  wechat_notifications: boolean
  language: string
  timezone: string
}

export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  phone: string
  role: string
  role_display: string
  department: string
  employee_id: string
  avatar: string
  is_active: boolean
  created_at: string
  updated_at: string
  last_login: string
  last_login_ip: string
  login_count: number
  full_name?: string
  profile: UserProfile
  // 权限相关属性
  is_device_admin?: boolean
  is_device_owner?: boolean
  is_super_admin?: boolean
}

export interface LoginResponse {
  access: string
  refresh: string
  user: User
}

// 用户认证API
export const authAPI = {
  // 用户登录
  login: (data: LoginData): Promise<LoginResponse> => {
    return api.post('/auth/login/', data)
  },

  // 用户注册
  register: (data: RegisterData): Promise<User> => {
    return api.post('/auth/register/', data)
  },

  // 用户登出
  logout: (refreshToken: string): Promise<{ message: string }> => {
    return api.post('/auth/logout/', { refresh: refreshToken })
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<{ access: string }> => {
    return api.post('/auth/token/refresh/', { refresh: refreshToken })
  },

  // 获取用户信息
  getProfile: (): Promise<User> => {
    return api.get('/auth/profile/')
  },

  // 更新用户信息
  updateProfile: (data: Partial<User>): Promise<User> => {
    return api.patch('/auth/update/', data)
  },

  // 修改密码
  changePassword: (data: {
    old_password: string
    new_password: string
    new_password_confirm: string
  }): Promise<{ message: string }> => {
    return api.post('/auth/change-password/', data)
  },

  // 上传头像
  uploadAvatar: (file: File): Promise<{ avatar: string }> => {
    const formData = new FormData()
    formData.append('avatar', file)
    return api.post('/auth/upload-avatar/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取用户列表（管理员）
  getUserList: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: User[]
  }> => {
    return api.get('/auth/', { params })
  },

  // 获取用户详情
  getUserDetail: (id: string): Promise<User> => {
    return api.get(`/auth/${id}/`)
  },

  // 更新用户信息（管理员）
  updateUser: (id: string, data: Partial<User>): Promise<User> => {
    return api.put(`/auth/${id}/`, data)
  },

  // 删除用户（软删除）
  deleteUser: (id: string): Promise<{ message: string }> => {
    return api.delete(`/auth/${id}/`)
  },

  // 重置用户密码（管理员）
  resetPassword: (id: string, newPassword: string): Promise<{ message: string }> => {
    return api.post(`/auth/${id}/reset-password/`, { new_password: newPassword })
  },

  // 获取登录日志
  getLoginLogs: (params?: any): Promise<any> => {
    return api.get('/auth/login-logs/', { params })
  },

  // 获取设备归属者选项（受限用户列表）
  getDeviceOwnerOptions: (params?: any): Promise<{
    count: number
    next: string | null
    previous: string | null
    results: User[]
  }> => {
    return api.get('/auth/device-owner-options/', { params })
  }
}
