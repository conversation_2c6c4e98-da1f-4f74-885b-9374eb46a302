<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>设备管理平台</h1>
        <p>请登录您的账户</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.isLoading"
            @click="handleLogin"
          >
            {{ authStore.isLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>
          还没有账户？<a
            href="#"
            @click="showRegister = true"
          >立即注册</a>
        </p>
      </div>
    </div>
    
    <!-- 注册对话框 -->
    <el-dialog
      v-model="showRegister"
      title="用户注册"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
      >
        <el-form-item
          label="用户名"
          prop="username"
        >
          <el-input v-model="registerForm.username" />
        </el-form-item>
        
        <el-form-item
          label="邮箱"
          prop="email"
        >
          <el-input
            v-model="registerForm.email"
            type="email"
          />
        </el-form-item>
        
        <el-form-item
          label="密码"
          prop="password"
        >
          <el-input
            v-model="registerForm.password"
            type="password"
            show-password
          />
          <div class="password-tips">
            <el-text size="small" type="info">
              密码要求：至少8位，包含字母和数字，避免使用常见密码
            </el-text>
          </div>
        </el-form-item>
        
        <el-form-item
          label="确认密码"
          prop="password_confirm"
        >
          <el-input
            v-model="registerForm.password_confirm"
            type="password"
            show-password
          />
        </el-form-item>
        
        <el-form-item
          label="姓名"
          prop="first_name"
        >
          <el-input v-model="registerForm.first_name" />
        </el-form-item>
        
        <el-form-item
          label="部门"
          prop="department"
        >
          <el-input v-model="registerForm.department" />
        </el-form-item>
        
        <el-form-item
          label="手机号"
          prop="phone"
        >
          <el-input v-model="registerForm.phone" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRegister = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleRegister"
          >注册</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import { authAPI } from '@/api/auth'

const router = useRouter()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref<FormInstance>()
const registerFormRef = ref<FormInstance>()

// 登录表单
const loginForm = reactive({
  username: '',
  password: ''
})

// 注册表单
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  password_confirm: '',
  first_name: '',
  department: '',
  phone: ''
})

// 显示注册对话框
const showRegister = ref(false)

// 登录表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于 8 个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }

        // 检查密码强度
        const hasLetter = /[a-zA-Z]/.test(value)
        const hasNumber = /\d/.test(value)
        const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value)

        if (!hasLetter || !hasNumber) {
          callback(new Error('密码必须包含字母和数字'))
          return
        }

        // 检查是否为常见密码
        const commonPasswords = ['12345678', 'password', '123456789', 'qwerty123', 'abc123456']
        if (commonPasswords.includes(value.toLowerCase())) {
          callback(new Error('密码过于简单，请使用更复杂的密码'))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  password_confirm: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  first_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    await authStore.login(loginForm)
    router.push('/')
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validate()
    await authAPI.register(registerForm)
    ElMessage.success('注册成功，请登录')
    showRegister.value = false

    // 清空注册表单
    Object.keys(registerForm).forEach(key => {
      registerForm[key as keyof typeof registerForm] = ''
    })
  } catch (error: any) {
    console.error('注册失败:', error)

    // 处理后端返回的详细错误信息
    if (error?.response?.data) {
      const errorData = error.response.data

      // 处理密码验证错误
      if (errorData.password) {
        const passwordErrors = Array.isArray(errorData.password)
          ? errorData.password
          : [errorData.password]
        ElMessage.error(`密码要求：${passwordErrors.join('；')}`)
        return
      }

      // 处理其他字段错误
      if (errorData.username) {
        ElMessage.error(`用户名错误：${Array.isArray(errorData.username) ? errorData.username.join('；') : errorData.username}`)
        return
      }

      if (errorData.email) {
        ElMessage.error(`邮箱错误：${Array.isArray(errorData.email) ? errorData.email.join('；') : errorData.email}`)
        return
      }

      // 处理通用错误
      if (errorData.non_field_errors) {
        ElMessage.error(Array.isArray(errorData.non_field_errors) ? errorData.non_field_errors.join('；') : errorData.non_field_errors)
        return
      }

      // 处理详细错误信息
      if (errorData.detail) {
        ElMessage.error(errorData.detail)
        return
      }
    }

    // 默认错误消息
    ElMessage.error('注册失败，请检查输入信息')
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  margin-bottom: 10px;
  font-size: 28px;
}

.login-header p {
  color: #666;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}

.login-footer {
  text-align: center;
}

.login-footer a {
  color: #409eff;
  text-decoration: none;
}

.login-footer a:hover {
  text-decoration: underline;
}

.password-tips {
  margin-top: 5px;
  padding: 5px 0;
}
</style>
