<template>
  <div class="profile-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>个人资料</span>
          <el-button
            v-if="!isEditing"
            type="primary"
            @click="startEdit"
          >
            <el-icon><Edit /></el-icon>
            编辑资料
          </el-button>
          <div v-else>
            <el-button
              type="success"
              :loading="saving"
              @click="saveProfile"
            >
              <el-icon><Check /></el-icon>
              保存
            </el-button>
            <el-button @click="cancelEdit">
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </div>
        </div>
      </template>

      <div class="profile-content">
        <el-row :gutter="20">
          <!-- 左侧头像区域 -->
          <el-col :span="6">
            <div class="avatar-section">
              <el-avatar
                :size="120"
                :src="profileData.avatar"
                class="profile-avatar"
              >
                {{ profileData.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              
              <div v-if="isEditing" class="avatar-upload">
                <el-upload
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                  :on-success="handleAvatarSuccess"
                  action="/api/auth/upload-avatar/"
                  :headers="{ Authorization: `Bearer ${authStore.accessToken}` }"
                >
                  <el-button size="small" type="primary">
                    <el-icon><Upload /></el-icon>
                    更换头像
                  </el-button>
                </el-upload>
              </div>
              
              <div class="user-basic-info">
                <h3>{{ profileData.username }}</h3>
                <el-tag :type="getRoleType(profileData.role)">
                  {{ profileData.role_display }}
                </el-tag>
              </div>
            </div>
          </el-col>

          <!-- 右侧信息区域 -->
          <el-col :span="18">
            <el-form
              ref="profileFormRef"
              :model="profileData"
              :rules="profileRules"
              label-width="120px"
              :disabled="!isEditing"
            >
              <el-tabs v-model="activeTab">
                <!-- 基本信息 -->
                <el-tab-pane label="基本信息" name="basic">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="用户名" prop="username">
                        <el-input
                          v-model="profileData.username"
                          :disabled="true"
                        />
                        <div class="form-tip">用户名不可修改</div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="邮箱地址" prop="email">
                        <el-input
                          v-model="profileData.email"
                          type="email"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="手机号码" prop="phone">
                        <el-input
                          v-model="profileData.phone"
                          placeholder="请输入手机号码"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="员工编号" prop="employee_id">
                        <el-input
                          v-model="profileData.employee_id"
                          placeholder="请输入员工编号"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="部门" prop="department">
                        <el-input
                          v-model="profileData.department"
                          placeholder="请输入所在部门"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="姓名" prop="full_name">
                        <el-input
                          v-model="profileData.full_name"
                          placeholder="请输入真实姓名"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-tab-pane>

                <!-- 个人资料 -->
                <el-tab-pane label="个人资料" name="profile">
                  <el-form-item label="个人简介" prop="profile.bio">
                    <el-input
                      v-model="profileData.profile.bio"
                      type="textarea"
                      :rows="4"
                      placeholder="介绍一下自己..."
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="所在地" prop="profile.location">
                        <el-input
                          v-model="profileData.profile.location"
                          placeholder="请输入所在城市"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="个人网站" prop="profile.website">
                        <el-input
                          v-model="profileData.profile.website"
                          placeholder="https://example.com"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-tab-pane>

                <!-- 账户信息 -->
                <el-tab-pane label="账户信息" name="account">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="用户ID">
                      {{ profileData.id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="用户角色">
                      <el-tag :type="getRoleType(profileData.role)">
                        {{ profileData.role_display }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="账户状态">
                      <el-tag :type="profileData.is_active ? 'success' : 'danger'">
                        {{ profileData.is_active ? '正常' : '已禁用' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="注册时间">
                      {{ formatDateTime(profileData.created_at) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="最后登录">
                      {{ formatDateTime(profileData.last_login) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="登录次数">
                      {{ profileData.login_count || 0 }} 次
                    </el-descriptions-item>
                    <el-descriptions-item label="最后登录IP">
                      {{ profileData.last_login_ip || '未知' }}
                    </el-descriptions-item>
                  </el-descriptions>
                </el-tab-pane>
              </el-tabs>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Check, Close, Upload } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'
import type { User } from '@/api/auth'

const authStore = useAuthStore()

// 响应式数据
const isEditing = ref(false)
const saving = ref(false)
const activeTab = ref('basic')
const profileFormRef = ref()

// 个人资料数据
const profileData = reactive<User>({
  id: '',
  username: '',
  email: '',
  phone: '',
  department: '',
  employee_id: '',
  avatar: '',
  role: 'normal_user',
  role_display: '',
  is_active: true,
  created_at: '',
  updated_at: '',
  last_login: '',
  last_login_ip: '',
  login_count: 0,
  full_name: '',
  profile: {
    bio: '',
    location: '',
    website: '',
    email_notifications: true,
    sms_notifications: false,
    wechat_notifications: true,
    language: 'zh-hans',
    timezone: 'Asia/Shanghai'
  }
})

// 备份原始数据
let originalData: User = {} as User

// 表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  'profile.website': [
    { type: 'url', message: '请输入正确的网址格式', trigger: 'blur' }
  ]
}

// 计算属性
const getRoleType = (role: string) => {
  const roleTypes: Record<string, string> = {
    'super_admin': 'danger',
    'device_admin': 'warning',
    'device_owner': 'primary',
    'normal_user': 'info'
  }
  return roleTypes[role] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 开始编辑
const startEdit = () => {
  originalData = JSON.parse(JSON.stringify(profileData))
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  Object.assign(profileData, originalData)
  isEditing.value = false
}

// 保存个人资料
const saveProfile = async () => {
  try {
    await profileFormRef.value?.validate()
    saving.value = true

    // 准备更新数据
    const updateData = {
      email: profileData.email,
      phone: profileData.phone,
      department: profileData.department,
      employee_id: profileData.employee_id,
      first_name: profileData.full_name?.split(' ')[0] || '',
      last_name: profileData.full_name?.split(' ').slice(1).join(' ') || '',
      profile: {
        bio: profileData.profile.bio,
        location: profileData.profile.location,
        website: profileData.profile.website
      }
    }

    await authStore.updateProfile(updateData)
    isEditing.value = false
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    console.error('保存个人资料失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 头像上传成功
const handleAvatarSuccess = (response: any) => {
  if (response.avatar) {
    profileData.avatar = response.avatar
    ElMessage.success('头像更新成功')
  }
}

// 加载用户资料
const loadProfile = async () => {
  try {
    const userData = await authStore.fetchUserProfile()
    Object.assign(profileData, userData)
    
    // 处理姓名字段
    if (userData.first_name || userData.last_name) {
      profileData.full_name = `${userData.first_name || ''} ${userData.last_name || ''}`.trim()
    }
    
    // 确保profile对象存在
    if (!profileData.profile) {
      profileData.profile = {
        bio: '',
        location: '',
        website: '',
        email_notifications: true,
        sms_notifications: false,
        wechat_notifications: true,
        language: 'zh-hans',
        timezone: 'Asia/Shanghai'
      }
    }
  } catch (error) {
    console.error('加载用户资料失败:', error)
    ElMessage.error('加载用户资料失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProfile()
})
</script>

<style scoped>
.profile-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-content {
  padding: 20px 0;
}

.avatar-section {
  text-align: center;
  padding: 20px;
}

.profile-avatar {
  margin-bottom: 20px;
  border: 3px solid #f0f0f0;
}

.avatar-upload {
  margin: 15px 0;
}

.user-basic-info h3 {
  margin: 10px 0 5px 0;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-descriptions {
  margin-top: 20px;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
