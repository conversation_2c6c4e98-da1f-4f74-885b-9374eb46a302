<template>
  <div class="reports">
    <div class="page-header">
      <h2>数据报表</h2>
      <div class="header-actions">
        <el-button
          type="primary"
          :loading="loading"
          @click="refreshAllData"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 报表选项卡 -->
    <el-tabs
      v-model="activeTab"
      @tab-click="handleTabClick"
    >
      <!-- 设备统计 -->
      <el-tab-pane
        label="设备统计"
        name="device"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <BaseChart
              title="设备状态分布"
              :option="deviceStatusChartOption"
              :loading="loading"
              @refresh="loadDeviceStats"
            />
          </el-col>
          <el-col :span="12">
            <BaseChart
              title="设备分类统计"
              :option="deviceCategoryChartOption"
              :loading="loading"
              @refresh="loadDeviceStats"
            />
          </el-col>
        </el-row>
        
        <el-row
          :gutter="20"
          style="margin-top: 20px;"
        >
          <el-col :span="24">
            <BaseChart
              title="设备利用率排行"
              :option="deviceUtilizationChartOption"
              :loading="loading"
              height="300px"
              @refresh="loadDeviceStats"
            />
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 借用统计 -->
      <el-tab-pane
        label="借用统计"
        name="loan"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <BaseChart
              title="借用趋势分析"
              :option="loanTrendChartOption"
              :loading="loading"
              height="300px"
              @refresh="loadLoanStats"
            />
          </el-col>
        </el-row>
        
        <el-row
          :gutter="20"
          style="margin-top: 20px;"
        >
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>借用统计概览</span>
              </template>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-label">
                    借用成功率
                  </div>
                  <div class="stat-value">
                    {{ loanStats?.success_rate || 0 }}%
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">
                    平均借用时长
                  </div>
                  <div class="stat-value">
                    {{ loanStats?.average_duration_days || 0 }}天
                  </div>
                </div>
                <div class="stat-item">
                  <div class="stat-label">
                    超期数量
                  </div>
                  <div class="stat-value">
                    {{ loanStats?.overdue_count || 0 }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <BaseChart
              title="热门设备排行"
              :option="popularDevicesChartOption"
              :loading="loading"
              @refresh="loadLoanStats"
            />
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 用户统计 -->
      <el-tab-pane
        label="用户统计"
        name="user"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <BaseChart
              title="用户角色分布"
              :option="userRoleChartOption"
              :loading="loading"
              @refresh="loadUserStats"
            />
          </el-col>
          <el-col :span="12">
            <BaseChart
              title="部门借用统计"
              :option="departmentStatsChartOption"
              :loading="loading"
              @refresh="loadUserStats"
            />
          </el-col>
        </el-row>
        
        <el-row
          :gutter="20"
          style="margin-top: 20px;"
        >
          <el-col :span="24">
            <el-card>
              <template #header>
                <span>用户借用排行榜</span>
              </template>
              <el-table
                v-loading="loading"
                :data="userRankingData"
                style="width: 100%"
              >
                <el-table-column
                  prop="borrower__username"
                  label="用户名"
                />
                <el-table-column
                  prop="borrower__department"
                  label="部门"
                />
                <el-table-column
                  prop="loan_count"
                  label="借用次数"
                />
                <el-table-column
                  prop="success_count"
                  label="成功次数"
                />
                <el-table-column label="成功率">
                  <template #default="scope">
                    {{ ((scope.row.success_count / scope.row.loan_count) * 100).toFixed(1) }}%
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Refresh, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseChart from '@/components/charts/BaseChart.vue'
import { 
  ReportsAPI, 
  type DeviceStats, 
  type LoanStats, 
  type UserStats,
  ChartUtils 
} from '@/api/reports'

// 数据状态
const loading = ref(false)
const activeTab = ref('device')
const deviceStats = ref<DeviceStats | null>(null)
const loanStats = ref<LoanStats | null>(null)
const userStats = ref<UserStats | null>(null)

// 设备状态图表配置
const deviceStatusChartOption = computed(() => {
  if (!deviceStats.value) return {}
  
  const data = deviceStats.value.status_distribution.map(item => ({
    name: getStatusDisplayName(item.status),
    value: item.count
  }))
  
  return ChartUtils.generatePieChartOption('设备状态分布', data)
})

// 设备分类图表配置
const deviceCategoryChartOption = computed(() => {
  if (!deviceStats.value) return {}
  
  const data = deviceStats.value.category_distribution.map(item => ({
    name: item.category__name || '未分类',
    value: item.count
  }))
  
  return ChartUtils.generateDoughnutChartOption('设备分类统计', data)
})

// 设备利用率图表配置
const deviceUtilizationChartOption = computed(() => {
  if (!deviceStats.value) return {}
  
  const labels = deviceStats.value.utilization_stats.map(item => item.device_name)
  const data = deviceStats.value.utilization_stats.map(item => item.utilization_rate)
  
  return ChartUtils.generateBarChartOption('设备利用率排行', labels, data)
})

// 借用趋势图表配置
const loanTrendChartOption = computed(() => {
  if (!loanStats.value) return {}
  
  const labels = loanStats.value.monthly_trend.map(item => item.month)
  const data = loanStats.value.monthly_trend.map(item => item.loan_count)
  
  return ChartUtils.generateLineChartOption('借用趋势分析', labels, data)
})

// 热门设备图表配置
const popularDevicesChartOption = computed(() => {
  if (!loanStats.value) return {}
  
  const data = loanStats.value.popular_devices.slice(0, 10).map(item => ({
    name: `${item.device__name} (${item.device__model})`,
    value: item.loan_count
  }))
  
  return ChartUtils.generateBarChartOption('热门设备排行', 
    data.map(item => item.name), 
    data.map(item => item.value)
  )
})

// 用户角色图表配置
const userRoleChartOption = computed(() => {
  if (!userStats.value) return {}
  
  const data = userStats.value.role_distribution.map(item => ({
    name: getRoleDisplayName(item.role),
    value: item.count
  }))
  
  return ChartUtils.generatePieChartOption('用户角色分布', data)
})

// 部门统计图表配置
const departmentStatsChartOption = computed(() => {
  if (!userStats.value) return {}
  
  const labels = userStats.value.department_stats.slice(0, 10).map(item => item.borrower__department || '未知部门')
  const data = userStats.value.department_stats.slice(0, 10).map(item => item.loan_count)
  
  return ChartUtils.generateBarChartOption('部门借用统计', labels, data)
})

// 用户排行数据
const userRankingData = computed(() => {
  return userStats.value?.user_ranking.slice(0, 20) || []
})

// 加载设备统计数据
const loadDeviceStats = async () => {
  try {
    loading.value = true
    deviceStats.value = await ReportsAPI.getDeviceStats()
  } catch (error) {
    console.error('加载设备统计失败:', error)
    ElMessage.error('加载设备统计失败')
  } finally {
    loading.value = false
  }
}

// 加载借用统计数据
const loadLoanStats = async () => {
  try {
    loading.value = true
    loanStats.value = await ReportsAPI.getLoanStats()
  } catch (error) {
    console.error('加载借用统计失败:', error)
    ElMessage.error('加载借用统计失败')
  } finally {
    loading.value = false
  }
}

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    loading.value = true
    userStats.value = await ReportsAPI.getUserStats()
  } catch (error) {
    console.error('加载用户统计失败:', error)
    ElMessage.error('加载用户统计失败')
  } finally {
    loading.value = false
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  await Promise.all([
    loadDeviceStats(),
    loadLoanStats(),
    loadUserStats()
  ])
}

// 导出报表
const exportReport = async () => {
  try {
    const blob = await ReportsAPI.exportReport({
      report_type: activeTab.value as any,
      format: 'excel'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${activeTab.value}_report_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('报表导出成功')
  } catch (error) {
    console.error('导出报表失败:', error)
    ElMessage.error('导出报表失败')
  }
}

// 处理标签页切换
const handleTabClick = (tab: any) => {
  activeTab.value = tab.name
}

// 获取状态显示名称
const getStatusDisplayName = (status: string): string => {
  const statusMap: Record<string, string> = {
    'available': '可借用',
    'borrowed': '借出中',
    'maintenance': '维修中',
    'in_stock': '库存中',
    'idle': '闲置',
    'locked': '锁定',
    'lost': '丢失',
    'scrapped': '报废'
  }
  return statusMap[status] || status
}

// 获取角色显示名称
const getRoleDisplayName = (role: string): string => {
  const roleMap: Record<string, string> = {
    'super_admin': '超级管理员',
    'device_admin': '设备管理员',
    'device_owner': '设备归属者',
    'normal_user': '普通用户'
  }
  return roleMap[role] || role
}

onMounted(() => {
  refreshAllData()
})
</script>

<style scoped>
.reports {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
