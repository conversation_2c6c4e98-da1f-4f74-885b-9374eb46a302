<template>
  <div class="loan-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>借用管理</span>
        </div>
      </template>
      
      <el-tabs
        v-model="activeTab"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          label="我的借用"
          name="my-loans"
        >
          <el-table
            :data="myLoans"
            style="width: 100%"
          >
            <el-table-column
              prop="deviceName"
              label="设备名称"
            />
            <el-table-column
              prop="model"
              label="型号"
            />
            <el-table-column
              prop="borrowDate"
              label="借用时间"
            />
            <el-table-column
              prop="expectedReturn"
              label="预期归还"
            />
            <el-table-column
              prop="status"
              label="状态"
            >
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="150"
            >
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleReturn(scope.row)"
                >
                  申请归还
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane
          label="待审批"
          name="pending-approval"
        >
          <el-table
            :data="pendingApprovals"
            style="width: 100%"
          >
            <el-table-column
              prop="deviceName"
              label="设备名称"
            />
            <el-table-column
              prop="borrower"
              label="申请人"
            />
            <el-table-column
              prop="reason"
              label="借用理由"
            />
            <el-table-column
              prop="requestDate"
              label="申请时间"
            />
            <el-table-column
              prop="expectedDuration"
              label="预期时长"
            />
            <el-table-column
              label="操作"
              width="200"
            >
              <template #default="scope">
                <el-button
                  size="small"
                  type="success"
                  @click="handleApprove(scope.row)"
                >
                  同意
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleReject(scope.row)"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        
        <el-tab-pane
          label="借用历史"
          name="loan-history"
        >
          <el-table
            :data="loanHistory"
            style="width: 100%"
          >
            <el-table-column
              prop="deviceName"
              label="设备名称"
            />
            <el-table-column
              prop="borrower"
              label="借用人"
            />
            <el-table-column
              prop="borrowDate"
              label="借用时间"
            />
            <el-table-column
              prop="returnDate"
              label="归还时间"
            />
            <el-table-column
              prop="duration"
              label="借用时长"
            />
            <el-table-column
              prop="status"
              label="状态"
            >
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const activeTab = ref('my-loans')

// 我的借用
const myLoans = ref([
  {
    id: 1,
    deviceName: 'iPhone 14 Pro',
    model: 'A2894',
    borrowDate: '2025-07-25',
    expectedReturn: '2025-07-30',
    status: '借用中'
  }
])

// 待审批
const pendingApprovals = ref([
  {
    id: 1,
    deviceName: 'MacBook Pro',
    borrower: '王五',
    reason: '项目开发需要',
    requestDate: '2025-07-27',
    expectedDuration: '7天'
  }
])

// 借用历史
const loanHistory = ref([
  {
    id: 1,
    deviceName: 'iPad Air',
    borrower: '张三',
    borrowDate: '2025-07-20',
    returnDate: '2025-07-25',
    duration: '5天',
    status: '已归还'
  }
])

const getStatusType = (status: string) => {
  switch (status) {
    case '借用中':
      return 'warning'
    case '已归还':
      return 'success'
    case '超期':
      return 'danger'
    default:
      return 'info'
  }
}

const handleTabClick = (tab: any) => {
  console.log('切换标签页:', tab.name)
}

const handleReturn = (row: any) => {
  ElMessageBox.confirm(
    `确定要申请归还设备 "${row.deviceName}" 吗？`,
    '确认归还',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('归还申请已提交')
  })
}

const handleApprove = (row: any) => {
  ElMessage.success(`已同意 ${row.borrower} 的借用申请`)
}

const handleReject = (row: any) => {
  ElMessageBox.prompt('请输入拒绝理由', '拒绝申请', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(({ value }) => {
    ElMessage.success(`已拒绝 ${row.borrower} 的借用申请`)
  })
}
</script>

<style scoped>
.loan-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
