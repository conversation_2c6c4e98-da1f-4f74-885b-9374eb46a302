<template>
  <div class="settings-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>账户设置</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" tab-position="left">
        <!-- 密码设置 -->
        <el-tab-pane label="密码设置" name="password">
          <div class="settings-section">
            <h3>修改密码</h3>
            <p class="section-desc">为了账户安全，建议定期更换密码</p>
            
            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="120px"
              style="max-width: 500px;"
            >
              <el-form-item label="当前密码" prop="old_password">
                <el-input
                  v-model="passwordForm.old_password"
                  type="password"
                  show-password
                  placeholder="请输入当前密码"
                />
              </el-form-item>
              
              <el-form-item label="新密码" prop="new_password">
                <el-input
                  v-model="passwordForm.new_password"
                  type="password"
                  show-password
                  placeholder="请输入新密码"
                />
                <div class="password-tips">
                  <el-text size="small" type="info">
                    密码要求：至少8位，包含字母和数字，避免使用常见密码
                  </el-text>
                </div>
              </el-form-item>
              
              <el-form-item label="确认密码" prop="new_password_confirm">
                <el-input
                  v-model="passwordForm.new_password_confirm"
                  type="password"
                  show-password
                  placeholder="请再次输入新密码"
                />
              </el-form-item>
              
              <el-form-item>
                <el-button
                  type="primary"
                  :loading="passwordChanging"
                  @click="changePassword"
                >
                  修改密码
                </el-button>
                <el-button @click="resetPasswordForm">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 通知设置 -->
        <el-tab-pane label="通知设置" name="notifications">
          <div class="settings-section">
            <h3>消息通知</h3>
            <p class="section-desc">选择您希望接收的通知类型</p>
            
            <el-form
              :model="notificationSettings"
              label-width="150px"
              style="max-width: 600px;"
            >
              <el-form-item label="邮件通知">
                <el-switch
                  v-model="notificationSettings.email_notifications"
                  @change="updateNotificationSettings"
                />
                <div class="setting-desc">
                  接收设备借用、归还等重要通知邮件
                </div>
              </el-form-item>
              
              <el-form-item label="短信通知">
                <el-switch
                  v-model="notificationSettings.sms_notifications"
                  @change="updateNotificationSettings"
                />
                <div class="setting-desc">
                  接收紧急通知短信（需要绑定手机号）
                </div>
              </el-form-item>
              
              <el-form-item label="微信通知">
                <el-switch
                  v-model="notificationSettings.wechat_notifications"
                  @change="updateNotificationSettings"
                />
                <div class="setting-desc">
                  通过企业微信接收工作通知
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 偏好设置 -->
        <el-tab-pane label="偏好设置" name="preferences">
          <div class="settings-section">
            <h3>个人偏好</h3>
            <p class="section-desc">自定义您的使用体验</p>
            
            <el-form
              :model="preferenceSettings"
              label-width="120px"
              style="max-width: 500px;"
            >
              <el-form-item label="语言">
                <el-select
                  v-model="preferenceSettings.language"
                  @change="updatePreferenceSettings"
                >
                  <el-option label="简体中文" value="zh-hans" />
                  <el-option label="繁体中文" value="zh-hant" />
                  <el-option label="English" value="en" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="时区">
                <el-select
                  v-model="preferenceSettings.timezone"
                  @change="updatePreferenceSettings"
                  filterable
                >
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                  <el-option label="香港时间 (UTC+8)" value="Asia/Hong_Kong" />
                  <el-option label="台北时间 (UTC+8)" value="Asia/Taipei" />
                  <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                  <el-option label="首尔时间 (UTC+9)" value="Asia/Seoul" />
                  <el-option label="新加坡时间 (UTC+8)" value="Asia/Singapore" />
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <div class="settings-section">
            <h3>账户安全</h3>
            <p class="section-desc">管理您的账户安全设置</p>
            
            <el-descriptions :column="1" border>
              <el-descriptions-item label="登录次数">
                {{ userInfo.login_count || 0 }} 次
              </el-descriptions-item>
              <el-descriptions-item label="最后登录时间">
                {{ formatDateTime(userInfo.last_login) }}
              </el-descriptions-item>
              <el-descriptions-item label="最后登录IP">
                {{ userInfo.last_login_ip || '未知' }}
              </el-descriptions-item>
              <el-descriptions-item label="账户状态">
                <el-tag :type="userInfo.is_active ? 'success' : 'danger'">
                  {{ userInfo.is_active ? '正常' : '已禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>

            <div class="security-actions">
              <h4>安全操作</h4>
              <el-space direction="vertical" size="large">
                <div class="security-item">
                  <div class="security-item-content">
                    <h5>登录日志</h5>
                    <p>查看最近的登录记录</p>
                  </div>
                  <el-button @click="viewLoginLogs">
                    查看日志
                  </el-button>
                </div>
                
                <div class="security-item">
                  <div class="security-item-content">
                    <h5>强制下线</h5>
                    <p>注销所有设备上的登录状态</p>
                  </div>
                  <el-button type="warning" @click="forceLogoutAll">
                    强制下线
                  </el-button>
                </div>
              </el-space>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 登录日志对话框 -->
    <el-dialog
      v-model="loginLogDialogVisible"
      title="登录日志"
      width="800px"
    >
      <el-table
        v-loading="loginLogsLoading"
        :data="loginLogs"
        style="width: 100%"
      >
        <el-table-column prop="login_time" label="登录时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.login_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="150" />
        <el-table-column prop="user_agent" label="设备信息" show-overflow-tooltip />
        <el-table-column prop="success" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.success ? 'success' : 'danger'">
              {{ scope.row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/store/auth'
import { authAPI } from '@/api/auth'

const authStore = useAuthStore()

// 响应式数据
const activeTab = ref('password')
const passwordChanging = ref(false)
const loginLogDialogVisible = ref(false)
const loginLogsLoading = ref(false)
const loginLogs = ref([])

// 密码修改表单
const passwordFormRef = ref()
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  new_password_confirm: ''
})

// 通知设置
const notificationSettings = reactive({
  email_notifications: true,
  sms_notifications: false,
  wechat_notifications: true
})

// 偏好设置
const preferenceSettings = reactive({
  language: 'zh-hans',
  timezone: 'Asia/Shanghai'
})

// 用户信息
const userInfo = reactive({
  login_count: 0,
  last_login: '',
  last_login_ip: '',
  is_active: true
})

// 密码验证规则
const passwordRules = {
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于 8 个字符', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        const hasLetter = /[a-zA-Z]/.test(value)
        const hasNumber = /\d/.test(value)

        if (!hasLetter || !hasNumber) {
          callback(new Error('密码必须包含字母和数字'))
          return
        }

        // 检查常见密码
        const commonPasswords = ['12345678', 'password', '123456789']
        if (commonPasswords.includes(value.toLowerCase())) {
          callback(new Error('密码过于简单，请使用更复杂的密码'))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  new_password_confirm: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value?.validate()
    passwordChanging.value = true

    await authStore.changePassword({
      old_password: passwordForm.old_password,
      new_password: passwordForm.new_password,
      new_password_confirm: passwordForm.new_password_confirm
    })

    resetPasswordForm()
    ElMessage.success('密码修改成功')
  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    passwordChanging.value = false
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  passwordForm.old_password = ''
  passwordForm.new_password = ''
  passwordForm.new_password_confirm = ''
  passwordFormRef.value?.clearValidate()
}

// 更新通知设置
const updateNotificationSettings = async () => {
  try {
    await authStore.updateProfile({
      profile: notificationSettings
    })
    ElMessage.success('通知设置已更新')
  } catch (error) {
    console.error('更新通知设置失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 更新偏好设置
const updatePreferenceSettings = async () => {
  try {
    await authStore.updateProfile({
      profile: preferenceSettings
    })
    ElMessage.success('偏好设置已更新')
  } catch (error) {
    console.error('更新偏好设置失败:', error)
    ElMessage.error('更新失败，请重试')
  }
}

// 查看登录日志
const viewLoginLogs = async () => {
  loginLogDialogVisible.value = true
  loginLogsLoading.value = true
  
  try {
    // TODO: 实现获取登录日志的API
    // const logs = await authAPI.getLoginLogs()
    // loginLogs.value = logs
    
    // 模拟数据
    loginLogs.value = [
      {
        login_time: new Date().toISOString(),
        ip_address: '*************',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        success: true
      }
    ]
  } catch (error) {
    console.error('获取登录日志失败:', error)
    ElMessage.error('获取登录日志失败')
  } finally {
    loginLogsLoading.value = false
  }
}

// 强制下线所有设备
const forceLogoutAll = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将注销您在所有设备上的登录状态，您需要重新登录。确定继续吗？',
      '确认强制下线',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 实现强制下线的API
    ElMessage.success('已强制下线所有设备')
  } catch (error) {
    // 用户取消操作
  }
}

// 加载设置数据
const loadSettings = async () => {
  try {
    const userData = await authStore.fetchUserProfile()
    
    // 更新用户信息
    Object.assign(userInfo, {
      login_count: userData.login_count,
      last_login: userData.last_login,
      last_login_ip: userData.last_login_ip,
      is_active: userData.is_active
    })
    
    // 更新通知设置
    if (userData.profile) {
      Object.assign(notificationSettings, {
        email_notifications: userData.profile.email_notifications,
        sms_notifications: userData.profile.sms_notifications,
        wechat_notifications: userData.profile.wechat_notifications
      })
      
      // 更新偏好设置
      Object.assign(preferenceSettings, {
        language: userData.profile.language,
        timezone: userData.profile.timezone
      })
    }
  } catch (error) {
    console.error('加载设置数据失败:', error)
    ElMessage.error('加载设置数据失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.settings-section {
  padding: 20px;
}

.settings-section h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.section-desc {
  color: #606266;
  margin-bottom: 30px;
  font-size: 14px;
}

.password-tips {
  margin-top: 5px;
}

.setting-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.security-actions {
  margin-top: 30px;
}

.security-actions h4 {
  margin-bottom: 20px;
  color: #303133;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.security-item-content h5 {
  margin: 0 0 5px 0;
  color: #303133;
}

.security-item-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

:deep(.el-tabs--left .el-tabs__content) {
  padding-left: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
