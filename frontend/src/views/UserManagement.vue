<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户账户、权限和状态</p>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="用户名/邮箱/姓名/部门/员工编号"
            style="width: 250px"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="用户角色">
          <el-select
            v-model="searchForm.role"
            placeholder="请选择角色"
            style="width: 150px"
            clearable
          >
            <el-option label="超级管理员" value="super_admin" />
            <el-option label="设备管理员" value="device_admin" />
            <el-option label="设备归属者" value="device_owner" />
            <el-option label="普通用户" value="normal_user" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户状态">
          <el-select
            v-model="searchForm.is_active"
            placeholder="请选择状态"
            style="width: 120px"
            clearable
          >
            <el-option label="启用" value="true" />
            <el-option label="禁用" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="action-card">
      <div class="action-bar">
        <div class="action-left">
          <el-button type="primary" :icon="Plus" @click="handleAdd">
            添加用户
          </el-button>
        </div>
        <div class="action-right">
          <el-text type="info">
            共 {{ pagination.total }} 个用户，
            启用 {{ activeUserCount }} 个，
            禁用 {{ inactiveUserCount }} 个
          </el-text>
        </div>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        :data="filteredUsers"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="first_name" label="姓名" width="100" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="employee_id" label="员工编号" width="120" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login" label="最后登录" width="160">
          <template #default="{ row }">
            {{ formatDate(row.last_login) }}
          </template>
        </el-table-column>
        <el-table-column prop="login_count" label="登录次数" width="100">
          <template #default="{ row }">
            {{ row.login_count || 0 }} 次
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              size="small"
              :type="row.is_active ? 'warning' : 'success'"
              :disabled="isSuperAdmin(row)"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleResetPassword(row)"
            >
              重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <UserForm
      v-model="userFormVisible"
      :user="currentUser"
      :readonly="isReadonlyMode"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import UserForm from '@/components/UserForm.vue'
import { useUserStore } from '@/store/users'
import { useAuthStore } from '@/store/auth'
import type { User } from '@/api/auth'

const userStore = useUserStore()
const authStore = useAuthStore()

// 搜索表单
const searchForm = ref({
  keyword: '',
  role: '',
  is_active: ''
})

// 用户表单相关
const userFormVisible = ref(false)
const currentUser = ref<User | null>(null)
const isReadonlyMode = ref(false)

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载状态
const loading = ref(false)

// 计算属性
const userList = computed(() => userStore.users)
const activeUserCount = computed(() => userStore.activeUserCount)
const inactiveUserCount = computed(() => userStore.inactiveUserCount)

const filteredUsers = computed(() => {
  let users = userList.value
  
  // 关键词搜索
  if (searchForm.value.keyword) {
    users = userStore.searchUsers(searchForm.value.keyword)
  }
  
  // 角色筛选
  if (searchForm.value.role) {
    users = users.filter(user => user.role === searchForm.value.role)
  }
  
  // 状态筛选
  if (searchForm.value.is_active !== '') {
    const isActive = searchForm.value.is_active === 'true'
    users = users.filter(user => user.is_active === isActive)
  }
  
  return users
})

// 角色显示
const getRoleText = (role: string) => {
  const roleMap: Record<string, string> = {
    super_admin: '超级管理员',
    device_admin: '设备管理员',
    device_owner: '设备归属者',
    normal_user: '普通用户'
  }
  return roleMap[role] || role
}

// 角色颜色
const getRoleType = (role: string) => {
  const typeMap: Record<string, string> = {
    super_admin: 'danger',
    device_admin: 'warning',
    device_owner: 'primary',
    normal_user: 'info'
  }
  return typeMap[role] || 'info'
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '从未登录'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 检查是否为超级管理员
const isSuperAdmin = (user: User) => {
  return user.role === 'super_admin'
}

// 搜索功能
const handleSearch = () => {
  console.log('🔍 搜索用户:', searchForm.value)
  loadUsers()
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    keyword: '',
    role: '',
    is_active: ''
  }
  loadUsers()
}

// 添加用户
const handleAdd = () => {
  console.log('🔧 添加用户')
  currentUser.value = null
  isReadonlyMode.value = false
  userFormVisible.value = true
}

// 查看用户
const handleView = async (row: User) => {
  try {
    console.log('🔍 查看用户:', row.username, 'ID:', row.id)
    
    // 获取用户详情
    const user = await userStore.fetchUserDetail(row.id)
    console.log('✅ 用户详情:', user)
    
    currentUser.value = user
    isReadonlyMode.value = true
    userFormVisible.value = true
    
    ElMessage.success(`已加载用户详情: ${row.username}`)
  } catch (error) {
    console.error('❌ 查看用户失败:', error)
    ElMessage.error(`查看用户失败: ${row.username}`)
  }
}

// 编辑用户
const handleEdit = async (row: User) => {
  try {
    console.log('🔧 编辑用户:', row.username, 'ID:', row.id)
    
    // 获取用户详情
    const user = await userStore.fetchUserDetail(row.id)
    console.log('✅ 用户详情:', user)
    
    currentUser.value = user
    isReadonlyMode.value = false
    userFormVisible.value = true
  } catch (error) {
    console.error('❌ 获取用户详情失败:', error)
    ElMessage.error(`获取用户详情失败: ${row.username}`)
  }
}

// 切换用户状态
const handleToggleStatus = async (row: User) => {
  // 检查是否为超级管理员
  if (isSuperAdmin(row)) {
    ElMessage.warning('超级管理员账户不能禁用')
    return
  }

  const action = row.is_active ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 "${row.username}" 吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    console.log('🔧 切换用户状态:', row.username, action)

    await userStore.toggleUserStatus(row.id, !row.is_active)
    console.log('✅ 用户状态切换成功')

    ElMessage.success(`用户 "${row.username}" ${action}成功`)
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消操作')
    } else {
      console.error('❌ 切换用户状态失败:', error)
      ElMessage.error(`${action}用户失败`)
    }
  }
}

// 重置密码
const handleResetPassword = async (row: User) => {
  try {
    const { value: newPassword } = await ElMessageBox.prompt(
      '请输入新密码（至少8位）',
      `重置 ${row.username} 的密码`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: (value) => {
          if (!value || value.length < 8) {
            return '密码长度不能少于8位'
          }
          return true
        }
      }
    )

    console.log('🔧 重置用户密码:', row.username)

    await userStore.resetUserPassword(row.id, newPassword)
    console.log('✅ 密码重置成功')

    ElMessage.success(`用户 "${row.username}" 密码重置成功`)
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消重置密码')
    } else {
      console.error('❌ 重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.currentPage = 1
  loadUsers()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadUsers()
}

// 表单成功处理
const handleFormSuccess = () => {
  loadUsers()
}

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.value.currentPage,
      page_size: pagination.value.pageSize,
      search: searchForm.value.keyword,
      role: searchForm.value.role,
      is_active: searchForm.value.is_active
    }

    const response = await userStore.fetchUsers(params)
    pagination.value.total = response.count
  } catch (error) {
    console.error('加载用户数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card,
.action-card,
.table-card {
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
