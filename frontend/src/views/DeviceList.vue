<template>
  <div class="device-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>设备管理</span>
          <el-button
            type="primary"
            @click="handleAdd"
            :disabled="!authStore.user?.is_device_admin"
            v-if="authStore.user?.is_device_admin"
          >
            <el-icon><Plus /></el-icon>
            添加设备
          </el-button>
          <el-button
            type="success"
            @click="handleBatchImport"
            :disabled="!authStore.user?.is_device_admin"
            v-if="authStore.user?.is_device_admin"
          >
            <el-icon><Upload /></el-icon>
            批量导入
          </el-button>
          <el-tooltip
            v-else
            content="只有设备管理员可以添加设备"
            placement="top"
          >
            <el-button
              type="primary"
              disabled
            >
              <el-icon><Plus /></el-icon>
              添加设备
            </el-button>
          </el-tooltip>
        </div>
      </template>
      
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索设备名称或型号"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="searchForm.status"
              placeholder="设备状态"
              clearable
            >
              <el-option
                label="全部"
                value=""
              />
              <el-option
                label="库存中"
                value="in_stock"
              />
              <el-option
                label="可借用"
                value="available"
              />
              <el-option
                label="闲置"
                value="idle"
              />
              <el-option
                label="锁定"
                value="locked"
              />
              <el-option
                label="借出中"
                value="borrowed"
              />
              <el-option
                label="维修中"
                value="maintenance"
              />
              <el-option
                label="丢失"
                value="lost"
              />
              <el-option
                label="报废"
                value="scrapped"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button
              type="primary"
              @click="handleSearch"
            >
              搜索
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 设备列表 -->
      <el-table
        v-loading="loading"
        :data="deviceList"
        style="width: 100%"
      >
        <el-table-column
          prop="device_number"
          label="编号"
          width="80"
        />
        <el-table-column
          prop="name"
          label="设备名称"
          width="120"
        />
        <el-table-column
          prop="model"
          label="型号"
          width="120"
        />
        <el-table-column
          prop="os_version"
          label="系统版本"
          width="100"
        />
        <el-table-column
          prop="memory"
          label="内存"
          width="100"
        />
        <el-table-column
          prop="serial_number"
          label="序列号"
          width="150"
        />
        <el-table-column
          prop="status"
          label="状态"
          width="100"
        >
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status_display || getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="owner_info"
          label="归属者"
          width="150"
        />
        <el-table-column
          prop="current_user_info"
          label="当前使用人"
          width="150"
        />
        <el-table-column
          label="操作"
          width="200"
        >
          <template #default="scope">
            <el-button
              size="small"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 设备表单对话框 -->
    <DeviceForm
      v-model="deviceFormVisible"
      :device="currentDevice"
      :readonly="isReadonlyMode"
      @success="handleFormSuccess"
    />

    <!-- 批量导入对话框 -->
    <DeviceBatchImport
      v-model="batchImportVisible"
      @success="handleBatchImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Upload } from '@element-plus/icons-vue'
import DeviceForm from '@/components/DeviceForm.vue'
import DeviceBatchImport from '@/components/DeviceBatchImport.vue'
import { useDeviceStore } from '@/store/devices'
import { useAuthStore } from '@/store/auth'
import type { Device } from '@/api/devices'

const deviceStore = useDeviceStore()
const authStore = useAuthStore()

// 设备表单相关
const deviceFormVisible = ref(false)
const currentDevice = ref<Device | null>(null)
const isReadonlyMode = ref(false)

// 批量导入相关
const batchImportVisible = ref(false)

// 搜索表单
const searchForm = ref({
  keyword: '',
  status: ''
})

// 设备列表
const deviceList = ref<Device[]>([])

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const loading = ref(false)

// 状态映射
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    in_stock: 'info',
    available: 'success',
    idle: 'info',
    locked: 'warning',
    borrowed: 'warning',
    maintenance: 'danger',
    lost: 'danger',
    scrapped: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    in_stock: '库存中',
    available: '可借用',
    idle: '闲置',
    locked: '锁定',
    borrowed: '借出中',
    maintenance: '维修中',
    lost: '丢失',
    scrapped: '报废'
  }
  return statusMap[status] || status
}

// 事件处理
const handleSearch = () => {
  pagination.value.currentPage = 1
  loadDevices()
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    status: ''
  }
  handleSearch()
}

const handleAdd = () => {
  console.log('🔧 点击添加设备按钮')
  console.log('用户认证状态:', authStore.isAuthenticated)
  console.log('用户信息:', authStore.user)
  console.log('是否为设备管理员:', authStore.user?.is_device_admin)

  // 检查用户权限
  if (!authStore.isAuthenticated) {
    ElMessage.error('请先登录')
    return
  }

  if (!authStore.user?.is_device_admin) {
    ElMessage.warning('只有设备管理员可以添加设备')
    return
  }

  currentDevice.value = null
  isReadonlyMode.value = false
  deviceFormVisible.value = true

  console.log('表单显示状态:', deviceFormVisible.value)
}

const handleView = async (row: any) => {
  try {
    console.log('🔍 查看设备:', row.name, 'ID:', row.id)

    // 获取设备详情
    const device = await deviceStore.fetchDeviceDetail(row.id)
    console.log('✅ 设备详情:', device)

    // 设置当前设备并显示表单（只读模式）
    currentDevice.value = device
    isReadonlyMode.value = true
    deviceFormVisible.value = true

    ElMessage.success(`已加载设备详情: ${row.name}`)
  } catch (error) {
    console.error('❌ 查看设备失败:', error)
    ElMessage.error(`查看设备失败: ${row.name}`)
  }
}

const handleEdit = async (row: Device) => {
  try {
    console.log('🔧 编辑设备:', row.name, 'ID:', row.id)

    // 先获取完整的设备详情数据
    const deviceDetail = await deviceStore.fetchDeviceDetail(row.id)
    console.log('🔧 获取到的设备详情:', deviceDetail)

    currentDevice.value = deviceDetail
    isReadonlyMode.value = false
    deviceFormVisible.value = true
  } catch (error) {
    console.error('❌ 获取设备详情失败:', error)
    ElMessage.error(`获取设备详情失败: ${row.name}`)
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    console.log('🗑️ 删除设备:', row.name, 'ID:', row.id)

    // 调用删除API
    await deviceStore.deleteDevice(row.id)
    console.log('✅ 设备删除成功:', row.name)

    // 重新加载设备列表
    await loadDevices()

    ElMessage.success(`设备 "${row.name}" 删除成功`)
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消删除')
    } else {
      console.error('❌ 删除设备失败:', error)
      ElMessage.error(`删除设备失败: ${row.name}`)
    }
  }
}

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val
  pagination.value.currentPage = 1
  loadDevices()
}

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val
  loadDevices()
}

// 表单成功处理
const handleFormSuccess = () => {
  loadDevices()
}

// 批量导入处理
const handleBatchImport = () => {
  console.log('🔧 批量导入设备')

  // 检查用户权限
  if (!authStore.isAuthenticated) {
    ElMessage.error('请先登录')
    return
  }

  if (!authStore.user?.is_device_admin) {
    ElMessage.warning('只有设备管理员可以批量导入设备')
    return
  }

  batchImportVisible.value = true
}

// 批量导入成功处理
const handleBatchImportSuccess = () => {
  loadDevices()
}

// 加载设备数据
const loadDevices = async () => {
  try {
    loading.value = true
    const response = await deviceStore.fetchDevices({
      page: pagination.value.currentPage,
      page_size: pagination.value.pageSize,
      search: searchForm.value.keyword,
      status: searchForm.value.status
    })

    deviceList.value = response.results
    pagination.value.total = response.count
  } catch (error) {
    console.error('加载设备数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDevices()
})
</script>

<style scoped>
.device-list {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
