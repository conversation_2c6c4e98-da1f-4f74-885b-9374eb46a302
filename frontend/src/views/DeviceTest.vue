<template>
  <div class="device-test">
    <el-card>
      <template #header>
        <span>设备功能测试</span>
      </template>
      
      <el-space direction="vertical" size="large" style="width: 100%">
        <!-- 认证状态检查 -->
        <el-card>
          <template #header>认证状态</template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="登录状态">
              <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
                {{ authStore.isAuthenticated ? '已登录' : '未登录' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="用户信息">
              {{ authStore.user?.username || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="用户角色">
              {{ authStore.user?.role || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否管理员">
              <el-tag :type="authStore.isAdmin ? 'success' : 'info'">
                {{ authStore.isAdmin ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
          
          <div style="margin-top: 16px">
            <el-button 
              v-if="!authStore.isAuthenticated" 
              type="primary" 
              @click="handleLogin"
              :loading="loginLoading"
            >
              登录
            </el-button>
            <el-button 
              v-else 
              @click="handleLogout"
            >
              登出
            </el-button>
          </div>
        </el-card>

        <!-- 网络连接测试 -->
        <el-card>
          <template #header>网络连接测试</template>
          <el-space>
            <el-button @click="testNetworkConnection" :loading="networkTesting">
              测试后端连接
            </el-button>
            <el-button @click="testDeviceAPI" :loading="apiTesting">
              测试设备API
            </el-button>
          </el-space>
          
          <div v-if="networkResult" style="margin-top: 16px">
            <el-alert 
              :title="networkResult.title" 
              :type="networkResult.type" 
              :description="networkResult.description"
              show-icon
            />
          </div>
        </el-card>

        <!-- 设备操作测试 -->
        <el-card>
          <template #header>设备操作测试</template>
          
          <el-space>
            <el-button @click="testGetDevices" :loading="devicesLoading">
              获取设备列表
            </el-button>
            <el-button @click="testGetCategories" :loading="categoriesLoading">
              获取设备分类
            </el-button>
            <el-button 
              @click="testCreateDevice" 
              :loading="createLoading"
              type="primary"
            >
              测试创建设备
            </el-button>
          </el-space>

          <div v-if="testResults.length > 0" style="margin-top: 16px">
            <el-timeline>
              <el-timeline-item
                v-for="(result, index) in testResults"
                :key="index"
                :type="result.type"
                :timestamp="result.timestamp"
              >
                <h4>{{ result.title }}</h4>
                <p>{{ result.description }}</p>
                <pre v-if="result.data" style="background: #f5f5f5; padding: 8px; border-radius: 4px; font-size: 12px;">{{ JSON.stringify(result.data, null, 2) }}</pre>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <!-- 添加设备表单测试 -->
        <el-card>
          <template #header>添加设备表单测试</template>
          
          <el-button 
            type="primary" 
            @click="showDeviceForm = true"
            :disabled="!authStore.isAuthenticated"
          >
            打开添加设备表单
          </el-button>
          
          <DeviceForm
            v-model="showDeviceForm"
            @success="handleDeviceFormSuccess"
          />
        </el-card>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/store/auth'
import { useDeviceStore } from '@/store/devices'
import { deviceAPI } from '@/api/devices'
import DeviceForm from '@/components/DeviceForm.vue'

const authStore = useAuthStore()
const deviceStore = useDeviceStore()

// 状态
const loginLoading = ref(false)
const networkTesting = ref(false)
const apiTesting = ref(false)
const devicesLoading = ref(false)
const categoriesLoading = ref(false)
const createLoading = ref(false)
const showDeviceForm = ref(false)

const networkResult = ref<any>(null)
const testResults = ref<any[]>([])

// 添加测试结果
const addTestResult = (title: string, type: 'success' | 'warning' | 'danger' | 'info', description: string, data?: any) => {
  testResults.value.unshift({
    title,
    type,
    description,
    data,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 登录测试
const handleLogin = async () => {
  try {
    loginLoading.value = true
    await authStore.login({
      username: 'admin',
      password: 'admin123'
    })
    addTestResult('登录测试', 'success', '登录成功')
  } catch (error) {
    addTestResult('登录测试', 'danger', '登录失败', error)
  } finally {
    loginLoading.value = false
  }
}

// 登出
const handleLogout = async () => {
  await authStore.logout()
  addTestResult('登出', 'info', '已登出')
}

// 测试网络连接
const testNetworkConnection = async () => {
  try {
    networkTesting.value = true
    const response = await fetch('http://localhost:8000/api/health/')
    
    if (response.ok) {
      const data = await response.json()
      networkResult.value = {
        title: '网络连接正常',
        type: 'success',
        description: `后端服务响应正常，状态码: ${response.status}`
      }
      addTestResult('网络连接测试', 'success', '后端连接正常', data)
    } else {
      networkResult.value = {
        title: '网络连接异常',
        type: 'warning',
        description: `后端服务响应异常，状态码: ${response.status}`
      }
      addTestResult('网络连接测试', 'warning', `响应异常: ${response.status}`)
    }
  } catch (error) {
    networkResult.value = {
      title: '网络连接失败',
      type: 'danger',
      description: '无法连接到后端服务'
    }
    addTestResult('网络连接测试', 'danger', '连接失败', error)
  } finally {
    networkTesting.value = false
  }
}

// 测试设备API
const testDeviceAPI = async () => {
  try {
    apiTesting.value = true
    
    // 测试获取设备列表
    const devices = await deviceAPI.getDeviceList()
    addTestResult('设备API测试', 'success', `获取设备列表成功，共 ${devices.count} 个设备`, devices)
    
    // 测试获取设备分类
    const categories = await deviceAPI.getDeviceCategories()
    addTestResult('设备API测试', 'success', `获取设备分类成功，共 ${categories.length} 个分类`, categories)
    
  } catch (error) {
    addTestResult('设备API测试', 'danger', 'API测试失败', error)
  } finally {
    apiTesting.value = false
  }
}

// 测试获取设备列表
const testGetDevices = async () => {
  try {
    devicesLoading.value = true
    const result = await deviceStore.fetchDevices()
    addTestResult('获取设备列表', 'success', `成功获取 ${result.count} 个设备`, result)
  } catch (error) {
    addTestResult('获取设备列表', 'danger', '获取失败', error)
  } finally {
    devicesLoading.value = false
  }
}

// 测试获取设备分类
const testGetCategories = async () => {
  try {
    categoriesLoading.value = true
    const result = await deviceStore.fetchCategories()
    addTestResult('获取设备分类', 'success', `成功获取 ${result.length} 个分类`, result)
  } catch (error) {
    addTestResult('获取设备分类', 'danger', '获取失败', error)
  } finally {
    categoriesLoading.value = false
  }
}

// 测试创建设备
const testCreateDevice = async () => {
  try {
    createLoading.value = true
    
    // 先获取分类
    const categories = await deviceStore.fetchCategories()
    if (!categories || categories.length === 0) {
      throw new Error('没有可用的设备分类')
    }

    const deviceData = {
      name: '测试设备',
      model: 'TEST-001',
      serial_number: `SN${Date.now()}`,
      brand: '测试品牌',
      category: categories[0].id,
      cpu: '测试CPU',
      memory: '8GB',
      storage: '256GB',
      os: '测试系统',
      purchase_price: 1000.00,
      purchase_date: '2025-07-30'
    }

    const result = await deviceStore.createDevice(deviceData)
    addTestResult('创建设备', 'success', `设备创建成功: ${result.name}`, result)
    
  } catch (error) {
    addTestResult('创建设备', 'danger', '创建失败', error)
  } finally {
    createLoading.value = false
  }
}

// 设备表单成功回调
const handleDeviceFormSuccess = () => {
  addTestResult('设备表单', 'success', '通过表单创建设备成功')
  showDeviceForm.value = false
}

// 组件挂载时初始化
onMounted(() => {
  addTestResult('页面加载', 'info', '设备测试页面已加载')
})
</script>

<style scoped>
.device-test {
  padding: 20px;
}

pre {
  max-height: 200px;
  overflow-y: auto;
}
</style>
