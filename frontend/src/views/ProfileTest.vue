<template>
  <div class="profile-test">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>个人资料测试页面</span>
        </div>
      </template>

      <div class="test-content">
        <h3>页面加载成功！</h3>
        <p>当前时间: {{ currentTime }}</p>
        <p>用户信息: {{ userInfo }}</p>
        
        <el-button type="primary" @click="testAPI">
          测试API调用
        </el-button>
        
        <div v-if="apiResult" class="api-result">
          <h4>API调用结果:</h4>
          <pre>{{ apiResult }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/store/auth'

const authStore = useAuthStore()
const currentTime = ref('')
const userInfo = ref('')
const apiResult = ref('')

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 测试API调用
const testAPI = async () => {
  try {
    ElMessage.info('正在测试API调用...')
    const userData = await authStore.fetchUserProfile()
    apiResult.value = JSON.stringify(userData, null, 2)
    ElMessage.success('API调用成功')
  } catch (error) {
    console.error('API调用失败:', error)
    apiResult.value = `错误: ${error}`
    ElMessage.error('API调用失败')
  }
}

// 组件挂载
onMounted(() => {
  console.log('ProfileTest 组件已挂载')
  updateTime()
  setInterval(updateTime, 1000)
  
  userInfo.value = authStore.user ? 
    `${authStore.user.username} (${authStore.user.role_display})` : 
    '未登录'
})
</script>

<style scoped>
.profile-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-content {
  padding: 20px;
}

.api-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.api-result pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
