<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row
      :gutter="20"
      class="stats-row"
    >
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon
                size="40"
                color="#409eff"
              >
                <Monitor />
              </el-icon>
            </div>
            <div class="stat-info">
              <h3>总设备数</h3>
              <p class="stat-number">
                {{ dashboardStats?.device_stats.total || 0 }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon
                size="40"
                color="#67c23a"
              >
                <CircleCheck />
              </el-icon>
            </div>
            <div class="stat-info">
              <h3>可借用设备</h3>
              <p class="stat-number">
                {{ dashboardStats?.device_stats.available || 0 }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon
                size="40"
                color="#e6a23c"
              >
                <Clock />
              </el-icon>
            </div>
            <div class="stat-info">
              <h3>借出中设备</h3>
              <p class="stat-number">
                {{ dashboardStats?.device_stats.borrowed || 0 }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon
                size="40"
                color="#f56c6c"
              >
                <User />
              </el-icon>
            </div>
            <div class="stat-info">
              <h3>活跃用户</h3>
              <p class="stat-number">
                {{ dashboardStats?.user_stats.active_borrowers || 0 }}
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row
      :gutter="20"
      style="margin-top: 20px;"
    >
      <el-col :span="12">
        <BaseChart
          title="设备状态分布"
          :option="deviceStatusChartOption"
          :loading="loading"
          @refresh="loadDashboardData"
        />
      </el-col>

      <el-col :span="12">
        <BaseChart
          title="借用趋势"
          :option="loanTrendChartOption"
          :loading="loading"
          @refresh="loadDashboardData"
        />
      </el-col>
    </el-row>

    <el-row
      :gutter="20"
      style="margin-top: 20px;"
    >
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近借用记录</span>
            </div>
          </template>
          <el-table
            v-loading="loading"
            :data="recentLoans"
            style="width: 100%"
          >
            <el-table-column
              prop="deviceName"
              label="设备名称"
            />
            <el-table-column
              prop="borrower"
              label="借用人"
            />
            <el-table-column
              prop="borrowDate"
              label="借用时间"
            />
            <el-table-column
              prop="status"
              label="状态"
            >
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统公告</span>
            </div>
          </template>
          <div class="announcement">
            <el-alert
              title="系统维护通知"
              type="info"
              description="系统将于本周末进行维护升级，请提前做好设备归还安排。"
              show-icon
              :closable="false"
            />
            <el-alert
              title="新功能上线"
              type="success"
              description="设备管理平台正式上线，支持设备借用、归还、统计等功能。"
              show-icon
              :closable="false"
              style="margin-top: 10px;"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Monitor, CircleCheck, Clock, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BaseChart from '@/components/charts/BaseChart.vue'
import { ReportsAPI, type DashboardStats, ChartUtils } from '@/api/reports'
import { loanAPI } from '@/api/loans'
import { useAuthStore } from '@/store/auth'
import { safeApiCall } from '@/utils/errorHandler'

// 认证状态
const authStore = useAuthStore()

// 数据状态
const loading = ref(false)
const dashboardStats = ref<DashboardStats | null>(null)

// 最近借用记录
const recentLoans = ref([
  {
    deviceName: 'iPhone 14 Pro',
    borrower: '张三',
    borrowDate: '2025-07-27',
    status: '借出中'
  },
  {
    deviceName: 'MacBook Pro',
    borrower: '李四',
    borrowDate: '2025-07-26',
    status: '已归还'
  },
  {
    deviceName: 'iPad Air',
    borrower: '王五',
    borrowDate: '2025-07-25',
    status: '借出中'
  }
])

// 设备状态图表配置
const deviceStatusChartOption = computed(() => {
  if (!dashboardStats.value) return {}

  const stats = dashboardStats.value.device_stats
  const data = [
    { name: '可借用', value: stats.available },
    { name: '借出中', value: stats.borrowed },
    { name: '维修中', value: stats.maintenance },
    { name: '其他', value: stats.total - stats.available - stats.borrowed - stats.maintenance }
  ].filter(item => item.value > 0)

  return ChartUtils.generateDoughnutChartOption('设备状态分布', data)
})

// 借用趋势图表配置
const loanTrendChartOption = computed(() => {
  if (!dashboardStats.value) return {}

  const stats = dashboardStats.value.loan_stats
  const data = [stats.this_week, stats.this_month - stats.this_week, stats.total - stats.this_month]
  const labels = ['本周', '本月其他', '历史总计']

  return ChartUtils.generateBarChartOption('借用统计', labels, data)
})

// 加载仪表盘数据
const loadDashboardData = async () => {
  // 检查用户是否已认证，避免在登出过程中发起请求
  if (!authStore.isAuthenticated) {
    console.log('用户未认证，跳过数据加载')
    return
  }

  try {
    loading.value = true
    const result = await safeApiCall(
      () => ReportsAPI.getDashboardStats(),
      '加载仪表盘数据'
    )

    if (result) {
      dashboardStats.value = result
    }
  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    // 错误处理已由safeApiCall处理
  } finally {
    loading.value = false
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '借出中':
      return 'warning'
    case '已归还':
      return 'success'
    case '超期':
      return 'danger'
    default:
      return 'info'
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  margin-right: 20px;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #666;
}

.stat-number {
  margin: 0;
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement {
  padding: 10px 0;
}
</style>
