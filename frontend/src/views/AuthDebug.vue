<template>
  <div class="auth-debug">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>认证状态调试</span>
          <el-button type="primary" @click="refreshData">刷新数据</el-button>
        </div>
      </template>

      <!-- 认证状态 -->
      <el-divider content-position="left">认证状态</el-divider>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="是否已认证">
          <el-tag :type="authStore.isAuthenticated ? 'success' : 'danger'">
            {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="Access Token">
          <el-tag :type="authStore.accessToken ? 'success' : 'danger'">
            {{ authStore.accessToken ? '存在' : '不存在' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="Refresh Token">
          <el-tag :type="authStore.refreshToken ? 'success' : 'danger'">
            {{ authStore.refreshToken ? '存在' : '不存在' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户信息">
          <el-tag :type="authStore.user ? 'success' : 'danger'">
            {{ authStore.user ? '已加载' : '未加载' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 用户信息 -->
      <el-divider content-position="left">用户信息</el-divider>
      <div v-if="authStore.user">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ authStore.user.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ authStore.user.email }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ authStore.user.role_display }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ authStore.user.department || '未设置' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-alert v-else type="warning" title="用户信息未加载" show-icon />

      <!-- 权限信息 -->
      <el-divider content-position="left">权限信息</el-divider>
      <div v-if="authStore.user">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="超级管理员">
            <el-tag :type="authStore.user.is_super_admin ? 'success' : 'info'">
              {{ authStore.user.is_super_admin ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备管理员">
            <el-tag :type="authStore.user.is_device_admin ? 'success' : 'info'">
              {{ authStore.user.is_device_admin ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备归属者">
            <el-tag :type="authStore.user.is_device_owner ? 'success' : 'info'">
              {{ authStore.user.is_device_owner ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="可创建设备">
            <el-tag :type="authStore.user.is_device_admin ? 'success' : 'danger'">
              {{ authStore.user.is_device_admin ? '可以' : '不可以' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-alert v-else type="warning" title="权限信息未加载" show-icon />

      <!-- Token信息 -->
      <el-divider content-position="left">Token信息</el-divider>
      <div v-if="authStore.accessToken">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="Token过期时间">
            {{ tokenExpiry || '解析失败' }}
          </el-descriptions-item>
          <el-descriptions-item label="Token是否过期">
            <el-tag :type="isTokenExpired ? 'danger' : 'success'">
              {{ isTokenExpired ? '已过期' : '有效' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-alert v-else type="warning" title="Token信息不可用" show-icon />

      <!-- 操作按钮 -->
      <el-divider content-position="left">操作</el-divider>
      <el-space>
        <el-button v-if="!authStore.isAuthenticated" type="primary" @click="goToLogin">
          去登录
        </el-button>
        <el-button v-if="authStore.isAuthenticated" type="warning" @click="logout">
          退出登录
        </el-button>
        <el-button @click="testDeviceAPI">测试设备API</el-button>
        <el-button @click="fetchUserProfile">重新获取用户信息</el-button>
      </el-space>

      <!-- API测试结果 -->
      <el-divider content-position="left">API测试结果</el-divider>
      <el-alert v-if="apiTestResult" :type="apiTestResult.type" :title="apiTestResult.message" show-icon />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { deviceAPI } from '@/api/devices'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

const apiTestResult = ref<{ type: string; message: string } | null>(null)

// 计算Token过期时间
const tokenExpiry = computed(() => {
  if (!authStore.accessToken) return null
  
  try {
    const tokenPayload = JSON.parse(atob(authStore.accessToken.split('.')[1]))
    return new Date(tokenPayload.exp * 1000).toLocaleString()
  } catch (error) {
    return '解析失败'
  }
})

// 检查Token是否过期
const isTokenExpired = computed(() => {
  if (!authStore.accessToken) return true
  
  try {
    const tokenPayload = JSON.parse(atob(authStore.accessToken.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    return tokenPayload.exp <= currentTime
  } catch (error) {
    return true
  }
})

// 刷新数据
const refreshData = () => {
  // 触发响应式更新
  console.log('刷新认证状态数据')
}

// 去登录
const goToLogin = () => {
  router.push('/login')
}

// 退出登录
const logout = async () => {
  await authStore.logout()
  ElMessage.success('已退出登录')
}

// 测试设备API
const testDeviceAPI = async () => {
  try {
    apiTestResult.value = null
    const response = await deviceAPI.getDeviceList({ page: 1, page_size: 1 })
    apiTestResult.value = {
      type: 'success',
      message: `设备API测试成功，返回 ${response.count} 个设备`
    }
  } catch (error: any) {
    console.error('设备API测试失败:', error)
    apiTestResult.value = {
      type: 'error',
      message: `设备API测试失败: ${error.response?.data?.detail || error.message}`
    }
  }
}

// 重新获取用户信息
const fetchUserProfile = async () => {
  try {
    await authStore.fetchUserProfile()
    ElMessage.success('用户信息更新成功')
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

onMounted(() => {
  console.log('认证调试页面已加载')
})
</script>

<style scoped>
.auth-debug {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-divider {
  margin: 20px 0;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-alert {
  margin-bottom: 20px;
}
</style>
