/**
 * 错误处理工具
 * 提供统一的错误处理和用户友好的错误消息
 */

import { ElMessage } from 'element-plus'

/**
 * 检查是否为认证相关错误
 */
export function isAuthError(error: any): boolean {
  return error?.response?.status === 401
}

/**
 * 检查用户是否正在登出
 */
export function isUserLoggingOut(): boolean {
  return sessionStorage.getItem('logging_out') === 'true'
}

/**
 * 处理API错误，提供用户友好的错误消息
 */
export function handleApiError(error: any, context: string = '操作'): void {
  // 如果是认证错误且用户正在登出，静默处理
  if (isAuthError(error) && isUserLoggingOut()) {
    console.log('用户正在登出，忽略认证错误')
    return
  }

  // 根据错误类型提供不同的消息
  if (error?.response) {
    const status = error.response.status
    const errorData = error.response.data

    switch (status) {
      case 400:
        // 处理表单验证错误
        if (errorData) {
          // 处理密码验证错误
          if (errorData.password) {
            const passwordErrors = Array.isArray(errorData.password)
              ? errorData.password
              : [errorData.password]
            ElMessage.error(`密码要求：${passwordErrors.join('；')}`)
            break
          }

          // 处理其他字段错误
          const fieldErrors = []
          for (const [field, messages] of Object.entries(errorData)) {
            if (field !== 'detail' && field !== 'non_field_errors') {
              const fieldMessages = Array.isArray(messages) ? messages : [messages]
              fieldErrors.push(...fieldMessages)
            }
          }

          if (fieldErrors.length > 0) {
            ElMessage.error(fieldErrors.join('；'))
            break
          }

          // 处理通用错误
          if (errorData.detail) {
            ElMessage.error(errorData.detail)
            break
          }

          if (errorData.non_field_errors) {
            const errors = Array.isArray(errorData.non_field_errors)
              ? errorData.non_field_errors
              : [errorData.non_field_errors]
            ElMessage.error(errors.join('；'))
            break
          }
        }
        ElMessage.error(`${context}失败，请检查输入信息`)
        break

      case 401:
        ElMessage.error('登录已过期，请重新登录')
        break
      case 403:
        ElMessage.error('权限不足，无法执行此操作')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 422:
        if (errorData?.errors) {
          const firstError = Object.values(errorData.errors)[0]
          ElMessage.error(Array.isArray(firstError) ? firstError[0] : firstError)
        } else {
          ElMessage.error(errorData?.detail || '数据验证失败')
        }
        break
      case 429:
        ElMessage.error('请求过于频繁，请稍后重试')
        break
      case 500:
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务器错误，请稍后重试')
        break
      default:
        ElMessage.error(`${context}失败，请重试`)
    }
  } else if (error?.code === 'ECONNABORTED') {
    ElMessage.error('请求超时，请检查网络连接')
  } else if (error?.message?.includes('Network Error')) {
    ElMessage.error('网络连接失败，请检查网络')
  } else {
    ElMessage.error(`${context}失败，请重试`)
  }
}

/**
 * 安全的API调用包装器
 * 自动处理登出状态下的请求
 */
export async function safeApiCall<T>(
  apiCall: () => Promise<T>,
  context: string = '操作'
): Promise<T | null> {
  // 如果用户正在登出，直接返回null
  if (isUserLoggingOut()) {
    console.log('用户正在登出，跳过API调用')
    return null
  }

  try {
    return await apiCall()
  } catch (error) {
    handleApiError(error, context)
    throw error
  }
}
