/**
 * 前端调试工具
 * 用于调试添加设备功能
 */

import { deviceAPI } from '@/api/devices'
import { authAPI } from '@/api/auth'

// 调试设备API
export const debugDeviceAPI = {
  // 测试登录
  async testLogin() {
    console.log('🔧 测试登录...')
    try {
      const response = await authAPI.login({
        username: 'admin',
        password: 'admin123'
      })
      console.log('✅ 登录成功:', response)
      
      // 保存token到localStorage
      localStorage.setItem('access_token', response.access)
      localStorage.setItem('refresh_token', response.refresh)
      
      return response
    } catch (error) {
      console.error('❌ 登录失败:', error)
      throw error
    }
  },

  // 测试获取设备列表
  async testGetDevices() {
    console.log('🔧 测试获取设备列表...')
    try {
      const response = await deviceAPI.getDeviceList()
      console.log('✅ 获取设备列表成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取设备列表失败:', error)
      throw error
    }
  },

  // 测试获取设备分类
  async testGetCategories() {
    console.log('🔧 测试获取设备分类...')
    try {
      const response = await deviceAPI.getDeviceCategories()
      console.log('✅ 获取设备分类成功:', response)
      return response
    } catch (error) {
      console.error('❌ 获取设备分类失败:', error)
      throw error
    }
  },

  // 测试创建设备
  async testCreateDevice() {
    console.log('🔧 测试创建设备...')
    
    // 先获取分类
    const categories = await this.testGetCategories()
    if (!categories || categories.length === 0) {
      throw new Error('没有可用的设备分类')
    }

    const deviceData = {
      name: '前端测试设备',
      model: 'FRONTEND-TEST-001',
      serial_number: `SN${Date.now()}`,
      brand: '前端测试品牌',
      category: categories[0].id,
      cpu: '前端测试CPU',
      memory: '8GB',
      storage: '256GB',
      os: '前端测试系统',
      purchase_price: 1000.00,
      purchase_date: '2025-07-30'
    }

    try {
      const response = await deviceAPI.createDevice(deviceData)
      console.log('✅ 创建设备成功:', response)
      return response
    } catch (error) {
      console.error('❌ 创建设备失败:', error)
      console.error('设备数据:', deviceData)
      throw error
    }
  },

  // 完整测试流程
  async runFullTest() {
    console.log('🚀 开始完整测试流程...')
    console.log('=' * 50)

    try {
      // 1. 测试登录
      await this.testLogin()
      
      // 2. 测试获取设备列表
      await this.testGetDevices()
      
      // 3. 测试获取设备分类
      await this.testGetCategories()
      
      // 4. 测试创建设备
      const device = await this.testCreateDevice()
      
      // 5. 清理测试数据
      if (device && device.id) {
        console.log('🔧 清理测试数据...')
        try {
          await deviceAPI.deleteDevice(device.id)
          console.log('✅ 清理测试数据成功')
        } catch (error) {
          console.warn('⚠️ 清理测试数据失败:', error)
        }
      }
      
      console.log('=' * 50)
      console.log('✅ 所有测试通过！前端设备API功能正常')
      
    } catch (error) {
      console.log('=' * 50)
      console.error('❌ 测试失败:', error)
      throw error
    }
  },

  // 检查认证状态
  checkAuthStatus() {
    console.log('🔧 检查认证状态...')
    
    const accessToken = localStorage.getItem('access_token')
    const refreshToken = localStorage.getItem('refresh_token')
    
    console.log('Access Token:', accessToken ? '存在' : '不存在')
    console.log('Refresh Token:', refreshToken ? '存在' : '不存在')
    
    if (accessToken) {
      try {
        const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]))
        const currentTime = Math.floor(Date.now() / 1000)
        const isExpired = tokenPayload.exp <= currentTime
        
        console.log('Token过期时间:', new Date(tokenPayload.exp * 1000))
        console.log('当前时间:', new Date())
        console.log('Token是否过期:', isExpired)
        
        return !isExpired
      } catch (error) {
        console.error('Token解析失败:', error)
        return false
      }
    }
    
    return false
  },

  // 检查网络连接
  async checkNetworkConnection() {
    console.log('🔧 检查网络连接...')
    
    try {
      const response = await fetch('http://localhost:8000/api/health/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        console.log('✅ 后端连接正常')
        return true
      } else {
        console.error('❌ 后端连接异常:', response.status, response.statusText)
        return false
      }
    } catch (error) {
      console.error('❌ 网络连接失败:', error)
      return false
    }
  }
}

// 在浏览器控制台中暴露调试工具
if (typeof window !== 'undefined') {
  (window as any).debugDeviceAPI = debugDeviceAPI
  console.log('🔧 调试工具已加载，使用 debugDeviceAPI 进行调试')
  console.log('可用方法:')
  console.log('- debugDeviceAPI.checkAuthStatus() - 检查认证状态')
  console.log('- debugDeviceAPI.checkNetworkConnection() - 检查网络连接')
  console.log('- debugDeviceAPI.testLogin() - 测试登录')
  console.log('- debugDeviceAPI.testGetDevices() - 测试获取设备列表')
  console.log('- debugDeviceAPI.testCreateDevice() - 测试创建设备')
  console.log('- debugDeviceAPI.runFullTest() - 运行完整测试')
}
