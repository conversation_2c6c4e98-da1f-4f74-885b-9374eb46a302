/**
 * 前端性能优化工具
 */

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 缓存管理器
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  set(key: string, data: any, ttl = 5 * 60 * 1000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  delete(key: string) {
    this.cache.delete(key)
  }
  
  clear() {
    this.cache.clear()
  }
  
  // 清理过期缓存
  cleanup() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

export const cache = new CacheManager()

// 定期清理缓存
setInterval(() => {
  cache.cleanup()
}, 60 * 1000) // 每分钟清理一次

// API请求缓存装饰器
export function withCache(ttl = 5 * 60 * 1000) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!
    
    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${propertyName}_${JSON.stringify(args)}`
      
      // 尝试从缓存获取
      const cached = cache.get(cacheKey)
      if (cached) {
        console.log(`缓存命中: ${propertyName}`)
        return cached
      }
      
      // 执行原方法
      const result = await method.apply(this, args)
      
      // 缓存结果
      cache.set(cacheKey, result, ttl)
      console.log(`缓存设置: ${propertyName}`)
      
      return result
    } as T
  }
}

// 性能监控
export class PerformanceMonitor {
  private static measurements = new Map<string, number[]>()
  
  static start(name: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      if (!this.measurements.has(name)) {
        this.measurements.set(name, [])
      }
      
      this.measurements.get(name)!.push(duration)
      
      // 如果执行时间过长，发出警告
      if (duration > 1000) {
        console.warn(`性能警告: ${name} 执行时间 ${duration.toFixed(2)}ms`)
      }
    }
  }
  
  static getStats(name: string) {
    const measurements = this.measurements.get(name)
    if (!measurements || measurements.length === 0) {
      return null
    }
    
    const sorted = [...measurements].sort((a, b) => a - b)
    const avg = measurements.reduce((a, b) => a + b, 0) / measurements.length
    const min = sorted[0]
    const max = sorted[sorted.length - 1]
    const p50 = sorted[Math.floor(sorted.length * 0.5)]
    const p95 = sorted[Math.floor(sorted.length * 0.95)]
    
    return { avg, min, max, p50, p95, count: measurements.length }
  }
  
  static getAllStats() {
    const stats: Record<string, any> = {}
    for (const [name] of this.measurements) {
      stats[name] = this.getStats(name)
    }
    return stats
  }
  
  static clear() {
    this.measurements.clear()
  }
}

// 性能监控装饰器
export function monitor(name?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const monitorName = name || `${target.constructor.name}.${propertyName}`
    
    descriptor.value = function (...args: any[]) {
      const end = PerformanceMonitor.start(monitorName)
      
      try {
        const result = method.apply(this, args)
        
        // 如果是Promise，等待完成后再结束监控
        if (result && typeof result.then === 'function') {
          return result.finally(() => end())
        }
        
        end()
        return result
      } catch (error) {
        end()
        throw error
      }
    }
  }
}

// 图片懒加载
export class LazyLoader {
  private observer: IntersectionObserver
  
  constructor() {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            const src = img.dataset.src
            
            if (src) {
              img.src = src
              img.removeAttribute('data-src')
              this.observer.unobserve(img)
            }
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )
  }
  
  observe(element: HTMLElement) {
    this.observer.observe(element)
  }
  
  unobserve(element: HTMLElement) {
    this.observer.unobserve(element)
  }
  
  disconnect() {
    this.observer.disconnect()
  }
}

// 虚拟滚动工具
export class VirtualScroller {
  private container: HTMLElement
  private itemHeight: number
  private visibleCount: number
  private startIndex = 0
  private endIndex = 0
  
  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container
    this.itemHeight = itemHeight
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2
    
    this.container.addEventListener('scroll', this.handleScroll.bind(this))
  }
  
  private handleScroll = throttle(() => {
    const scrollTop = this.container.scrollTop
    this.startIndex = Math.floor(scrollTop / this.itemHeight)
    this.endIndex = Math.min(this.startIndex + this.visibleCount, this.getTotalCount())
    
    this.render()
  }, 16) // 60fps
  
  private getTotalCount(): number {
    // 需要子类实现
    return 0
  }
  
  private render() {
    // 需要子类实现
  }
  
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll)
  }
}

// 资源预加载
export class ResourcePreloader {
  private static preloadedResources = new Set<string>()
  
  static preloadImage(src: string): Promise<void> {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        this.preloadedResources.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }
  
  static preloadScript(src: string): Promise<void> {
    if (this.preloadedResources.has(src)) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = () => {
        this.preloadedResources.add(src)
        resolve()
      }
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })
  }
  
  static preloadCSS(href: string): Promise<void> {
    if (this.preloadedResources.has(href)) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.onload = () => {
        this.preloadedResources.add(href)
        resolve()
      }
      link.onerror = reject
      link.href = href
      document.head.appendChild(link)
    })
  }
}

// 内存使用监控
export class MemoryMonitor {
  static getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
      }
    }
    return null
  }
  
  static startMonitoring(interval = 30000) { // 30秒
    return setInterval(() => {
      const usage = this.getMemoryUsage()
      if (usage) {
        console.log(`内存使用: ${usage.used}MB / ${usage.total}MB (限制: ${usage.limit}MB)`)
        
        // 内存使用率超过80%时警告
        if (usage.used / usage.limit > 0.8) {
          console.warn('内存使用率过高，建议刷新页面')
        }
      }
    }, interval)
  }
}
