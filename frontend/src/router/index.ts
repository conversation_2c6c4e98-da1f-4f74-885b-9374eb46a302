import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import Dashboard from '@/views/Dashboard.vue'
import Login from '@/views/Login.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Dashboard',
      component: Dashboard,
      meta: { requiresAuth: true }
    },
    {
      path: '/devices',
      name: 'DeviceList',
      component: () => import('@/views/DeviceList.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/loans',
      name: 'LoanManagement',
      component: () => import('@/views/LoanManagement.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/users',
      name: 'UserManagement',
      component: () => import('@/views/UserManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/reports',
      name: 'Reports',
      component: () => import('@/views/Reports.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/Profile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('@/views/Settings.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/device-test',
      name: 'DeviceTest',
      component: () => import('@/views/DeviceTest.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/auth-debug',
      name: 'AuthDebug',
      component: () => import('@/views/AuthDebug.vue')
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 清除可能的登出状态标记
      sessionStorage.removeItem('logging_out')
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 如果需要管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      // 权限不足，跳转到首页
      next('/')
      return
    }
  }

  // 如果已登录用户访问登录页，跳转到首页
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

export default router
