<template>
  <div class="lazy-table">
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="visibleData"
      :height="height"
      v-bind="$attrs"
      @scroll="handleScroll"
    >
      <slot />
    </el-table>
    
    <!-- 虚拟滚动占位 -->
    <div 
      v-if="virtualScrolling"
      :style="{ height: `${totalHeight}px` }"
      class="virtual-spacer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { throttle } from '@/utils/performance'

interface Props {
  data: any[]
  itemHeight?: number
  height?: number | string
  virtualScrolling?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 50,
  height: 400,
  virtualScrolling: true,
  pageSize: 50
})

const emit = defineEmits<{
  loadMore: []
}>()

const tableRef = ref()
const loading = ref(false)
const scrollTop = ref(0)

// 计算可见数据
const visibleData = computed(() => {
  if (!props.virtualScrolling) {
    return props.data
  }
  
  const containerHeight = typeof props.height === 'number' ? props.height : 400
  const visibleCount = Math.ceil(containerHeight / props.itemHeight) + 5 // 缓冲区
  const startIndex = Math.floor(scrollTop.value / props.itemHeight)
  const endIndex = Math.min(startIndex + visibleCount, props.data.length)
  
  return props.data.slice(startIndex, endIndex)
})

// 总高度
const totalHeight = computed(() => {
  return props.data.length * props.itemHeight
})

// 滚动处理
const handleScroll = throttle((event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  // 检查是否需要加载更多数据
  const scrollHeight = target.scrollHeight
  const clientHeight = target.clientHeight
  const currentScrollTop = target.scrollTop
  
  if (scrollHeight - clientHeight - currentScrollTop < 100) {
    emit('loadMore')
  }
}, 16) // 60fps

// 监听数据变化
watch(() => props.data, () => {
  // 数据变化时重置滚动位置
  if (tableRef.value) {
    tableRef.value.$el.querySelector('.el-table__body-wrapper').scrollTop = 0
    scrollTop.value = 0
  }
})

onMounted(() => {
  // 初始化滚动监听
  if (tableRef.value) {
    const bodyWrapper = tableRef.value.$el.querySelector('.el-table__body-wrapper')
    if (bodyWrapper) {
      bodyWrapper.addEventListener('scroll', handleScroll)
    }
  }
})

onUnmounted(() => {
  // 清理滚动监听
  if (tableRef.value) {
    const bodyWrapper = tableRef.value.$el.querySelector('.el-table__body-wrapper')
    if (bodyWrapper) {
      bodyWrapper.removeEventListener('scroll', handleScroll)
    }
  }
})

// 暴露方法
defineExpose({
  scrollToTop: () => {
    if (tableRef.value) {
      const bodyWrapper = tableRef.value.$el.querySelector('.el-table__body-wrapper')
      if (bodyWrapper) {
        bodyWrapper.scrollTop = 0
        scrollTop.value = 0
      }
    }
  },
  refresh: () => {
    loading.value = true
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
})
</script>

<style scoped>
.lazy-table {
  position: relative;
}

.virtual-spacer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  pointer-events: none;
  z-index: -1;
}

/* 优化表格性能 */
:deep(.el-table) {
  /* 启用硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

:deep(.el-table__body-wrapper) {
  /* 优化滚动性能 */
  overflow-y: auto;
  will-change: scroll-position;
}

:deep(.el-table__row) {
  /* 优化行渲染 */
  contain: layout style paint;
}
</style>
