<template>
  <div
    v-if="showMonitor"
    class="performance-monitor"
  >
    <el-card class="monitor-card">
      <template #header>
        <div class="monitor-header">
          <span>性能监控</span>
          <el-button
            size="small"
            @click="toggleMonitor"
          >
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>
      
      <div
        v-if="expanded"
        class="monitor-content"
      >
        <!-- 内存使用 -->
        <div class="metric-item">
          <span class="metric-label">内存使用:</span>
          <span
            class="metric-value"
            :class="getMemoryClass()"
          >
            {{ memoryUsage.used }}MB / {{ memoryUsage.total }}MB
          </span>
        </div>
        
        <!-- FPS -->
        <div class="metric-item">
          <span class="metric-label">FPS:</span>
          <span
            class="metric-value"
            :class="getFpsClass()"
          >
            {{ currentFps }}
          </span>
        </div>
        
        <!-- 网络请求 -->
        <div class="metric-item">
          <span class="metric-label">活跃请求:</span>
          <span class="metric-value">{{ activeRequests }}</span>
        </div>
        
        <!-- 页面加载时间 -->
        <div class="metric-item">
          <span class="metric-label">页面加载:</span>
          <span class="metric-value">{{ pageLoadTime }}ms</span>
        </div>
        
        <!-- 操作按钮 -->
        <div class="monitor-actions">
          <el-button
            size="small"
            @click="clearCache"
          >
            清理缓存
          </el-button>
          <el-button
            size="small"
            @click="exportStats"
          >
            导出统计
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { MemoryMonitor, PerformanceMonitor as PerfMonitor, cache } from '@/utils/performance'

interface Props {
  enabled?: boolean
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

const props = withDefaults(defineProps<Props>(), {
  enabled: process.env.NODE_ENV === 'development',
  position: 'top-right'
})

const showMonitor = ref(props.enabled)
const expanded = ref(false)
const memoryUsage = ref({ used: 0, total: 0, limit: 0 })
const currentFps = ref(0)
const activeRequests = ref(0)
const pageLoadTime = ref(0)

let fpsCounter = 0
let lastTime = performance.now()
let animationId: number
let memoryInterval: number

// 计算FPS
const calculateFPS = () => {
  const now = performance.now()
  const delta = now - lastTime
  
  if (delta >= 1000) {
    currentFps.value = Math.round((fpsCounter * 1000) / delta)
    fpsCounter = 0
    lastTime = now
  }
  
  fpsCounter++
  animationId = requestAnimationFrame(calculateFPS)
}

// 更新内存使用情况
const updateMemoryUsage = () => {
  const usage = MemoryMonitor.getMemoryUsage()
  if (usage) {
    memoryUsage.value = usage
  }
}

// 获取内存使用状态样式
const getMemoryClass = () => {
  const usage = memoryUsage.value
  if (usage.limit === 0) return 'normal'
  
  const percentage = usage.used / usage.limit
  if (percentage > 0.8) return 'danger'
  if (percentage > 0.6) return 'warning'
  return 'normal'
}

// 获取FPS状态样式
const getFpsClass = () => {
  if (currentFps.value < 30) return 'danger'
  if (currentFps.value < 50) return 'warning'
  return 'normal'
}

// 切换监控器显示状态
const toggleMonitor = () => {
  expanded.value = !expanded.value
}

// 清理缓存
const clearCache = () => {
  cache.clear()
  ElMessage.success('缓存已清理')
}

// 导出统计数据
const exportStats = () => {
  const stats = {
    timestamp: new Date().toISOString(),
    memory: memoryUsage.value,
    fps: currentFps.value,
    activeRequests: activeRequests.value,
    pageLoadTime: pageLoadTime.value,
    performanceStats: PerfMonitor.getAllStats()
  }
  
  const blob = new Blob([JSON.stringify(stats, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `performance-stats-${Date.now()}.json`
  link.click()
  URL.revokeObjectURL(url)
  
  ElMessage.success('统计数据已导出')
}

// 监听网络请求
const monitorNetworkRequests = () => {
  const originalFetch = window.fetch
  const originalXHROpen = XMLHttpRequest.prototype.open
  
  // 监听fetch请求
  window.fetch = async (...args) => {
    activeRequests.value++
    try {
      const response = await originalFetch(...args)
      return response
    } finally {
      activeRequests.value--
    }
  }
  
  // 监听XMLHttpRequest
  XMLHttpRequest.prototype.open = function(...args) {
    activeRequests.value++
    
    this.addEventListener('loadend', () => {
      activeRequests.value--
    })
    
    return originalXHROpen.apply(this, args)
  }
}

// 获取页面加载时间
const getPageLoadTime = () => {
  if (performance.timing) {
    const timing = performance.timing
    pageLoadTime.value = timing.loadEventEnd - timing.navigationStart
  }
}

onMounted(() => {
  if (!showMonitor.value) return
  
  // 开始FPS监控
  calculateFPS()
  
  // 开始内存监控
  updateMemoryUsage()
  memoryInterval = setInterval(updateMemoryUsage, 5000)
  
  // 监控网络请求
  monitorNetworkRequests()
  
  // 获取页面加载时间
  getPageLoadTime()
  
  // 键盘快捷键切换监控器
  const handleKeyPress = (event: KeyboardEvent) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'P') {
      showMonitor.value = !showMonitor.value
    }
  }
  
  document.addEventListener('keydown', handleKeyPress)
  
  // 清理函数
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyPress)
  })
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  if (memoryInterval) {
    clearInterval(memoryInterval)
  }
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 250px;
}

.monitor-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.monitor-content {
  padding: 10px 0;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.metric-label {
  color: #666;
}

.metric-value {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.metric-value.normal {
  color: #67c23a;
}

.metric-value.warning {
  color: #e6a23c;
}

.metric-value.danger {
  color: #f56c6c;
}

.monitor-actions {
  margin-top: 15px;
  display: flex;
  gap: 8px;
}

/* 不同位置的样式 */
.performance-monitor.top-left {
  top: 20px;
  left: 20px;
  right: auto;
}

.performance-monitor.bottom-right {
  top: auto;
  bottom: 20px;
  right: 20px;
}

.performance-monitor.bottom-left {
  top: auto;
  bottom: 20px;
  left: 20px;
  right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-monitor {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px;
    min-width: auto;
  }
}
</style>
