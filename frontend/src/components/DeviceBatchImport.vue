<template>
  <el-dialog
    v-model="visible"
    title="批量导入设备"
    width="700px"
    :before-close="handleClose"
  >
    <div class="import-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center style="margin-bottom: 20px;">
        <el-step title="下载模板" description="下载Excel导入模板" />
        <el-step title="上传文件" description="上传填写好的Excel文件" />
        <el-step title="数据预览" description="预览导入数据" />
        <el-step title="导入完成" description="确认导入结果" />
      </el-steps>

      <!-- 步骤1: 下载模板 -->
      <div v-show="currentStep === 0" class="step-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <template #default>
            <div>
              <p><strong>必填字段（红色标记）：</strong></p>
              <ul>
                <li>设备名称、设备型号、序列号、品牌、设备状态</li>
              </ul>
              <p><strong>可选字段：</strong></p>
              <ul>
                <li>设备分类、设备归属者、技术规格、采购信息等</li>
              </ul>
              <p><strong>字段选项说明：</strong></p>
              <ul>
                <li><strong>品牌：</strong>HUAWEI、Xiaomi、OPPO、VIVO、Redmi、SAMSUNG、HONOR、OnePlus、Google、Apple、realme、ZTE、MEIZU、SONY、Asus、nubia、Moto、Other</li>
                <li><strong>设备分类：</strong>手机、平板、笔记本、PC、其它</li>
                <li><strong>特殊屏幕：</strong>刘海屏、水滴屏、挖孔屏、瀑布屏、曲面屏、真全面屏、内折屏、外折屏、上下折叠屏、三折屏、环绕屏、滑盖全面屏</li>
                <li><strong>操作系统：</strong>Android、iOS、HarmonyOS、HarmonyOSNext</li>
                <li><strong>设备状态：</strong>库存中、可借用、闲置、锁定、借出中、维修中、丢失、报废</li>
              </ul>
              <p><strong>注意事项：</strong></p>
              <ul>
                <li>序列号必须唯一，不能重复</li>
                <li>字段选项请严格按照上述选项填写，区分大小写</li>
                <li>归属者请使用用户名，系统会自动匹配</li>
                <li>导入失败时需要修正所有错误后重新导入</li>
              </ul>
            </div>
          </template>
        </el-alert>

        <div class="template-download">
          <el-button 
            type="primary" 
            size="large"
            @click="downloadTemplate"
            :loading="downloadLoading"
          >
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 上传文件 -->
      <div v-show="currentStep === 1" class="step-content">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :action="uploadUrl"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :file-list="fileList"
          :limit="1"
          accept=".xlsx,.xls"
          :auto-upload="false"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 .xlsx/.xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>

        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button 
            type="primary" 
            @click="parseFile"
            :loading="parseLoading"
          >
            解析文件
          </el-button>
          <el-button @click="clearFile">清空文件</el-button>
        </div>
      </div>

      <!-- 步骤3: 数据预览 -->
      <div v-show="currentStep === 2" class="step-content">
        <div class="preview-summary">
          <el-alert
            :title="`共解析到 ${previewData.length} 条设备数据`"
            type="success"
            :closable="false"
            style="margin-bottom: 20px;"
          />
        </div>

        <el-table
          :data="previewData"
          style="width: 100%"
          max-height="400"
          border
        >
          <el-table-column prop="name" label="设备名称" width="120" />
          <el-table-column prop="model" label="设备型号" width="120" />
          <el-table-column prop="serial_number" label="序列号" width="120" />
          <el-table-column prop="brand" label="品牌" width="100" />
          <el-table-column prop="status" label="状态" width="100" />
          <el-table-column prop="owner" label="归属者" width="100" />
          <el-table-column prop="category" label="分类" width="100" />
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button 
                size="small" 
                type="danger" 
                @click="removeRow($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-show="currentStep === 3" class="step-content">
        <div class="import-result">
          <el-result
            :icon="importResult.success ? 'success' : 'error'"
            :title="importResult.title"
            :sub-title="importResult.message"
          >
            <template #extra>
              <el-button 
                v-if="importResult.success" 
                type="primary" 
                @click="handleClose"
              >
                完成
              </el-button>
              <el-button 
                v-else 
                type="primary" 
                @click="currentStep = 1"
              >
                重新上传
              </el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          v-if="currentStep === 0" 
          type="primary" 
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button 
          v-if="currentStep === 2" 
          type="primary" 
          @click="confirmImport"
          :loading="importLoading"
        >
          确认导入
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, UploadFilled } from '@element-plus/icons-vue'
import { useDeviceStore } from '@/store/devices'
import * as XLSX from 'xlsx'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const deviceStore = useDeviceStore()

// 响应式数据
const visible = ref(false)
const currentStep = ref(0)
const downloadLoading = ref(false)
const parseLoading = ref(false)
const importLoading = ref(false)
const uploadRef = ref()
const fileList = ref([])
const previewData = ref([])

const uploadUrl = ref('/api/devices/batch-import/')

const importResult = reactive({
  success: false,
  title: '',
  message: ''
})

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  currentStep.value = 0
  fileList.value = []
  previewData.value = []
  importResult.success = false
  importResult.title = ''
  importResult.message = ''
}

// 下载模板
const downloadTemplate = () => {
  downloadLoading.value = true
  
  // 创建Excel模板数据
  const templateData = [
    {
      '设备名称*': 'iPhone 15 Pro',
      '设备型号*': 'iPhone 15 Pro',
      '序列号*': 'F2ABC123456',
      '品牌*': 'Apple (可选: HUAWEI,Xiaomi,OPPO,VIVO,Redmi,SAMSUNG,HONOR,OnePlus,Google,Apple,realme,ZTE,MEIZU,SONY,Asus,nubia,Moto,Other)',
      '设备状态*': '库存中',
      '设备分类': '手机 (可选: 手机,平板,笔记本,PC,其它)',
      '设备归属者': 'admin',
      'CPU': 'A17 Pro',
      'GPU': 'A17 Pro GPU',
      '内存': '8GB',
      '存储': '256GB',
      '分辨率': '2556x1179',
      '屏幕尺寸': '6.1英寸',
      '特殊屏幕': '刘海屏 (可选: 刘海屏,水滴屏,挖孔屏,瀑布屏,曲面屏,真全面屏,内折屏,外折屏,上下折叠屏,三折屏,环绕屏,滑盖全面屏)',
      '操作系统': 'iOS (可选: Android,iOS,HarmonyOS,HarmonyOSNext)',
      '系统版本': 'iOS 17.0',
      '采购价格': '8999.00',
      '采购日期': '2024-01-15',
      '保修期': '1年',
      '特殊说明': '高端旗舰机型'
    }
  ]
  
  try {
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(templateData)
    
    // 设置列宽
    const colWidths = [
      { wch: 15 }, { wch: 20 }, { wch: 15 }, { wch: 10 }, { wch: 10 },
      { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 10 },
      { wch: 15 }, { wch: 12 }, { wch: 10 }, { wch: 15 }, { wch: 10 },
      { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 20 }
    ]
    ws['!cols'] = colWidths
    
    XLSX.utils.book_append_sheet(wb, ws, '设备导入模板')
    
    // 下载文件
    XLSX.writeFile(wb, '设备批量导入模板.xlsx')
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  } finally {
    downloadLoading.value = false
  }
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

// 文件上传前验证
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  
  return false // 阻止自动上传
}

// 解析文件
const parseFile = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  parseLoading.value = true
  
  try {
    const file = fileList.value[0].raw
    const data = await readExcelFile(file)
    previewData.value = data
    currentStep.value = 2
    ElMessage.success(`成功解析 ${data.length} 条数据`)
  } catch (error) {
    console.error('解析文件失败:', error)
    ElMessage.error('解析文件失败，请检查文件格式')
  } finally {
    parseLoading.value = false
  }
}

// 读取Excel文件
const readExcelFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        
        // 转换字段名
        const convertedData = jsonData.map((row: any) => ({
          name: row['设备名称*'] || row['设备名称'],
          model: row['设备型号*'] || row['设备型号'],
          serial_number: row['序列号*'] || row['序列号'],
          brand: row['品牌*'] || row['品牌'],
          status: convertStatus(row['设备状态*'] || row['设备状态']),
          category: row['设备分类'],
          owner: row['设备归属者'],
          cpu: row['CPU'],
          gpu: row['GPU'],
          memory: row['内存'],
          storage: row['存储'],
          resolution: row['分辨率'],
          screen_size: row['屏幕尺寸'],
          special_screen: row['特殊屏幕'],
          os: row['操作系统'],
          os_version: row['系统版本'],
          purchase_price: row['采购价格'],
          purchase_date: row['采购日期'],
          warranty_period: row['保修期'],
          special_notes: row['特殊说明']
        }))
        
        resolve(convertedData)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })
}

// 转换状态值
const convertStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    '库存中': 'in_stock',
    '可借用': 'available',
    '闲置': 'idle',
    '锁定': 'locked',
    '借出中': 'borrowed',
    '维修中': 'maintenance',
    '丢失': 'lost',
    '报废': 'scrapped'
  }
  return statusMap[status] || status
}

// 移除行
const removeRow = (index: number) => {
  previewData.value.splice(index, 1)
}

// 清空文件
const clearFile = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
}

// 确认导入
const confirmImport = async () => {
  if (previewData.value.length === 0) {
    ElMessage.warning('没有可导入的数据')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要导入 ${previewData.value.length} 条设备数据吗？`,
      '确认导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    importLoading.value = true
    
    // 调用批量导入API
    await deviceStore.batchImportDevices(previewData.value)
    
    importResult.success = true
    importResult.title = '导入成功'
    importResult.message = `成功导入 ${previewData.value.length} 条设备数据`
    
    currentStep.value = 3
    emit('success')
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导入失败:', error)
      importResult.success = false
      importResult.title = '导入失败'
      importResult.message = '导入过程中发生错误，请检查数据后重试'
      currentStep.value = 3
    }
  } finally {
    importLoading.value = false
  }
}

// 上传成功回调
const onUploadSuccess = () => {
  // 这里不会被调用，因为我们阻止了自动上传
}

// 上传失败回调
const onUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
.import-container {
  min-height: 400px;
}

.step-content {
  padding: 20px 0;
}

.template-download {
  text-align: center;
  padding: 40px 0;
}

.upload-actions {
  margin-top: 20px;
  text-align: center;
}

.preview-summary {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-upload-dragger) {
  width: 100%;
}

:deep(.el-steps) {
  margin-bottom: 30px;
}
</style>
