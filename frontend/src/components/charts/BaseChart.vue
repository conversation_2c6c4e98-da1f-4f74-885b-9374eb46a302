<template>
  <div class="chart-container">
    <div
      v-if="title"
      class="chart-header"
    >
      <h3>{{ title }}</h3>
      <div class="chart-actions">
        <el-button
          size="small"
          :loading="loading"
          @click="refreshChart"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          size="small"
          @click="exportChart"
        >
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>
    <div 
      ref="chartRef" 
      :style="{ width: width, height: height }"
      class="chart-content"
    />
    <div
      v-if="loading"
      class="chart-loading"
    >
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { Refresh, Download, Loading } from '@element-plus/icons-vue'

interface Props {
  title?: string
  width?: string
  height?: string
  option: any
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false
})

const emit = defineEmits<{
  refresh: []
  export: []
}>()

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return
  
  await nextTick()
  
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  chartInstance = echarts.init(chartRef.value)
  
  // 设置图表配置
  if (props.option) {
    chartInstance.setOption(props.option)
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 刷新图表
const refreshChart = () => {
  emit('refresh')
}

// 导出图表
const exportChart = () => {
  if (chartInstance) {
    const url = chartInstance.getDataURL({
      type: 'png',
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${props.title || 'chart'}.png`
    link.href = url
    link.click()
  }
  emit('export')
}

// 监听配置变化
watch(
  () => props.option,
  (newOption) => {
    if (chartInstance && newOption) {
      chartInstance.setOption(newOption, true)
    }
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (loading) => {
    if (chartInstance) {
      if (loading) {
        chartInstance.showLoading()
      } else {
        chartInstance.hideLoading()
      }
    }
  }
)

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露图表实例给父组件
defineExpose({
  chartInstance,
  refreshChart,
  exportChart
})
</script>

<style scoped>
.chart-container {
  position: relative;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #909399;
  font-size: 14px;
}

.chart-loading .el-icon {
  font-size: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
