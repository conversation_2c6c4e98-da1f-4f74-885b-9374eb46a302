<template>
  <el-dialog
    v-model="visible"
    :title="props.readonly ? '查看用户' : (isEdit ? '编辑用户' : '添加用户')"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- 基础信息 -->
      <el-divider content-position="left">基础信息</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              :readonly="props.readonly || isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱"
              :readonly="props.readonly"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="first_name">
            <el-input
              v-model="formData.first_name"
              placeholder="请输入姓名"
              :readonly="props.readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入手机号"
              :readonly="props.readonly"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 工作信息 -->
      <el-divider content-position="left">工作信息</el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户角色" prop="role">
            <el-select
              v-model="formData.role"
              placeholder="请选择用户角色"
              :disabled="props.readonly"
              style="width: 100%"
            >
              <el-option label="超级管理员" value="super_admin" />
              <el-option label="设备管理员" value="device_admin" />
              <el-option label="设备归属者" value="device_owner" />
              <el-option label="普通用户" value="normal_user" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门" prop="department">
            <el-input
              v-model="formData.department"
              placeholder="请输入部门"
              :readonly="props.readonly"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="员工编号" prop="employee_id">
            <el-input
              v-model="formData.employee_id"
              placeholder="请输入员工编号"
              :readonly="props.readonly"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!isEdit">
          <el-form-item label="初始密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              placeholder="请输入初始密码"
              :readonly="props.readonly"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="!isEdit">
        <el-col :span="12">
          <el-form-item label="确认密码" prop="password_confirm">
            <el-input
              v-model="formData.password_confirm"
              type="password"
              placeholder="请再次输入密码"
              :readonly="props.readonly"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 状态信息 -->
      <el-divider content-position="left" v-if="isEdit">状态信息</el-divider>
      
      <el-row :gutter="20" v-if="isEdit">
        <el-col :span="12">
          <el-form-item label="账户状态">
            <el-switch
              v-model="formData.is_active"
              :disabled="props.readonly || isSuperAdmin"
              active-text="启用"
              inactive-text="禁用"
            />
            <div v-if="isSuperAdmin" class="text-warning">
              <el-text type="warning" size="small">
                超级管理员账户不能禁用
              </el-text>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="props.user">
          <el-form-item label="创建时间">
            <el-text>{{ formatDate(props.user.created_at) }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="isEdit && props.user">
        <el-col :span="12">
          <el-form-item label="最后登录">
            <el-text>{{ formatDate(props.user.last_login) || '从未登录' }}</el-text>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录次数">
            <el-text>{{ props.user.login_count || 0 }} 次</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ props.readonly ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!props.readonly"
          type="primary"
          :loading="submitting"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/users'
import { useAuthStore } from '@/store/auth'
import type { User } from '@/api/auth'

interface Props {
  modelValue: boolean
  user?: User | null
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
  readonly: false
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()
const authStore = useAuthStore()

// 响应式数据
const visible = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  first_name: '',
  phone: '',
  role: 'normal_user',
  department: '',
  employee_id: '',
  password: '',
  password_confirm: '',
  is_active: true
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  first_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择用户角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于 8 个字符', trigger: 'blur' }
  ],
  password_confirm: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== formData.password) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.user)
const isSuperAdmin = computed(() => props.user?.role === 'super_admin')

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadFormData()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载表单数据
const loadFormData = () => {
  if (props.user) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      username: props.user.username,
      email: props.user.email,
      first_name: props.user.first_name || '',
      phone: props.user.phone || '',
      role: props.user.role,
      department: props.user.department || '',
      employee_id: props.user.employee_id || '',
      is_active: props.user.is_active
    })
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    first_name: '',
    phone: '',
    role: 'normal_user',
    department: '',
    employee_id: '',
    password: '',
    password_confirm: '',
    is_active: true
  })
  formRef.value?.clearValidate()
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    if (isEdit.value && props.user) {
      // 编辑用户
      const updateData = {
        email: formData.email,
        first_name: formData.first_name,
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        employee_id: formData.employee_id,
        is_active: formData.is_active
      }
      await userStore.updateUser(props.user.id, updateData)
    } else {
      // 创建用户
      const createData = {
        username: formData.username,
        email: formData.email,
        first_name: formData.first_name,
        phone: formData.phone,
        role: formData.role,
        department: formData.department,
        employee_id: formData.employee_id,
        password: formData.password,
        password_confirm: formData.password_confirm
      }
      await userStore.createUser(createData)
    }

    handleClose()
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.text-warning {
  margin-top: 5px;
}
</style>
