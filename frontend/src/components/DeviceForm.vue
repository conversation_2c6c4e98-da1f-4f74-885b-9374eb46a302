<template>
  <el-dialog
    v-model="visible"
    :title="props.readonly ? '查看设备' : (isEdit ? '编辑设备' : '添加设备')"
    width="900px"
    :before-close="handleClose"
  >
    <!-- 步骤指示器 -->
    <el-steps
      v-if="!props.readonly && !isEdit"
      :active="currentStep"
      align-center
      style="margin-bottom: 20px;"
    >
      <el-step title="基础信息" description="设备基本信息" />
      <el-step title="技术规格" description="硬件配置信息" />
      <el-step title="管理信息" description="采购和管理信息" />
    </el-steps>

    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      style="max-height: 500px; overflow-y: auto;"
    >
      <!-- 步骤1: 基础信息 -->
      <div v-show="currentStep === 0 || props.readonly || isEdit">
        <el-divider content-position="left">
          <el-icon><InfoFilled /></el-icon>
          基础信息
        </el-divider>
      
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="name">
              <template #label>
                <span class="required-field">* 设备名称</span>
              </template>
              <el-input
                v-model="formData.name"
                placeholder="请输入设备名称"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="model">
              <template #label>
                <span class="required-field">* 设备型号</span>
              </template>
              <el-input
                v-model="formData.model"
                placeholder="请输入设备型号"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="serial_number">
              <template #label>
                <span class="required-field">* 序列号</span>
              </template>
              <el-input
                v-model="formData.serial_number"
                placeholder="请输入序列号（唯一标识）"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="brand">
              <template #label>
                <span class="required-field">* 品牌</span>
              </template>
              <el-select
                v-model="formData.brand"
                placeholder="请选择品牌"
                :disabled="props.readonly"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="option in configOptions.brands"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备分类" prop="category">
              <el-select
                v-model="formData.category"
                placeholder="请选择设备分类"
                style="width: 100%"
                :disabled="props.readonly"
                clearable
                filterable
              >
                <el-option
                  v-for="option in configOptions.categories"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备归属者" prop="owner">
              <el-select
                v-model="formData.owner"
                placeholder="请选择设备归属者（可后续分配）"
                style="width: 100%"
                :disabled="props.readonly"
                clearable
                filterable
              >
                <el-option
                  v-for="user in ownerOptions"
                  :key="user.id"
                  :label="`${user.username} (${user.department || '无部门'})`"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="status">
              <template #label>
                <span class="required-field">* 设备状态</span>
              </template>
              <el-select
                v-model="formData.status"
                placeholder="请选择设备状态"
                style="width: 100%"
                :disabled="props.readonly"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编号">
              <el-input
                :value="formData.device_number || '系统自动生成'"
                readonly
                placeholder="系统自动生成"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤2: 技术规格 -->
      <div v-show="currentStep === 1 || props.readonly || isEdit">
        <el-divider content-position="left">
          <el-icon><Setting /></el-icon>
          技术规格
        </el-divider>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="CPU">
            <el-input
              v-model="formData.cpu"
              placeholder="请输入CPU信息"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="GPU">
            <el-input
              v-model="formData.gpu"
              placeholder="请输入GPU信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="内存">
            <el-input
              v-model="formData.memory"
              placeholder="请输入内存信息"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存储">
            <el-input
              v-model="formData.storage"
              placeholder="请输入存储信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分辨率">
            <el-input
              v-model="formData.resolution"
              placeholder="请输入分辨率"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="屏幕尺寸">
            <el-input
              v-model="formData.screen_size"
              placeholder="请输入屏幕尺寸"
            />
          </el-form-item>
        </el-col>
      </el-row>

        <el-form-item label="特殊屏幕">
          <el-select
            v-model="formData.special_screen"
            placeholder="请选择特殊屏幕类型"
            style="width: 100%"
            :disabled="props.readonly"
            clearable
            filterable
          >
            <el-option
              v-for="option in configOptions.special_screens"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 系统信息 -->
        <el-divider content-position="left">系统信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="操作系统">
              <el-select
                v-model="formData.os"
                placeholder="请选择操作系统"
                style="width: 100%"
                :disabled="props.readonly"
                clearable
                filterable
              >
                <el-option
                  v-for="option in configOptions.operating_systems"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="系统版本">
              <el-input
                v-model="formData.os_version"
                placeholder="请输入系统版本"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 步骤3: 管理信息 -->
      <div v-show="currentStep === 2 || props.readonly || isEdit">
        <el-divider content-position="left">
          <el-icon><Management /></el-icon>
          管理信息
        </el-divider>

        <!-- 采购信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购价格">
              <el-input-number
                v-model="formData.purchase_price"
                :precision="2"
                :min="0"
                placeholder="请输入采购价格"
                style="width: 100%"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购日期">
              <el-date-picker
                v-model="formData.purchase_date"
                type="date"
                placeholder="请选择采购日期"
                style="width: 100%"
                :readonly="props.readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="保修期">
          <el-input
            v-model="formData.warranty_period"
            placeholder="请输入保修期（如：3年、36个月）"
            :readonly="props.readonly"
          />
        </el-form-item>

        <!-- 其他信息 -->
        <el-form-item label="特殊说明">
          <el-input
            v-model="formData.special_notes"
            type="textarea"
            :rows="3"
            placeholder="请输入特殊说明（如屏幕锁、损坏等）"
            :readonly="props.readonly"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <el-button @click="handleClose">
            {{ props.readonly ? '关闭' : '取消' }}
          </el-button>
        </div>

        <div class="footer-right" v-if="!props.readonly">
          <!-- 向导模式导航 -->
          <template v-if="!isEdit">
            <el-button
              v-if="currentStep > 0"
              @click="prevStep"
            >
              上一步
            </el-button>
            <el-button
              v-if="currentStep < 2"
              type="primary"
              @click="nextStep"
            >
              下一步
            </el-button>
            <el-button
              v-if="currentStep === 2"
              type="primary"
              :loading="submitting"
              @click="handleSubmit"
            >
              创建设备
            </el-button>
          </template>

          <!-- 编辑模式 -->
          <el-button
            v-if="isEdit"
            type="primary"
            :loading="submitting"
            @click="handleSubmit"
          >
            更新设备
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Setting, Management } from '@element-plus/icons-vue'
import { useDeviceStore } from '@/store/devices'
import { useAuthStore } from '@/store/auth'
import { authAPI } from '@/api/auth'
import { deviceAPI } from '@/api/devices'
import type { Device, DeviceCategory } from '@/api/devices'

interface Props {
  modelValue: boolean
  device?: Device | null
  readonly?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  device: null,
  readonly: false
})

const emit = defineEmits<Emits>()

const deviceStore = useDeviceStore()
const authStore = useAuthStore()

// 响应式数据
const visible = ref(false)
const submitting = ref(false)
const formRef = ref()
const categories = ref<DeviceCategory[]>([])
const ownerOptions = ref<any[]>([])
const currentStep = ref(0)

// 配置选项数据
const configOptions = ref({
  brands: [] as { value: string, label: string }[],
  categories: [] as { value: string, label: string }[],
  special_screens: [] as { value: string, label: string }[],
  operating_systems: [] as { value: string, label: string }[],
  statuses: [] as { value: string, label: string }[]
})

// 表单数据
const formData = reactive<Partial<Device>>({
  name: '',
  model: '',
  serial_number: '',
  brand: '', // 将在配置选项加载后设置默认值
  category: '', // 将在配置选项加载后设置默认值
  owner: '',
  status: 'in_stock',
  cpu: '',
  gpu: '',
  memory: '',
  storage: '',
  resolution: '',
  screen_size: '',
  special_screen: '', // 将在配置选项加载后设置默认值
  os: '', // 将在配置选项加载后设置默认值
  os_version: '',
  purchase_price: undefined,
  purchase_date: '',
  warranty_period: '',
  special_notes: ''
})

// 设备状态选项 - 与数据库设计文档保持一致
const statusOptions = [
  { value: 'in_stock', label: '库存中' },
  { value: 'available', label: '可借用' },
  { value: 'idle', label: '闲置' },
  { value: 'locked', label: '锁定' },
  { value: 'borrowed', label: '借出中' },
  { value: 'maintenance', label: '维修中' },
  { value: 'lost', label: '丢失' },
  { value: 'scrapped', label: '报废' }
]

// 表单验证规则（先定义基础规则）
const formRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '请输入设备型号', trigger: 'blur' }
  ],
  serial_number: [
    { required: true, message: '请输入序列号', trigger: 'blur' }
    // 序列号唯一性验证将在后面添加
  ],
  brand: [
    { required: true, message: '请选择品牌', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择设备状态', trigger: 'change' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.device)

// 序列号唯一性验证（在isEdit定义之后）
const validateSerialNumber = async (rule: any, value: string, callback: any) => {
  if (!value) {
    callback()
    return
  }

  // 编辑模式下，如果序列号没有变化，不需要验证
  if (isEdit.value && props.device?.serial_number === value) {
    callback()
    return
  }

  try {
    // 这里应该调用API检查序列号是否存在
    // const exists = await deviceAPI.checkSerialNumber(value)
    // if (exists) {
    //   callback(new Error('序列号已存在，请使用其他序列号'))
    //   return
    // }
    callback()
  } catch (error) {
    console.error('验证序列号失败:', error)
    callback()
  }
}

// 添加序列号验证到规则中
formRules.serial_number.push({ validator: validateSerialNumber, trigger: 'blur' })

// 监听对话框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadFormData()
    loadCategories()
    loadOwnerOptions()
    currentStep.value = 0 // 重置步骤
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载表单数据
const loadFormData = () => {
  if (props.device) {
    // 编辑模式，填充现有数据
    console.log('🔧 编辑模式 - 接收到的设备数据:', props.device)
    console.log('🔧 技术规格原始数据:', {
      cpu: props.device.cpu,
      gpu: props.device.gpu,
      memory: props.device.memory,
      storage: props.device.storage,
      os: props.device.os,
      os_version: props.device.os_version
    })

    // 直接设置每个字段的值，确保响应式更新
    formData.id = props.device.id || ''
    formData.name = props.device.name || ''
    formData.model = props.device.model || ''
    formData.serial_number = props.device.serial_number || ''
    formData.brand = props.device.brand || ''
    formData.device_number = props.device.device_number || ''
    formData.category = props.device.category || ''
    formData.owner = props.device.owner || ''
    formData.status = props.device.status || 'in_stock'

    // 技术规格 - 直接赋值确保响应式更新
    formData.cpu = props.device.cpu || ''
    formData.gpu = props.device.gpu || ''
    formData.memory = props.device.memory || ''
    formData.storage = props.device.storage || ''
    formData.resolution = props.device.resolution || ''
    formData.screen_size = props.device.screen_size || ''
    formData.special_screen = props.device.special_screen || ''

    // 系统信息
    formData.os = props.device.os || ''
    formData.os_version = props.device.os_version || ''

    // 管理信息
    formData.purchase_price = props.device.purchase_price || undefined
    formData.purchase_date = props.device.purchase_date || ''
    formData.warranty_period = props.device.warranty_period || ''
    formData.special_notes = props.device.special_notes || ''

    console.log('🔧 表单数据加载后:', {
      cpu: formData.cpu,
      gpu: formData.gpu,
      memory: formData.memory,
      storage: formData.storage,
      os: formData.os,
      os_version: formData.os_version,
      purchase_price: formData.purchase_price,
      warranty_period: formData.warranty_period
    })
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    model: '',
    serial_number: '',
    brand: '',
    category: '',
    status: 'in_stock',
    cpu: '',
    gpu: '',
    memory: '',
    storage: '',
    resolution: '',
    screen_size: '',
    special_screen: '',
    os: '',
    os_version: '',
    purchase_price: undefined,
    purchase_date: '',
    warranty_period: '',
    special_notes: ''
  })
  formRef.value?.clearValidate()
}

// 字段名称映射
const getFieldDisplayName = (field: string): string => {
  const fieldNames: Record<string, string> = {
    name: '设备名称',
    model: '设备型号',
    serial_number: '序列号',
    brand: '品牌',
    category: '设备分类',
    status: '设备状态',
    cpu: 'CPU',
    gpu: 'GPU',
    memory: '内存',
    storage: '存储',
    resolution: '分辨率',
    screen_size: '屏幕尺寸',
    special_screen: '特殊屏幕',
    os: '操作系统',
    os_version: '系统版本',
    owner: '归属者',
    purchase_price: '采购价格',
    purchase_date: '采购日期',
    warranty_period: '保修期',
    special_notes: '特殊说明',
    image: '设备图片'
  }
  return fieldNames[field] || field
}

// 加载设备分类
const loadCategories = async () => {
  try {
    await deviceStore.fetchCategories()
    categories.value = deviceStore.categories
  } catch (error) {
    console.error('加载设备分类失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    console.log('🔧 开始提交设备表单')
    console.log('表单数据:', formData)
    console.log('是否编辑模式:', isEdit.value)
    console.log('用户认证状态:', authStore.isAuthenticated)
    console.log('用户权限信息:', {
      is_device_admin: authStore.user?.is_device_admin,
      is_super_admin: authStore.user?.is_super_admin,
      role: authStore.user?.role
    })

    // 前置权限检查
    if (!authStore.isAuthenticated) {
      ElMessage.error('请先登录后再操作')
      // 可以考虑跳转到登录页
      return
    }

    if (!authStore.user?.is_device_admin) {
      ElMessage.error('权限不足：只有设备管理员可以创建设备')
      return
    }

    await formRef.value?.validate()
    submitting.value = true

    // 处理日期格式
    const submitData = { ...formData }
    if (submitData.purchase_date) {
      // 如果是Date对象，转换为YYYY-MM-DD格式
      if (submitData.purchase_date instanceof Date) {
        submitData.purchase_date = submitData.purchase_date.toISOString().split('T')[0]
      }
    } else {
      // 确保空值为null而不是空字符串
      submitData.purchase_date = null
    }

    // 确保保修期字段不为null
    if (!submitData.warranty_period) {
      submitData.warranty_period = ''
    }

    if (isEdit.value && props.device) {
      // 编辑设备
      console.log('🔧 更新设备:', props.device.id)
      await deviceStore.updateDevice(props.device.id, submitData)
    } else {
      // 创建设备
      console.log('🔧 创建新设备')
      console.log('🔧 提交数据:', submitData)
      const result = await deviceStore.createDevice(submitData)
      console.log('✅ 设备创建成功:', result)
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('❌ 提交失败:', error)

    // 增强错误处理
    if (error.response?.status === 403) {
      ElMessage.error('权限不足：只有设备管理员可以创建设备')
    } else if (error.response?.status === 401) {
      ElMessage.error('身份认证失败，请重新登录')
      // 清除认证信息并跳转到登录页
      authStore.clearAuth()
      // router.push('/login') // 如果需要自动跳转
    } else if (error.response?.data?.error) {
      ElMessage.error(error.response.data.error)
    } else if (error.response?.data?.detail) {
      ElMessage.error(error.response.data.detail)
    } else if (error.response?.data) {
      // 处理字段验证错误
      const errorData = error.response.data
      const errorMessages = []

      for (const [field, messages] of Object.entries(errorData)) {
        if (Array.isArray(messages)) {
          const fieldName = getFieldDisplayName(field)
          errorMessages.push(`${fieldName}: ${messages.join(', ')}`)
        }
      }

      if (errorMessages.length > 0) {
        ElMessage.error(`表单验证失败：${errorMessages.join('; ')}`)
      } else {
        ElMessage.error('操作失败，请稍后重试')
      }
    } else {
      ElMessage.error('操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 步骤控制方法
const nextStep = async () => {
  // 验证当前步骤的必填字段
  if (currentStep.value === 0) {
    // 验证基础信息
    try {
      await formRef.value?.validateField(['name', 'model', 'serial_number', 'brand', 'status'])
      currentStep.value++
    } catch (error) {
      ElMessage.warning('请完善基础信息中的必填字段')
    }
  } else if (currentStep.value === 1) {
    // 技术规格步骤，无必填字段，直接进入下一步
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 加载归属者选项
const loadOwnerOptions = async () => {
  try {
    // 根据用户权限选择合适的API
    let response
    if (authStore.user?.is_device_admin) {
      // 管理员可以访问完整的用户列表
      response = await authAPI.getUserList({ page_size: 100 })
    } else {
      // 设备拥有者使用受限的归属者选项API
      response = await authAPI.getDeviceOwnerOptions({ page_size: 100 })
    }
    ownerOptions.value = response.results || []
  } catch (error) {
    console.error('加载归属者选项失败:', error)
    // 如果是权限错误，给出友好提示
    if (error.response?.status === 403) {
      console.warn('权限不足，无法加载归属者选项')
      ownerOptions.value = []
    }
  }
}

// 加载配置选项
const loadConfigOptions = async () => {
  try {
    const response = await deviceAPI.getConfigOptions()
    configOptions.value = response

    // 设置默认值（如果是新增模式且字段为空）
    if (!props.device) {
      if (!formData.brand && response.brands.length > 0) {
        formData.brand = response.brands[0].value
      }
      if (!formData.category && response.categories.length > 0) {
        formData.category = response.categories[0].value
      }
      if (!formData.special_screen && response.special_screens.length > 0) {
        formData.special_screen = response.special_screens[0].value
      }
      if (!formData.os && response.operating_systems.length > 0) {
        formData.os = response.operating_systems[0].value
      }
    }

    console.log('🔧 配置选项加载成功:', response)
  } catch (error) {
    console.error('加载配置选项失败:', error)
  }
}

// 组件挂载时加载分类数据
onMounted(() => {
  loadCategories()
  loadOwnerOptions()
  loadConfigOptions()
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 10px;
}

.required-field {
  color: #f56c6c;
}

:deep(.el-form) {
  padding-right: 20px;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #409eff;
}

:deep(.el-steps) {
  margin-bottom: 20px;
}

:deep(.el-step__title) {
  font-size: 14px;
}

:deep(.el-step__description) {
  font-size: 12px;
}
</style>
