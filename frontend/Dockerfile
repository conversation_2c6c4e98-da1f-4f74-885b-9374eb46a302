# 开发环境 Vue.js Dockerfile - 企业级优化版本
FROM node:18-alpine

# 安装必要的系统工具（用于健康检查）
RUN apk add --no-cache wget curl

# 设置工作目录
WORKDIR /app

# 复制package文件（利用Docker缓存层）
COPY package*.json ./

# 配置npm使用国内镜像源并安装依赖
RUN npm config set registry https://registry.npmmirror.com && \
    npm ci --only=production=false && \
    npm cache clean --force

# 复制项目文件
COPY . .

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=development
ENV DOCKER_ENV=true
ENV VITE_HOST=0.0.0.0
ENV VITE_PORT=3000

# 健康检查脚本
HEALTHCHECK --interval=15s --timeout=5s --start-period=90s --retries=5 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev"]
