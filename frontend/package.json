{"name": "device-management-frontend", "version": "1.0.0", "type": "module", "description": "设备管理平台前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tanstack/vue-table": "^8.10.7", "@vee-validate/rules": "^4.11.8", "axios": "^1.6.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-plus": "^2.4.2", "pinia": "^2.1.7", "vee-validate": "^4.11.8", "vue": "^3.3.8", "vue-echarts": "^6.7.3", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.16", "eslint": "^9.0.0", "eslint-plugin-vue": "^9.18.1", "postcss": "^8.4.31", "sass": "^1.69.5", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^6.0.0", "vue-tsc": "^2.0.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}