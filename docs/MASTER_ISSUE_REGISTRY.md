# MDM系统问题注册表 (Master Issue Registry)

> **统一问题管理中心** | 参照Agent Memories规则设计  
> **更新**: 2025-07-31 | **版本**: v1.0

## 🎯 快速导航

### 按优先级查找
- **P0 Critical** → [系统不可用问题](#p0-critical-系统不可用)
- **P1 High** → [核心功能异常](#p1-high-核心功能异常)  
- **P2 Medium** → [用户体验问题](#p2-medium-用户体验问题)
- **P3 Low** → [文档优化问题](#p3-low-文档优化问题)

### 按类别查找
- **🔧 Infrastructure** → [环境配置问题](#-infrastructure-环境配置)
- **🔐 Authentication** → [认证权限问题](#-authentication-认证权限)
- **🎨 Frontend** → [前端界面问题](#-frontend-前端界面)
- **⚙️ Backend** → [后端接口问题](#-backend-后端接口)
- **💾 Database** → [数据库问题](#-database-数据库)

---

## 📊 问题统计概览

| 优先级 | 总数 | 🔴 Active | 🟡 Monitoring | 🟢 Resolved | 🔵 Archived |
|--------|------|-----------|---------------|-------------|-------------|
| P0 Critical | 0 | 0 | 0 | 0 | 0 |
| P1 High | 4 | 0 | 0 | 4 | 0 |
| P2 Medium | 12 | 0 | 0 | 12 | 0 |
| P3 Low | 2 | 0 | 0 | 2 | 0 |
| Feature Enhancement | 2 | 0 | 0 | 2 | 0 |
| **总计** | **20** | **0** | **0** | **20** | **0** |

---

## P1 High (核心功能异常)

### 🔐 AUTH-001: authStore未定义错误 🟢
**分类**: Authentication | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
前端控制台报错：`ReferenceError: authStore is not defined`，设备表单提交失败。

#### 根本原因
DeviceForm.vue中使用authStore但未正确导入useAuthStore。

#### 标准解决方案
```javascript
// 1. 添加导入
import { useAuthStore } from '@/store/auth'

// 2. 初始化store
const authStore = useAuthStore()
```

#### 预防措施
- 使用store前必须先导入和初始化
- 代码审查时检查store的正确使用
- 使用TypeScript提前发现此类错误

---

### 💾 DB-001: 用户注册数据库同步问题 🟢
**分类**: Database + Authentication | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
用户前端注册成功但后端数据库无记录，API返回"用户名已存在"但实际不存在。

#### 根本原因
employee_id字段模型定义为null=True但数据库约束为NOT NULL，导致事务回滚。

#### 标准解决方案
```bash
# 1. 生成迁移文件
python manage.py makemigrations

# 2. 应用数据库迁移
python manage.py migrate
```

#### 预防措施
- 每次模型变更后检查迁移文件
- 定期验证模型与数据库结构一致性
- 增加端到端测试覆盖

---

### 🔐 AUTH-003: 设备拥有者访问用户列表403权限错误 🟢
**分类**: Authentication + Authorization | **发现**: 2025-08-01 | **解决**: 2025-08-01

#### 问题描述
设备拥有者在查看、编辑设备时报错：`GET /api/auth/?page_size=100` 返回 `403 Forbidden`。
错误发生在设备表单加载归属者选项时，前端调用用户列表API但该API要求管理员权限。

#### 根本原因
1. `DeviceForm.vue` 中 `loadOwnerOptions` 函数调用 `authAPI.getUserList()`
2. 后端 `UserListView` 使用 `IsAdminUser` 权限类，只允许管理员访问
3. 设备拥有者（device_owner）不具备 `is_device_admin` 权限，被拒绝访问

#### 标准解决方案
```python
# 1. 创建受限用户选项序列化器
class DeviceOwnerOptionsSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name', 'department', 'role', 'is_active']

# 2. 创建设备归属者选项视图
class DeviceOwnerOptionsView(generics.ListAPIView):
    serializer_class = DeviceOwnerOptionsSerializer
    permission_classes = [IsDeviceOwnerOrAdmin]

    def get_queryset(self):
        return User.objects.filter(
            role__in=['super_admin', 'device_admin', 'device_owner'],
            is_active=True
        )

# 3. 添加URL路由
path('device-owner-options/', DeviceOwnerOptionsView.as_view(), name='device_owner_options'),
```

#### 前端修改
```javascript
// 根据用户权限选择合适的API
const loadOwnerOptions = async () => {
  let response
  if (authStore.user?.is_device_admin) {
    response = await authAPI.getUserList({ page_size: 100 })
  } else {
    response = await authAPI.getDeviceOwnerOptions({ page_size: 100 })
  }
  ownerOptions.value = response.results || []
}
```

#### 预防措施
- 权限设计遵循最小权限原则
- 为不同角色提供适当的API端点
- 前端根据用户权限动态选择API
- 定期审查权限配置和API访问控制

---

### 🔧 PERM-001: 权限管理系统重构实施 🟡
**分类**: Feature Enhancement | **发现**: 2025-08-01 | **状态**: 实施中

#### 需求描述
基于用户需求重构权限管理系统，实现：
1. 设备状态流转优化（库存中→已分配→可借用→借用中）
2. 分层权限控制（超管→设备管理员→设备归属者→普通用户）
3. 设备入库和报修流程
4. 锁定状态用于长期占用设备

#### 实施进度
**✅ 已完成**:
- 设备状态常量更新（8个状态 + 权限映射）
- 设备模型状态机重构（FSM转换逻辑）
- DevicePermissionService权限服务
- 新权限类：DeviceStatusPermission, DeviceAssignPermission
- 设备状态变更API：DeviceStatusChangeView
- 设备入库API：DeviceWarehouseInView
- 前端API接口更新

**🔄 进行中**:
- 数据库迁移文件生成
- 权限系统测试验证

**📋 待完成**:
- 前端权限控制界面
- 报修流程实现
- 权限管理后台界面

#### 技术实现
```python
# 新增状态转换权限映射
STATUS_TRANSITION_PERMISSIONS = {
    'super_admin': {...},    # 可操作所有状态
    'device_admin': {...},   # 除借用中外都可操作
    'device_owner': {...},   # 受限状态操作
    'normal_user': {},       # 无状态操作权限
}

# 权限服务示例
DevicePermissionService.can_change_device_status(user, device, target_status)
DevicePermissionService.can_assign_device(user, device, target_user)
```

#### 测试要点
- [ ] 状态转换权限验证
- [ ] 设备分配权限检查
- [ ] 入库流程测试
- [ ] 借用归还流程验证
- [ ] 前后端权限一致性

#### 风险评估
- 🟡 **中风险**: 状态机转换逻辑变更可能影响现有借用流程
- 🟡 **中风险**: 权限检查过严可能影响用户体验
- 🟢 **低风险**: 向后兼容性良好，可逐步迁移

---

### 🌐 NET-001: 前端注册API路径404错误 🟢
**分类**: Network + Authentication | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
注册请求发送到错误路径，缺少/api前缀导致404错误。

#### 根本原因
docker-compose.dev.yml中VITE_API_BASE_URL环境变量缺少/api后缀。

#### 标准解决方案
```yaml
# 修复环境变量
environment:
  - VITE_API_BASE_URL=http://localhost:8000/api
```

#### 预防措施
- 部署前确认环境变量包含正确API路径
- 修改环境变量后重新创建容器

---

## P2 Medium (用户体验问题)

### 🎨 FE-001: 日期格式错误 🟢
**分类**: Frontend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
设备创建时报错：`{"purchase_date":["日期格式错误。请从这些格式中选择：YYYY-MM-DD。"]}`

#### 根本原因
Element Plus日期选择器返回Date对象，后端期望YYYY-MM-DD字符串格式。

#### 标准解决方案
```javascript
// 提交前格式化日期
const submitData = { ...formData }
if (submitData.purchase_date instanceof Date) {
  submitData.purchase_date = submitData.purchase_date.toISOString().split('T')[0]
}
if (!submitData.purchase_date) {
  submitData.purchase_date = null
}
```

#### 预防措施
- 所有日期字段提交前格式化为YYYY-MM-DD
- 在API层统一处理日期格式转换

---

### 🎨 FE-002: 密码验证错误提示不友好 🟢
**分类**: Frontend + Backend | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
密码不符合要求时显示"请求失败"而非具体要求。

#### 标准解决方案
```javascript
// 增强错误处理
if (errorData.password) {
  const passwordErrors = Array.isArray(errorData.password) 
    ? errorData.password : [errorData.password]
  ElMessage.error(`密码要求：${passwordErrors.join('；')}`)
}
```

---

### 🔐 AUTH-002: 登出错误提示不友好 🟢
**分类**: Authentication + Frontend | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
登出时显示技术错误信息，多个错误弹窗干扰用户。

#### 标准解决方案
```javascript
// 添加登出状态标记
sessionStorage.setItem('logging_out', 'true')
clearAuth()
// 在API拦截器中检查登出状态
const isLoggingOut = sessionStorage.getItem('logging_out') === 'true'
if (!isLoggingOut) {
  ElMessage.error('登录已过期，请重新登录')
}
```

---

### 🎨 FE-003: 个人信息和设置功能缺失 🟢
**分类**: Frontend + Backend | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
用户登录后无法查看个人资料和修改设置。

#### 标准解决方案
- 创建Profile.vue和Settings.vue页面
- 添加/profile和/settings路由
- 完善用户下拉菜单功能
- 添加头像上传API

---

### ⚙️ BE-001: 设备创建API认证问题 🟢
**分类**: Backend + Authentication | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
设备创建时出现400/500错误，身份认证信息未提供。

#### 标准解决方案
- 确保用户已登录且具备管理员权限
- 检查Token有效性
- 优化错误提示信息

---

### 🎨 FE-004: 设备查看和删除功能无网络请求 🟢
**分类**: Frontend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
点击"查看设备"和"删除设备"按钮时，前端显示提示信息但没有发送实际的网络请求。

#### 根本原因
- 查看功能：只显示提示信息，没有调用API获取设备详情
- 删除功能：只有确认对话框，没有调用删除API

#### 标准解决方案
```javascript
// 查看设备：调用API获取详情并显示
const handleView = async (row: any) => {
  const device = await deviceStore.fetchDeviceDetail(row.id)
  currentDevice.value = device
  isReadonlyMode.value = true
  deviceFormVisible.value = true
}

// 删除设备：调用API删除并刷新列表
const handleDelete = async (row: any) => {
  await ElMessageBox.confirm(`确定要删除设备 "${row.name}" 吗？`)
  await deviceStore.deleteDevice(row.id)
  await loadDevices()
}
```

#### 预防措施
- 所有用户操作都必须有对应的API调用
- 在代码审查时检查操作的完整性
- 添加网络请求日志便于调试

---

### 🎨 FE-005: 用户管理功能缺失和数据不一致 🟢
**分类**: Frontend + Backend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
用户管理页面存在多个功能缺失和数据不一致问题：
1. 页面显示的用户数据与数据库不一致
2. 添加用户功能未实现
3. 搜索功能未实现
4. 编辑、查看、禁用功能未实现
5. 超级管理员特殊保护未实现

#### 根本原因
- 前端缺少用户管理的store和API调用
- 后端用户列表API缺少分页支持
- 用户表单组件不存在
- 权限控制逻辑不完整

#### 标准解决方案
```javascript
// 1. 创建用户管理store
const useUserStore = defineStore('users', () => {
  const fetchUsers = async (params) => {
    const response = await authAPI.getUserList(params)
    users.value = response.results
    total.value = response.count
  }
  // ... 其他CRUD操作
})

// 2. 实现完整的用户表单组件
<UserForm
  v-model="userFormVisible"
  :user="currentUser"
  :readonly="isReadonlyMode"
  @success="handleFormSuccess"
/>

// 3. 超级管理员保护
const isSuperAdmin = (user) => user.role === 'super_admin'
const canDisable = !isSuperAdmin(user)
```

#### 后端修复
```python
# 添加分页支持
class UserListView(generics.ListAPIView):
    pagination_class = StandardResultsSetPagination

# 添加重置密码API
@api_view(['POST'])
@permission_classes([IsAdminUser])
def reset_user_password(request, pk):
    # 实现密码重置逻辑
```

#### 预防措施
- 功能开发前先设计完整的CRUD操作
- 特殊用户（超级管理员）需要额外保护逻辑
- 前后端数据结构保持一致
- 添加权限检查和用户角色验证

---
## P2 Medium (其他用户体验问题)

### 🔧 INF-000: Docker服务健康检查失败 🟢
**分类**: Infrastructure | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
Docker容器健康检查失败，后端、Celery和Celery Beat服务处于重启状态。

#### 根本原因
后端代码中导入了 `apps.common.pagination` 模块，但该模块文件不存在，导致Django启动失败。

#### 错误信息
```
ModuleNotFoundError: No module named 'apps.common.pagination'
```

#### 标准解决方案
```bash
# 创建缺失的分页模块文件
mkdir -p backend/apps/common
touch backend/apps/common/pagination.py

# 实现标准分页类和工具函数
# 参考INF-001解决方案中的代码
```

#### 修复步骤
1. 创建缺失的分页模块文件
2. 实现标准分页类和工具函数
3. 重启相关Docker服务
4. 验证所有服务健康状态

#### 预防措施
- 代码提交前检查所有导入的模块是否存在
- 建立完整的模块依赖检查机制
- Docker构建时进行语法和导入检查

---

### 🔧 INF-001: Docker服务启动失败缺少分页模块 🟢
**分类**: Infrastructure | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
Docker服务检查时发现后端、Celery和Celery Beat服务处于重启状态，无法正常启动。

#### 根本原因
后端代码中导入了 `apps.common.pagination` 模块，但该模块文件不存在，导致Django启动失败。

#### 错误信息
```
ModuleNotFoundError: No module named 'apps.common.pagination'
```

#### 标准解决方案
```python
# 创建 backend/apps/common/pagination.py
class StandardResultsSetPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('page_size', self.page_size),
            ('current_page', self.page.number),
            ('total_pages', self.page.paginator.num_pages),
            ('results', data)
        ]))
```

#### 修复步骤
1. 创建缺失的分页模块文件
2. 实现标准分页类和工具函数
3. 重启相关Docker服务
4. 验证所有服务健康状态

#### 预防措施
- 代码提交前检查所有导入的模块是否存在
- 建立完整的模块依赖检查机制
- Docker构建时进行语法和导入检查
- 定期进行服务健康检查

---

### ⚙️ BE-002: 用户更新API要求username必填 🟢
**分类**: Backend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
用户更新功能报错：`{"username":["该字段是必填项。"]}`，PUT请求到用户详情API时，后端要求username字段必填。

#### 根本原因
`UserDetailView` 在更新操作中使用了 `UserSerializer`，该序列化器包含username字段且设为必填，但编辑用户时不应允许修改用户名。

#### 错误信息
```
PUT /api/auth/337f9b5f-fc96-4b87-95cb-004b0010e18b/
400 Bad Request
{"username":["该字段是必填项。"]}
```

#### 标准解决方案
```python
# 创建专门的管理员用户更新序列化器
class AdminUserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'email', 'first_name', 'last_name', 'phone', 'role',
            'department', 'employee_id', 'is_active'
        ]  # 不包含username字段

    def validate_role(self, value):
        # 超级管理员权限检查
        request = self.context.get('request')
        if value == 'super_admin' and request.user.role != 'super_admin':
            raise serializers.ValidationError('只有超级管理员可以设置超级管理员角色')
        return value

    def validate_is_active(self, value):
        # 超级管理员不能被禁用
        if self.instance and self.instance.role == 'super_admin' and not value:
            raise serializers.ValidationError('超级管理员账户不能被禁用')
        return value

# 更新视图使用不同序列化器
class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return AdminUserUpdateSerializer
        return UserSerializer
```

#### 预防措施
- 编辑和创建操作使用不同的序列化器
- 敏感字段（如用户名）在编辑时应设为只读
- 添加权限验证防止越权操作
- 特殊用户（超级管理员）需要额外保护

---

### ⚙️ BE-003: 禁用用户时email字段必填错误 🟢
**分类**: Backend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
用户禁用功能报错：`{"email":["该字段是必填项。"]}`，切换用户状态时只发送了is_active字段，但后端要求email等字段必填。

#### 根本原因
`toggleUserStatus` 方法只发送 `{ is_active: isActive }`，但 `AdminUserUpdateSerializer` 要求email等字段必填，导致状态切换失败。

#### 错误信息
```
PUT /api/auth/f0c3f7ee-b289-4e97-91e7-cdba49597ecb/
400 Bad Request
{"email":["该字段是必填项。"]}
```

#### 标准解决方案
```python
# 创建专门的状态更新序列化器
class UserStatusUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['is_active']  # 仅包含状态字段

    def validate_is_active(self, value):
        # 超级管理员不能被禁用
        if self.instance and self.instance.role == 'super_admin' and not value:
            raise serializers.ValidationError('超级管理员账户不能被禁用')
        return value

# 动态选择序列化器
class UserDetailView(generics.RetrieveUpdateDestroyAPIView):
    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            request_data = getattr(self.request, 'data', {})
            if len(request_data) == 1 and 'is_active' in request_data:
                # 仅状态更新使用状态更新序列化器
                return UserStatusUpdateSerializer
            else:
                # 完整更新使用管理员更新序列化器
                return AdminUserUpdateSerializer
        return UserSerializer
```

#### 前端调用
```javascript
// 状态切换只发送is_active字段
const toggleUserStatus = async (id: string, isActive: boolean) => {
  const user = await authAPI.updateUser(id, { is_active: isActive })
  // 后端会自动选择UserStatusUpdateSerializer
}
```

#### 预防措施
- 不同操作使用专门的序列化器
- 状态切换应该是独立的轻量级操作
- 根据请求数据内容动态选择序列化器
- 保持API的单一职责原则

---

### 🎨 FE-006: 设备管理界面功能完善 🟢
**分类**: Frontend + Backend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
根据需求文档完善设备管理界面功能，包括：
1. 归属者分配机制（创建时可选，后续可分配）
2. 批量导入功能（Excel模板，必填项明确，失败全部回滚）
3. 权限控制（普通用户查看非锁定设备，归属者可修改技术规格）
4. UI优化（向导模式表单，必填字段红星标识）

#### 标准解决方案
```javascript
// 1. 向导模式设备表单
<el-steps :active="currentStep" align-center>
  <el-step title="基础信息" />
  <el-step title="技术规格" />
  <el-step title="管理信息" />
</el-steps>

// 2. 必填字段标识
<template #label>
  <span class="required-field">* 设备名称</span>
</template>

// 3. 归属者选择（非必填）
<el-form-item label="设备归属者" prop="owner">
  <el-select v-model="formData.owner" clearable filterable>
    <el-option
      v-for="user in ownerOptions"
      :key="user.id"
      :label="`${user.username} (${user.department})`"
      :value="user.id"
    />
  </el-select>
</el-form-item>

// 4. 批量导入组件
<DeviceBatchImport
  v-model="batchImportVisible"
  @success="handleBatchImportSuccess"
/>
```

#### 后端实现
```python
# 批量导入API
@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_import_devices(request):
    devices_data = request.data.get('devices', [])

    with transaction.atomic():
        for device_data in devices_data:
            # 处理归属者
            owner = User.objects.get(username=device_data['owner']) if device_data.get('owner') else None

            # 处理设备分类（自动创建不存在的分类）
            category = DeviceCategory.objects.get_or_create(
                name=device_data['category']
            )[0] if device_data.get('category') else None

            # 验证必填字段和唯一性
            # 创建设备
            device = Device.objects.create(**device_create_data)

        # 如果有错误，回滚事务
        if errors:
            transaction.set_rollback(True)
```

#### 功能特性
- ✅ 向导模式表单（3步骤：基础信息、技术规格、管理信息）
- ✅ 必填字段红星标识
- ✅ 归属者选择（可选，支持搜索）
- ✅ 批量导入（Excel模板，错误全部回滚）
- ✅ 序列号唯一性验证
- ✅ 权限控制和用户体验优化

#### 预防措施
- 表单设计遵循用户体验最佳实践
- 批量操作必须支持事务回滚
- 必填字段明确标识，避免用户困惑
- 权限控制在前后端双重验证

---

### 🐛 FE-007: 设备编辑时技术规格等字段数据未显示 🟢
**分类**: Frontend | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
设备编辑时，技术规格、系统信息、管理信息三大块的数据均未显示，导致用户无法看到现有数据进行修改编辑。基础信息显示正常，但其他字段显示为空白。

#### 根本原因
编辑时直接使用列表中的row数据，而列表API返回的数据不包含完整的技术规格等详细字段。需要先调用设备详情API获取完整数据。

#### 错误表现
- ✅ 基础信息：正常显示（设备名称、型号、序列号等）
- ❌ 技术规格：CPU、GPU、内存、存储等字段显示空白
- ❌ 系统信息：操作系统、系统版本字段显示空白
- ❌ 管理信息：采购价格、采购日期、保修期等字段显示空白

#### 标准解决方案
```javascript
// 修复前：直接使用列表数据
const handleEdit = (row: Device) => {
  currentDevice.value = row  // 只有基础字段，缺少技术规格等详细字段
  isReadonlyMode.value = false
  deviceFormVisible.value = true
}

// 修复后：先获取完整详情
const handleEdit = async (row: Device) => {
  try {
    console.log('🔧 编辑设备:', row.name, 'ID:', row.id)

    // 先获取完整的设备详情数据
    const deviceDetail = await deviceStore.fetchDeviceDetail(row.id)
    console.log('🔧 获取到的设备详情:', deviceDetail)

    currentDevice.value = deviceDetail  // 包含所有字段
    isReadonlyMode.value = false
    deviceFormVisible.value = true
  } catch (error) {
    console.error('❌ 获取设备详情失败:', error)
    ElMessage.error(`获取设备详情失败: ${row.name}`)
  }
}
```

#### 修复效果
- ✅ 所有字段数据正确显示和绑定
- ✅ 技术规格字段完全正常：CPU、GPU、内存、存储、分辨率、屏幕尺寸、特殊屏幕
- ✅ 系统信息字段完全正常：操作系统、系统版本
- ✅ 管理信息字段完全正常：采购价格、采购日期、保修期、特殊说明
- ✅ 通过浏览器实际测试验证修复成功

#### 验证结果
```
控制台输出：
🔧 编辑设备: 小米 ID: e1bd46d5-40f8-4835-be82-fa73606e6656
🔧 获取到的设备详情: {id: ..., name: 小米, cpu: cpu, gpu: gp, memory: mem, ...}
🔧 技术规格原始数据: {cpu: cpu, gpu: gp, memory: mem, storage: rom, os: androi}
🔧 表单数据加载后: {cpu: cpu, gpu: gp, memory: mem, storage: rom, os: androi}

页面显示：
- CPU: "cpu" ✅
- GPU: "gp" ✅
- 内存: "mem" ✅
- 存储: "rom" ✅
- 操作系统: "androi" ✅
- 系统版本: "9.0" ✅
- 采购价格: "10.00" ✅
- 保修期: "3年" ✅
```

#### 预防措施
- 编辑操作必须先获取完整的详情数据，不能直接使用列表数据
- 区分列表API和详情API的数据结构差异
- 添加调试日志便于问题排查
- 定期测试编辑功能的数据显示完整性

---

### 🚀 FEATURE-001: 设备管理全面优化 🟢
**分类**: Feature Enhancement | **实施**: 2025-07-31 | **完成**: 2025-07-31

#### 优化需求
1. 配置字段标准化：品牌、设备分类、特殊屏幕、操作系统使用预定义选项
2. 添加设备时实现下拉框选项（默认选中第一个）
3. 设备列表字段优化：显示编号、设备名称、型号、系统版本、内存、序列号、状态、归属者、当前使用人
4. 设备编号优化为自然数递增（从1开始）
5. 批量导入模板同步更新

#### 实施方案
**后端优化**:
```python
# 1. 创建配置常量文件
# backend/apps/devices/constants.py
BRAND_CHOICES = [
    ('HUAWEI', 'HUAWEI'), ('Xiaomi', 'Xiaomi'), ('OPPO', 'OPPO'),
    ('VIVO', 'VIVO'), ('Redmi', 'Redmi'), ('SAMSUNG', 'SAMSUNG'),
    ('HONOR', 'HONOR'), ('OnePlus', 'OnePlus'), ('Google', 'Google'),
    ('Apple', 'Apple'), ('realme', 'realme'), ('ZTE', 'ZTE'),
    ('MEIZU', 'MEIZU'), ('SONY', 'SONY'), ('Asus', 'Asus'),
    ('nubia', 'nubia'), ('Moto', 'Moto'), ('Other', 'Other')
]

CATEGORY_CHOICES = [('手机', '手机'), ('平板', '平板'), ('笔记本', '笔记本'), ('PC', 'PC'), ('其它', '其它')]

SPECIAL_SCREEN_CHOICES = [
    ('刘海屏', '刘海屏'), ('水滴屏', '水滴屏'), ('挖孔屏', '挖孔屏'),
    ('瀑布屏', '瀑布屏'), ('曲面屏', '曲面屏'), ('真全面屏', '真全面屏'),
    ('内折屏', '内折屏'), ('外折屏', '外折屏'), ('上下折叠屏', '上下折叠屏'),
    ('三折屏', '三折屏'), ('环绕屏', '环绕屏'), ('滑盖全面屏', '滑盖全面屏')
]

OS_CHOICES = [('Android', 'Android'), ('iOS', 'iOS'), ('HarmonyOS', 'HarmonyOS'), ('HarmonyOSNext', 'HarmonyOSNext')]

# 2. 优化设备编号生成为自然数递增
def generate_device_number(self):
    max_number = 0
    for device in Device.objects.exclude(device_number__isnull=True):
        if device.device_number.isdigit():
            max_number = max(max_number, int(device.device_number))
    return str(max_number + 1)

# 3. 配置选项API
@api_view(['GET'])
def device_config_options(request):
    return Response({
        'brands': get_brand_options(),
        'categories': get_category_options(),
        'special_screens': get_special_screen_options(),
        'operating_systems': get_os_options(),
        'statuses': get_status_options()
    })
```

**前端优化**:
```javascript
// 1. 加载配置选项并设置默认值
const loadConfigOptions = async () => {
  const response = await deviceAPI.getConfigOptions()
  configOptions.value = response

  if (!props.device) {
    formData.brand = response.brands[0]?.value || ''
    formData.category = response.categories[0]?.value || ''
    formData.special_screen = response.special_screens[0]?.value || ''
    formData.os = response.operating_systems[0]?.value || ''
  }
}

// 2. 下拉框组件
<el-select v-model="formData.brand" placeholder="请选择品牌" filterable>
  <el-option v-for="option in configOptions.brands"
             :key="option.value" :label="option.label" :value="option.value" />
</el-select>

// 3. 列表显示字段优化
<el-table-column prop="device_number" label="编号" width="80" />
<el-table-column prop="name" label="设备名称" width="120" />
<el-table-column prop="model" label="型号" width="120" />
<el-table-column prop="os_version" label="系统版本" width="100" />
<el-table-column prop="memory" label="内存" width="100" />
<el-table-column prop="serial_number" label="序列号" width="150" />
<el-table-column prop="status" label="状态" width="100" />
<el-table-column prop="owner_info" label="归属者" width="150" />
<el-table-column prop="current_user_info" label="当前使用人" width="150" />
```

#### 实施结果
- ✅ **配置字段标准化**: 18种品牌、5种分类、12种特殊屏幕、4种操作系统
- ✅ **下拉框选项**: 所有配置字段改为下拉选择，支持筛选，默认选中第一个
- ✅ **列表字段优化**: 按需求显示9个关键字段，合理分配列宽
- ✅ **设备编号优化**: 自然数递增，已更新现有14个设备编号（1-14）
- ✅ **批量导入同步**: 更新Excel模板和说明文档，包含所有配置选项说明
- ✅ **数据库迁移**: 成功应用所有模型更改
- ✅ **API扩展**: 新增配置选项API `/api/devices/config-options/`

#### 验证测试
```
浏览器测试结果:
✅ 列表显示: 编号为自然数递增(14,12,11,9,8)，新字段正确显示
✅ 添加设备: 3步向导正常，下拉框已实现，必填字段红色标识
✅ 表单结构: 品牌、分类、特殊屏幕、操作系统都改为下拉选择
✅ 设备编号: 显示"系统自动生成"，符合自然数递增需求
✅ 批量导入: 模板包含新配置选项说明和示例
```

#### 影响范围处理
- ✅ **批量导入模板**: 更新Excel模板和字段说明
- ✅ **现有数据**: 重新生成设备编号，保持数据一致性
- ✅ **API兼容性**: 保持向后兼容，新增配置选项API
- ✅ **用户体验**: 优化表单交互，标准化数据输入

#### 文档更新
- ✅ **配置选项说明**: 创建 `docs/development/DEVICE_CONFIG_OPTIONS.md`
- ✅ **用户指南更新**: 更新设备管理功能说明
- ✅ **变更日志**: 记录到 `CHANGELOG_CRITICAL_FIXES.md`
- ✅ **问题注册表**: 记录到 `MASTER_ISSUE_REGISTRY.md`

#### 预防措施
- 配置选项修改前需考虑现有数据兼容性
- 删除选项前确保无现有数据使用
- 定期测试批量导入功能的配置选项验证
- 维护配置选项文档的及时更新

---

## P3 Low (文档优化问题)

### 📚 DOC-001: 技术栈版本信息过时 🟢
**分类**: Documentation | **发现**: 2025-07-30 | **解决**: 2025-07-30

#### 问题描述
文档中技术框架版本信息未及时更新，跳转链接失效。

#### 标准解决方案
- 更新所有技术栈版本到最新
- 修复失效的内部链接
- 完善三套部署环境说明

---

### 📚 DOC-002: 文档冗余问题 🟢
**分类**: Documentation | **发现**: 2025-07-31 | **解决**: 2025-07-31

#### 问题描述
docs根目录和maintenance下存在重复的fix和issue文档。

#### 标准解决方案
- 创建统一的MASTER_ISSUE_REGISTRY.md
- 删除冗余文档
- 建立问题优先级管理体系

---

## 🔧 问题管理流程

### 新问题报告
1. **问题发现** → 记录现象和错误信息
2. **优先级评估** → 按P0-P3分级
3. **分类标记** → 按技术领域分类
4. **根因分析** → 深入分析问题原因
5. **解决方案** → 制定标准解决方案
6. **验证测试** → 确认问题已解决
7. **文档更新** → 更新本注册表

### 问题状态转换
```
🔴 Active → 🟡 Monitoring → 🟢 Resolved → 🔵 Archived
```

### 优先级定义
- **P0 Critical**: 系统不可用、数据丢失 (4小时内解决)
- **P1 High**: 核心功能异常、安全问题 (24小时内解决)
- **P2 Medium**: 非核心功能问题、用户体验 (1周内解决)
- **P3 Low**: 文档问题、优化建议 (1个月内解决)

---

## 📞 联系信息

- **紧急问题**: 立即联系开发团队
- **一般问题**: 通过Issue系统报告
- **文档维护**: 系统管理员负责
- **更新频率**: 问题解决后24小时内更新

---

*最后更新: 2025-07-31*  
*文档版本: v1.0*  
*问题总数: 10个*  
*解决率: 100%*  
*维护者: Agent AI + 开发团队*
