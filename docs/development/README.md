# 📚 开发文档目录

**版本**: v2.0.0
**更新时间**: 2025年7月30日
**维护者**: 开发团队

---

## 📋 文档概览

本目录包含 MDM 设备管理系统的开发相关指南和规范，为开发者提供完整的开发支持。

## 🚀 快速开始

### 核心开发指南
- [开发指南](DEV-GUIDE.md) ⭐ - 完整的开发环境搭建、启动流程和开发规范
- [编码规范](coding-standards.md) - Python/Django和Vue.js/TypeScript代码质量标准

### 相关文档
- [快速启动指南](../QUICK_START_GUIDE.md) - 项目快速体验
- [环境配置指南](../../ENVIRONMENT_GUIDE.md) - 详细的环境配置说明

## 🛠️ 开发工具链

### 推荐开发环境
- **Node.js**: 20.x LTS
- **Python**: 3.11+
- **Docker**: Desktop 20.10+
- **IDE**: VS Code / PyCharm Professional

*详细的技术栈信息和版本要求请查看 [开发指南](DEV-GUIDE.md)*

## ⚡ 快速启动

```bash
# 1. 启动基础服务（数据库、Redis）
scripts\dev\start-services.bat

# 2. 启动后端开发服务器
dev-backend.bat

# 3. 启动前端开发服务器
dev-frontend.bat
```

**访问地址**:
- 前端: http://localhost:5173
- 后端: http://localhost:8000
- API文档: http://localhost:8000/api/docs/

*详细的开发环境搭建步骤请查看 [开发指南](DEV-GUIDE.md)*

## 📚 相关文档

### 系统架构
- [技术架构](../architecture/technical_architecture.md) - 系统整体架构设计
- [API设计](../architecture/api-design.md) - RESTful API规范
- [数据库设计](../architecture/database-design.md) - 数据库结构设计

### 部署运维
- [部署指南](../deployment/README.md) - 生产环境部署
- [健康检查](../monitoring/HEALTH_CHECKS.md) - 容器健康监控
- [管理员配置](../ADMIN_SETUP_GUIDE.md) - 管理员账户配置

### 用户文档
- [用户手册](../user/user-guide.md) - 系统使用说明
- [个人设置](../user/profile_settings_guide.md) - 个人资料配置

## 🔧 开发支持

### 问题排查
- [问题管理](../maintenance/ISSUE_MANAGEMENT.md) - 问题跟踪和解决流程
- [健康检查](../monitoring/HEALTH_CHECKS.md) - 服务状态监控

### 开发工具
- **调试命令**: 详见 [开发指南](DEV-GUIDE.md) 中的调试章节
- **测试流程**: 详见 [编码规范](coding-standards.md) 中的测试标准

## 📞 技术支持

- **开发团队**: <EMAIL> / <EMAIL>
- **技术支持**: <EMAIL>
- **文档维护**: <EMAIL>

---

*最后更新: 2025-07-30*
*文档版本: v2.0.0*
