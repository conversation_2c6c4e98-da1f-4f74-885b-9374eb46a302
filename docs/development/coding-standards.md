# 💻 代码规范文档

**版本**: v1.0.0  
**更新时间**: 2025年7月28日  
**维护者**: 开发团队负责人  
**适用范围**: 设备管理平台项目

---

## 📋 目录

1. [总体原则](#总体原则)
2. [Python/Django规范](#pythondjango规范)
3. [Vue.js/TypeScript规范](#vuejstypescript规范)
4. [数据库规范](#数据库规范)
5. [Git提交规范](#git提交规范)
6. [代码审查标准](#代码审查标准)

---

## 🎯 总体原则

### 核心理念
1. **可读性优先**：代码应该易于理解和维护
2. **一致性**：团队内保持统一的编码风格
3. **简洁性**：避免过度复杂的设计和实现
4. **可测试性**：编写易于测试的代码
5. **文档化**：重要功能必须有清晰的文档

### 命名原则
- **英文命名**：所有标识符使用英文
- **语义明确**：名称能清晰表达用途
- **避免缩写**：除非是广泛认知的缩写
- **保持一致**：同类功能使用相似的命名模式

---

## 🐍 Python/Django规范

### 1. 代码风格

#### PEP 8 标准
```python
# 正确示例
class DeviceManager:
    """设备管理器类"""
    
    def __init__(self, user):
        self.user = user
        self.devices = []
    
    def get_available_devices(self, category=None):
        """获取可用设备列表"""
        queryset = Device.objects.filter(status='available')
        if category:
            queryset = queryset.filter(category=category)
        return queryset

# 错误示例
class deviceManager:
    def __init__(self,user):
        self.user=user
        self.devices=[]
    def getAvailableDevices(self,category=None):
        queryset=Device.objects.filter(status='available')
        if category:queryset=queryset.filter(category=category)
        return queryset
```

#### 导入规范
```python
# 标准库导入
import os
import sys
from datetime import datetime, timedelta

# 第三方库导入
import requests
from django.db import models
from rest_framework import serializers

# 本地应用导入
from apps.users.models import User
from apps.devices.models import Device
from utils.helpers import generate_qr_code
```

### 2. Django最佳实践

#### 模型定义
```python
class Device(SoftDeleteModel, AuditModel):
    """设备模型
    
    继承软删除和审计模型，提供基础的删除和审计功能
    """
    
    STATUS_CHOICES = [
        ('available', '可借用'),
        ('borrowed', '借出中'),
        ('maintenance', '维修中'),
    ]
    
    name = models.CharField('设备名称', max_length=200)
    serial_number = models.CharField('序列号', max_length=100, unique=True)
    status = FSMField('设备状态', max_length=20, choices=STATUS_CHOICES, default='available')
    
    class Meta:
        db_table = 'devices'
        verbose_name = '设备'
        verbose_name_plural = '设备'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['serial_number']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.serial_number})"
    
    @transition(field=status, source='available', target='borrowed')
    def borrow(self, user):
        """借出设备"""
        self.current_user = user
```

#### 视图类规范
```python
class DeviceListCreateView(generics.ListCreateAPIView):
    """设备列表和创建视图"""
    
    queryset = Device.objects.filter(is_deleted=False)
    serializer_class = DeviceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'category']
    search_fields = ['name', 'model', 'serial_number']
    ordering_fields = ['created_at', 'name']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """根据用户权限过滤查询集"""
        queryset = super().get_queryset()
        user = self.request.user
        
        if user.role == 'normal_user':
            # 普通用户只能看到可借用的设备
            queryset = queryset.filter(status='available')
        
        return queryset
    
    def perform_create(self, serializer):
        """创建设备时设置创建者"""
        serializer.save(created_by=self.request.user)
```

#### 序列化器规范
```python
class DeviceSerializer(serializers.ModelSerializer):
    """设备序列化器"""
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    owner_info = serializers.SerializerMethodField()
    category_info = serializers.SerializerMethodField()
    
    class Meta:
        model = Device
        fields = [
            'id', 'name', 'model', 'serial_number', 'status', 'status_display',
            'owner_info', 'category_info', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_owner_info(self, obj):
        """获取归属者信息"""
        if obj.owner:
            return {
                'id': obj.owner.id,
                'username': obj.owner.username,
                'email': obj.owner.email
            }
        return None
    
    def validate_serial_number(self, value):
        """验证序列号唯一性"""
        if Device.objects.filter(serial_number=value).exists():
            raise serializers.ValidationError("序列号已存在")
        return value
```

### 3. 错误处理
```python
from django.core.exceptions import ValidationError
from rest_framework.exceptions import APIException

class DeviceNotAvailableError(APIException):
    """设备不可用异常"""
    status_code = 400
    default_detail = '设备当前不可用'
    default_code = 'device_not_available'

def borrow_device(device_id, user):
    """借用设备业务逻辑"""
    try:
        device = Device.objects.get(id=device_id)
        if device.status != 'available':
            raise DeviceNotAvailableError()
        
        device.borrow(user)
        device.save()
        
        return device
    except Device.DoesNotExist:
        raise ValidationError("设备不存在")
    except Exception as e:
        logger.error(f"借用设备失败: {e}")
        raise APIException("借用设备失败，请稍后重试")
```

---

## 🎨 Vue.js/TypeScript规范

### 1. 组件规范

#### 组件命名
```typescript
// 正确：使用PascalCase
export default defineComponent({
  name: 'DeviceList',
  // ...
})

// 文件名使用kebab-case
// device-list.vue
```

#### 组件结构
```vue
<template>
  <div class="device-list">
    <div class="device-list__header">
      <h2 class="device-list__title">设备列表</h2>
      <el-button type="primary" @click="handleCreate">
        添加设备
      </el-button>
    </div>
    
    <div class="device-list__content">
      <el-table :data="devices" :loading="loading">
        <el-table-column prop="name" label="设备名称" />
        <el-table-column prop="status_display" label="状态" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { deviceAPI } from '@/api/devices'
import type { Device } from '@/types/device'

// 响应式数据
const devices = ref<Device[]>([])
const loading = ref(false)

// 方法定义
const fetchDevices = async () => {
  try {
    loading.value = true
    const response = await deviceAPI.getDeviceList()
    devices.value = response.results
  } catch (error) {
    ElMessage.error('获取设备列表失败')
    console.error('Fetch devices error:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  // 创建设备逻辑
}

// 生命周期
onMounted(() => {
  fetchDevices()
})
</script>

<style scoped lang="scss">
.device-list {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
  
  &__content {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
  }
}
</style>
```

### 2. TypeScript类型定义
```typescript
// types/device.ts
export interface Device {
  id: string
  name: string
  model: string
  serial_number: string
  status: DeviceStatus
  status_display: string
  owner_info?: UserInfo
  created_at: string
  updated_at: string
}

export type DeviceStatus = 'available' | 'borrowed' | 'maintenance' | 'scrapped'

export interface UserInfo {
  id: string
  username: string
  email: string
}

export interface DeviceListParams {
  page?: number
  page_size?: number
  status?: DeviceStatus
  search?: string
}
```

### 3. API调用规范
```typescript
// api/devices.ts
import request from './request'
import type { Device, DeviceListParams } from '@/types/device'

export const deviceAPI = {
  // 获取设备列表
  getDeviceList: (params?: DeviceListParams) => {
    return request.get<{
      count: number
      results: Device[]
    }>('/devices/', { params })
  },
  
  // 获取设备详情
  getDeviceDetail: (id: string) => {
    return request.get<Device>(`/devices/${id}/`)
  },
  
  // 创建设备
  createDevice: (data: Partial<Device>) => {
    return request.post<Device>('/devices/', data)
  }
}
```

---

## 🗄️ 数据库规范

### 1. 表设计规范
```sql
-- 表名使用复数形式，小写字母，下划线分隔
CREATE TABLE devices (
    -- 主键使用UUID
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 字段名使用下划线分隔
    device_name VARCHAR(200) NOT NULL,
    serial_number VARCHAR(100) UNIQUE NOT NULL,
    
    -- 外键字段使用_id后缀
    category_id BIGINT REFERENCES device_categories(id),
    owner_id UUID REFERENCES users(id),
    
    -- 布尔字段使用is_前缀
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    
    -- 审计字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 索引
    INDEX idx_devices_status (status),
    INDEX idx_devices_serial_number (serial_number)
);
```

### 2. 查询优化
```python
# 正确：使用select_related减少查询次数
devices = Device.objects.select_related('category', 'owner').filter(status='available')

# 正确：使用prefetch_related优化多对多查询
users = User.objects.prefetch_related('devices').all()

# 错误：N+1查询问题
devices = Device.objects.filter(status='available')
for device in devices:
    print(device.owner.username)  # 每次都会查询数据库
```

---

## 📝 Git提交规范

### 1. 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 2. 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 3. 提交示例
```bash
feat(devices): 添加设备批量操作功能

- 支持批量修改设备状态
- 支持批量分配设备归属者
- 添加操作日志记录

Closes #123
```

---

## 🔍 代码审查标准

### 1. 审查清单
- [ ] 代码符合项目规范
- [ ] 功能实现正确
- [ ] 错误处理完善
- [ ] 性能考虑合理
- [ ] 安全性检查通过
- [ ] 测试覆盖充分
- [ ] 文档更新及时

### 2. 审查重点
1. **安全性**：SQL注入、XSS攻击、权限控制
2. **性能**：数据库查询优化、缓存使用
3. **可维护性**：代码结构清晰、注释完整
4. **测试**：单元测试、集成测试覆盖

---

## 🛠️ 开发工具配置

### 1. VS Code配置
```json
{
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  "typescript.preferences.quoteStyle": "single",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### 2. 代码检查工具
- **Python**: flake8, black, isort
- **TypeScript**: ESLint, Prettier
- **Git**: pre-commit hooks

---

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0.0 | 2025-07-28 | 初始版本，包含所有代码规范 | 开发团队负责人 |

---

*最后更新：2025年7月28日*
