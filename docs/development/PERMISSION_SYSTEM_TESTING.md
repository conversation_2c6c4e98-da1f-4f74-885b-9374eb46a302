# 权限系统测试指南

> **状态**: 🔄 实施中 | **创建**: 2025-08-01 | **版本**: v1.0

## 📋 实施完成情况

### ✅ **已完成的修改**

#### **1. 后端核心修改**
- **设备状态系统**: 更新了8个设备状态和权限映射
- **状态机重构**: 重写了FSM状态转换逻辑
- **权限服务**: 创建了`DevicePermissionService`统一权限管理
- **新API端点**: 
  - `POST /devices/{id}/change-status/` - 设备状态变更
  - `POST /devices/{id}/warehouse-in/` - 设备入库
- **权限类**: `DeviceStatusPermission`, `DeviceAssignPermission`

#### **2. 前端接口更新**
- 更新了`deviceAPI.changeDeviceStatus()`接口
- 新增了`deviceAPI.warehouseInDevice()`接口

### 🔄 **需要完成的步骤**

#### **1. 数据库迁移**
```bash
# 在Docker环境中执行
docker compose exec backend python manage.py makemigrations devices --name update_device_status_system
docker compose exec backend python manage.py migrate
```

#### **2. 重启服务**
```bash
# 根据Agent memories规范，判断是否需要重构
docker compose restart  # 配置变更
# 或
docker compose down && docker compose build --no-cache && docker compose up -d  # 代码变更
```

---

## 🧪 测试方案

### **阶段1: 基础功能测试**

#### **1.1 设备状态转换测试**
```bash
# 测试用例1: 设备分配
POST /api/devices/{device_id}/assign-owner/
{
  "owner_id": "device_owner_user_id",
  "reason": "分配测试"
}
# 预期: 设备状态从 in_stock → assigned

# 测试用例2: 开放借用
POST /api/devices/{device_id}/change-status/
{
  "status": "available",
  "reason": "开放借用测试"
}
# 预期: 设备状态从 assigned → available

# 测试用例3: 设备锁定
POST /api/devices/{device_id}/change-status/
{
  "status": "locked",
  "reason": "长期占用测试"
}
# 预期: 设备状态从 assigned/available → locked
```

#### **1.2 权限验证测试**
```bash
# 测试用例4: 普通用户尝试修改状态（应该失败）
# 使用normal_user身份
POST /api/devices/{device_id}/change-status/
{
  "status": "available"
}
# 预期: 403 Forbidden

# 测试用例5: 设备归属者修改非自己设备状态（应该失败）
# 使用device_owner身份，但设备不属于该用户
POST /api/devices/{device_id}/change-status/
{
  "status": "locked"
}
# 预期: 403 Forbidden "只能管理自己的设备"
```

#### **1.3 入库流程测试**
```bash
# 测试用例6: 设备归属者入库自己的设备
POST /api/devices/{device_id}/warehouse-in/
{
  "reason": "设备不再需要"
}
# 预期: 设备状态变为 in_stock，owner清空
```

### **阶段2: 业务流程测试**

#### **2.1 完整设备生命周期**
```
1. 管理员创建设备 → in_stock
2. 管理员分配给归属者 → assigned
3. 归属者开放借用 → available
4. 用户借用设备 → borrowed
5. 用户归还设备 → available/assigned
6. 归属者入库设备 → in_stock
```

#### **2.2 借用流程测试**
```bash
# 测试借用申请权限
POST /api/loans/applications/
{
  "device": "device_id",
  "reason": "测试借用",
  "expected_start_date": "2025-08-01T10:00:00Z",
  "expected_end_date": "2025-08-05T18:00:00Z"
}
# 预期: 只有非设备归属者可以申请借用
```

#### **2.3 报修流程测试**
```bash
# 设备归属者发起报修
POST /api/devices/{device_id}/change-status/
{
  "status": "maintenance",
  "reason": "设备故障需要维修"
}
# 预期: 设备状态变为 maintenance

# 管理员处理维修完成
POST /api/devices/{device_id}/change-status/
{
  "status": "in_stock",
  "reason": "维修完成，入库重新分配"
}
# 预期: 设备状态变为 in_stock，可重新分配
```

---

## 🔍 关键测试点

### **1. 权限边界测试**
- [ ] 超级管理员可以操作所有状态（包括借用中）
- [ ] 设备管理员不能操作借用中状态
- [ ] 设备归属者只能操作自己的设备
- [ ] 普通用户无设备状态操作权限

### **2. 状态转换验证**
- [ ] 只允许合法的状态转换
- [ ] 非法状态转换返回明确错误信息
- [ ] 状态转换记录操作日志

### **3. 业务逻辑验证**
- [ ] 设备只能分配给device_owner及以上角色
- [ ] 借用中设备只有超管可强制修改状态
- [ ] 维修完成后设备自动入库
- [ ] 入库操作清空设备归属者

### **4. 前后端一致性**
- [ ] 前端状态显示与后端一致
- [ ] 权限控制在前后端都生效
- [ ] API错误信息友好显示

---

## 🚨 常见问题排查

### **问题1: 状态转换失败**
```
错误: "不支持从X到Y的状态转换"
解决: 检查STATUS_TRANSITION_PERMISSIONS配置
```

### **问题2: 权限检查失败**
```
错误: "只能管理自己的设备"
解决: 确认用户角色和设备归属关系
```

### **问题3: 数据库迁移问题**
```
错误: "django.db.utils.IntegrityError"
解决: 检查现有数据是否符合新的状态约束
```

---

## 📊 性能监控

### **关键指标**
- API响应时间: < 200ms
- 权限检查耗时: < 50ms
- 状态转换成功率: > 99%
- 错误日志数量: 监控异常状态转换

### **监控命令**
```bash
# 查看API日志
docker compose logs backend | grep "change-status"

# 查看权限检查日志
docker compose logs backend | grep "DevicePermissionService"

# 查看状态转换日志
docker compose logs backend | grep "DeviceStatusLog"
```

---

## ✅ 验收标准

### **功能验收**
- [ ] 所有8个设备状态正常显示
- [ ] 权限控制按设计要求生效
- [ ] 状态转换符合业务流程
- [ ] 入库和报修流程正常

### **性能验收**
- [ ] API响应时间符合要求
- [ ] 权限检查不影响用户体验
- [ ] 数据库查询优化

### **安全验收**
- [ ] 权限绕过测试通过
- [ ] 恶意状态转换被阻止
- [ ] 操作日志完整记录

---

**下一步**: 执行测试方案，确保所有功能正常后再进行生产部署。
