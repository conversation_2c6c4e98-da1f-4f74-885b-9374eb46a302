# 设备管理配置选项说明

> **更新时间**: 2025-07-31  
> **版本**: v1.0  
> **适用范围**: 设备管理模块

## 📋 概述

设备管理模块使用预定义的配置选项来确保数据标准化和一致性。所有配置选项都在后端 `backend/apps/devices/constants.py` 中定义，并通过API提供给前端。

## 🏷️ 配置选项详情

### 1. 品牌选项 (BRAND_CHOICES)

支持18种主流设备品牌：

```python
BRAND_CHOICES = [
    ('HUAWEI', 'HUAWEI'),
    ('Xiaomi', 'Xiaomi'),
    ('OPPO', 'OPPO'),
    ('VIVO', 'VIVO'),
    ('Redmi', 'Redmi'),
    ('SAMSUNG', 'SAMSUNG'),
    ('HONOR', 'HONOR'),
    ('OnePlus', 'OnePlus'),
    ('Google', 'Google'),
    ('Apple', 'Apple'),
    ('realme', 'realme'),
    ('ZTE', 'ZTE'),
    ('MEIZU', 'MEIZU'),
    ('SONY', 'SONY'),
    ('Asus', 'Asus'),
    ('nubia', 'nubia'),
    ('Moto', 'Moto'),
    ('Other', 'Other'),
]
```

**默认值**: `HUAWEI`

### 2. 设备分类选项 (CATEGORY_CHOICES)

支持5种设备分类：

```python
CATEGORY_CHOICES = [
    ('手机', '手机'),
    ('平板', '平板'),
    ('笔记本', '笔记本'),
    ('PC', 'PC'),
    ('其它', '其它'),
]
```

**默认值**: `手机`

### 3. 特殊屏幕选项 (SPECIAL_SCREEN_CHOICES)

支持12种特殊屏幕类型：

```python
SPECIAL_SCREEN_CHOICES = [
    ('刘海屏', '刘海屏'),
    ('水滴屏', '水滴屏'),
    ('挖孔屏', '挖孔屏'),
    ('瀑布屏', '瀑布屏'),
    ('曲面屏', '曲面屏'),
    ('真全面屏', '真全面屏'),
    ('内折屏', '内折屏'),
    ('外折屏', '外折屏'),
    ('上下折叠屏', '上下折叠屏'),
    ('三折屏', '三折屏'),
    ('环绕屏', '环绕屏'),
    ('滑盖全面屏', '滑盖全面屏'),
]
```

**默认值**: `刘海屏`

### 4. 操作系统选项 (OS_CHOICES)

支持4种主流操作系统：

```python
OS_CHOICES = [
    ('Android', 'Android'),
    ('iOS', 'iOS'),
    ('HarmonyOS', 'HarmonyOS'),
    ('HarmonyOSNext', 'HarmonyOSNext'),
]
```

**默认值**: `Android`

### 5. 设备状态选项 (STATUS_CHOICES)

支持6种设备状态：

```python
STATUS_CHOICES = [
    ('in_stock', '库存中'),
    ('available', '可借用'),
    ('borrowed', '已借出'),
    ('maintenance', '维修中'),
    ('retired', '已报废'),
    ('locked', '已锁定'),
]
```

**默认值**: `in_stock`

## 🔌 API接口

### 获取配置选项

**接口地址**: `GET /api/devices/config-options/`

**响应格式**:
```json
{
  "brands": [
    {"value": "HUAWEI", "label": "HUAWEI"},
    {"value": "Xiaomi", "label": "Xiaomi"},
    ...
  ],
  "categories": [
    {"value": "手机", "label": "手机"},
    {"value": "平板", "label": "平板"},
    ...
  ],
  "special_screens": [
    {"value": "刘海屏", "label": "刘海屏"},
    {"value": "水滴屏", "label": "水滴屏"},
    ...
  ],
  "operating_systems": [
    {"value": "Android", "label": "Android"},
    {"value": "iOS", "label": "iOS"},
    ...
  ],
  "statuses": [
    {"value": "in_stock", "label": "库存中"},
    {"value": "available", "label": "可借用"},
    ...
  ]
}
```

## 🎯 前端使用

### 1. 加载配置选项

```javascript
import { deviceAPI } from '@/api/devices'

const loadConfigOptions = async () => {
  try {
    const response = await deviceAPI.getConfigOptions()
    configOptions.value = response
    
    // 设置默认值（新增模式）
    if (!props.device) {
      formData.brand = response.brands[0]?.value || ''
      formData.category = response.categories[0]?.value || ''
      formData.special_screen = response.special_screens[0]?.value || ''
      formData.os = response.operating_systems[0]?.value || ''
    }
  } catch (error) {
    console.error('加载配置选项失败:', error)
  }
}
```

### 2. 下拉框组件使用

```vue
<el-select
  v-model="formData.brand"
  placeholder="请选择品牌"
  style="width: 100%"
  filterable
>
  <el-option
    v-for="option in configOptions.brands"
    :key="option.value"
    :label="option.label"
    :value="option.value"
  />
</el-select>
```

## 📊 批量导入支持

### Excel模板字段说明

在批量导入Excel模板中，配置字段需要严格按照预定义选项填写：

- **品牌**: 必须是18种支持品牌之一
- **设备分类**: 必须是5种分类之一
- **特殊屏幕**: 必须是12种屏幕类型之一
- **操作系统**: 必须是4种操作系统之一

### 导入验证

系统会自动验证导入数据的有效性：
- 无效的配置选项值会导致导入失败
- 提供详细的错误提示信息
- 支持部分成功导入（跳过错误行）

## 🔧 维护和扩展

### 添加新选项

1. 在 `backend/apps/devices/constants.py` 中添加新选项
2. 创建数据库迁移：`python manage.py makemigrations devices`
3. 应用迁移：`python manage.py migrate`
4. 更新批量导入模板和说明文档
5. 更新用户指南

### 修改默认值

1. 修改 `constants.py` 中的默认值函数
2. 更新前端默认值设置逻辑
3. 测试新增设备功能

## ⚠️ 注意事项

1. **数据一致性**: 修改配置选项前需要考虑现有数据的兼容性
2. **向后兼容**: 删除选项前需要确保没有现有数据使用该选项
3. **国际化**: 如需支持多语言，需要相应调整选项结构
4. **性能考虑**: 配置选项会被频繁访问，考虑添加缓存机制

## 📝 更新记录

- **2025-07-31**: 初始版本，定义所有配置选项和使用说明
