# 🚀 MDM开发指南

**版本**: v2.0.0
**更新时间**: 2025年7月30日
**维护者**: 开发团队
**适用范围**: MDM设备管理系统开发

---

## 📋 目录

1. [快速开始](#快速开始)
2. [开发环境要求](#开发环境要求)
3. [项目结构](#项目结构)
4. [开发流程](#开发流程)
5. [调试和测试](#调试和测试)
6. [常见问题](#常见问题)

---

## ⚡ 快速开始（推荐）

### 1. 启动基础服务
```bash
scripts\dev\start-services.bat
```
这会启动数据库和Redis（约5秒）

### 2. 启动后端（新终端）
```bash
dev-backend.bat
```
访问：http://127.0.0.1:8000

### 3. 启动前端（新终端）
```bash
dev-frontend.bat
```
访问：http://localhost:5173

### 4. 停止服务
```bash
scripts\dev\stop-services.bat
```

### 完整Docker模式（较慢）

如果需要完整的Docker环境：
```bash
build-dev.bat
```

---

## 💻 开发环境要求

### 必需软件

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| **Node.js** | 20.x LTS | 前端开发环境 |
| **npm** | 10.0+ | 包管理器 |
| **Python** | 3.11+ | 后端开发环境 |
| **Docker Desktop** | 20.10+ | 容器化开发 |
| **Git** | 2.25+ | 版本控制 |

### 推荐工具

| 工具 | 用途 | 说明 |
|------|------|------|
| **VS Code** | 前端开发 | + Vue Language Features (Volar) |
| **PyCharm Professional** | 后端开发 | 或 VS Code + Python Extension |
| **pgAdmin 4** | 数据库管理 | 或 DBeaver |
| **Postman** | API测试 | 或 Insomnia |

### 浏览器要求
- **Chrome/Firefox** 最新版
- **Vue DevTools** 扩展（推荐）

---

## 📁 项目结构

```
mdm/
├── backend/                 # Django后端
│   ├── apps/               # 应用模块
│   ├── config/             # 项目配置
│   ├── requirements.txt    # Python依赖
│   └── manage.py          # Django管理脚本
├── frontend/               # Vue.js前端
│   ├── src/               # 源代码
│   ├── package.json       # Node.js依赖
│   └── vite.config.ts     # Vite配置
├── scripts/               # 开发脚本
│   ├── dev/              # 开发环境脚本
│   └── deploy/           # 部署脚本
├── docs/                  # 项目文档
├── dev-backend.bat        # 启动后端开发服务器
├── dev-frontend.bat       # 启动前端开发服务器
└── build-dev.bat         # 完整Docker构建
```

---

## 🔄 开发流程

### 日常开发流程

1. **首次运行**: 按照"快速开始"步骤完整执行
2. **日常开发**: 只需运行步骤2和3（基础服务保持运行）
3. **代码修改**: 支持热重载，无需重启服务
4. **调试**: 可在IDE中直接调试Python和JavaScript代码

### 开发环境端口

| 服务 | 地址 | 说明 |
|------|------|------|
| **前端** | http://localhost:5173 | Vue.js开发服务器 |
| **后端** | http://127.0.0.1:8000 | Django开发服务器 |
| **API文档** | http://127.0.0.1:8000/api/docs/ | Swagger文档 |
| **管理后台** | http://127.0.0.1:8000/admin/ | Django Admin |
| **数据库** | localhost:5432 | PostgreSQL |
| **Redis** | localhost:6379 | Redis缓存 |

## 数据库迁移管理

### 重要提醒
⚠️ **每次修改模型后必须检查和应用迁移**，避免前端功能与数据库不同步的问题。

### 迁移操作流程
```bash
# 1. 生成迁移文件
cd backend
.\venv\Scripts\activate
python manage.py makemigrations

# 2. 检查迁移文件内容
# 查看生成的迁移文件，确认变更正确

# 3. 应用迁移
python manage.py migrate

# 4. 验证迁移结果
python manage.py showmigrations
```

### 常见迁移问题
1. **字段约束不一致**: 模型定义与数据库约束不匹配
   - 症状：API返回成功但数据未保存
   - 解决：重新生成并应用迁移

2. **外键约束错误**: 关联表不存在或字段类型不匹配
   - 症状：创建记录时外键约束失败
   - 解决：检查关联模型的迁移顺序

3. **唯一约束冲突**: 字段唯一性约束与现有数据冲突
   - 症状：迁移执行失败
   - 解决：清理冲突数据或调整约束

### 迁移最佳实践
- 每次模型变更后立即生成迁移
- 提交代码前确保迁移文件已包含
- 生产环境部署前在测试环境验证迁移
- 重要迁移操作前备份数据库

---

## 🔧 调试和测试

### 前端调试

#### 开发调试
```bash
# 启动开发服务器（带调试信息）
npm run dev

# 类型检查
npm run type-check

# 代码质量检查
npm run lint

# 修复代码格式
npm run lint:fix
```

#### 构建测试
```bash
# 生产构建
npm run build

# 预览构建结果
npm run preview

# 构建分析
npm run build -- --analyze
```

### 后端调试

#### 开发调试
```bash
# 进入虚拟环境
cd backend
.\venv\Scripts\activate

# 运行测试
python manage.py test

# 运行特定测试
python manage.py test apps.devices.tests

# 代码覆盖率
coverage run --source='.' manage.py test
coverage report
```

#### 管理命令
```bash
# 创建超级用户
python manage.py createsuperuser

# 收集静态文件
python manage.py collectstatic

# 清理会话
python manage.py clearsessions

# Django shell
python manage.py shell

# 数据库shell
python manage.py dbshell
```

---

## 🚨 常见问题

### 环境问题

#### 端口冲突
```bash
# 检查端口占用
netstat -ano | findstr :5173
netstat -ano | findstr :8000
netstat -ano | findstr :5432
netstat -ano | findstr :6379

# 杀死占用进程
taskkill /PID <PID> /F
```

#### 权限问题
- 确保脚本有执行权限
- 以管理员身份运行命令提示符
- 检查防火墙和杀毒软件设置

#### 依赖问题
```bash
# 前端依赖重装
cd frontend
rm -rf node_modules package-lock.json
npm install

# 后端依赖重装
cd backend
rm -rf venv
python -m venv venv
.\venv\Scripts\activate
pip install -r requirements.txt
```

### Docker问题

#### 容器问题
```bash
# 查看容器状态
docker ps -a

# 查看容器日志
docker logs <container_name>

# 重启容器
docker restart <container_name>

# 重建容器
docker-compose -f scripts/dev/docker-services.yml up -d --force-recreate
```

#### 重置开发环境
```bash
# 停止所有服务
scripts\dev\stop-services.bat

# 清理Docker
docker system prune -f
docker volume prune -f

# 重新开始
scripts\dev\start-services.bat
```

### 性能问题

#### 前端性能优化
- 使用Vue DevTools分析组件性能
- 检查网络请求是否过多
- 优化图片和静态资源

#### 后端性能优化
- 使用Django Debug Toolbar分析SQL查询
- 检查数据库索引
- 优化API响应时间

---

## 📞 技术支持

### 开发团队联系方式
- **前端团队**: <EMAIL>
- **后端团队**: <EMAIL>
- **DevOps团队**: <EMAIL>

### 相关文档
- [编码规范](coding-standards.md) - 代码质量标准
- [架构设计](../architecture/README.md) - 系统架构文档
- [部署指南](../deployment/README.md) - 生产环境部署

---

*最后更新: 2025-07-30*
*文档版本: v2.0.0*
