# 问题报告模板

## 🚨 重要提醒
在提交新问题前，请先检查 [已知问题文档](KNOWN_ISSUES.md) 是否已有解决方案。

## 问题基本信息

### 问题类型
- [ ] 环境搭建问题
- [ ] 功能Bug
- [ ] 性能问题
- [ ] 文档问题
- [ ] 安全问题
- [ ] 其他

### 严重程度
- [ ] 🔴 严重 - 阻塞开发/生产
- [ ] 🟡 中等 - 影响功能使用
- [ ] 🟢 轻微 - 优化建议

### 影响范围
- [ ] 开发环境
- [ ] 测试环境
- [ ] 生产环境
- [ ] 所有环境

## 问题描述

### 简要描述
[请简要描述问题]

### 详细描述
[请详细描述问题的具体表现]

### 期望行为
[描述您期望的正确行为]

### 实际行为
[描述实际发生的行为]

## 复现步骤

1. [第一步]
2. [第二步]
3. [第三步]
...

## 环境信息

### 操作系统
- [ ] Windows 10/11
- [ ] macOS
- [ ] Linux (请指定发行版)

### Docker版本
```
docker --version
docker compose version
```

### 浏览器信息（如适用）
- [ ] Chrome
- [ ] Firefox
- [ ] Safari
- [ ] Edge

## 错误信息

### 错误日志
```
[请粘贴完整的错误日志]
```

### 截图
[如有必要，请提供截图]

## 已尝试的解决方案

- [ ] 重启Docker服务
- [ ] 清理Docker缓存
- [ ] 检查端口占用
- [ ] 查阅已知问题文档
- [ ] 其他: [请描述]

## 附加信息

### 相关配置文件
[如果问题与配置相关，请提供相关配置文件内容]

### 其他相关信息
[任何其他可能有助于解决问题的信息]

---

## 📋 问题处理检查清单（维护人员填写）

### 问题分析
- [ ] 问题已确认可复现
- [ ] 问题根本原因已识别
- [ ] 影响范围已评估

### 解决方案
- [ ] 临时解决方案已提供
- [ ] 永久解决方案已实施
- [ ] 相关文档已更新

### 预防措施
- [ ] 已添加到已知问题文档
- [ ] 已更新环境检查脚本
- [ ] 已优化启动流程
- [ ] 已添加自动化检测

### 验证
- [ ] 解决方案已在开发环境验证
- [ ] 解决方案已在测试环境验证
- [ ] 相关团队成员已通知

---

## 🔄 问题状态跟踪

| 状态 | 时间 | 操作人 | 说明 |
|------|------|--------|------|
| 新建 | [时间] | [姓名] | 问题首次报告 |
| 确认 | [时间] | [姓名] | 问题已确认 |
| 处理中 | [时间] | [姓名] | 正在处理 |
| 已解决 | [时间] | [姓名] | 问题已解决 |
| 已验证 | [时间] | [姓名] | 解决方案已验证 |
| 已关闭 | [时间] | [姓名] | 问题已关闭 |
