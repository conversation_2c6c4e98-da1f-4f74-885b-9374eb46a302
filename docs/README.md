# 📚 MDM设备管理系统 - 技术文档中心

**版本**: v2.0.0
**更新时间**: 2025年7月30日
**维护者**: 技术团队

---

## 🎯 快速导航

### ⚡ 5分钟上手
```bash
# 1. 启动开发环境
start-dev.bat

# 2. 访问系统
前端: http://localhost:3000
后端: http://localhost:8000/admin/

# 3. 默认登录
用户名: admin
密码: admin123
```

### 📋 常用命令速查
- **启动开发**: `start-dev.bat`
- **启动生产**: `start-prod.bat`
- **停止所有**: `stop-all.bat`
- **查看日志**: `docker logs <container_name>`
- **健康检查**: `scripts/check-env-sync.bat`

---

## 📖 文档分类

### 🏗️ 系统架构
- [技术架构](architecture/technical_architecture.md) - 系统整体架构设计
- [API设计](architecture/api-design.md) - RESTful API规范和接口文档
- [数据库设计](architecture/database-design.md) - 数据模型和关系设计

### 🔧 开发指南
- [开发指南](development/DEV-GUIDE.md) ⭐ - 完整的开发环境搭建和规范
- [编码规范](development/coding-standards.md) - Python/Django和Vue.js代码标准

### 🚀 部署运维
- [生产环境部署](deployment/PRODUCTION_DEPLOYMENT.md) ⭐ - 完整的生产部署流程
- [Docker最佳实践](deployment/DOCKER_BEST_PRACTICES.md) - 容器化部署指南
- [健康检查](monitoring/HEALTH_CHECKS.md) - 容器健康监控配置

### 🔐 管理配置
- [管理员配置指南](ADMIN_SETUP_GUIDE.md) ⭐ - 管理员账户创建和配置
- [安全标准](security/SECURITY_STANDARDS.md) - 系统安全要求和规范

### 👥 用户文档
- [用户操作手册](user/user-guide.md) ⭐ - 完整的系统使用说明

### 📋 项目管理
- [快速参考指南](QUICK_REFERENCE_GUIDE.md) - 常用命令和检查清单
- [问题注册表](MASTER_ISSUE_REGISTRY.md) ⭐ - 统一问题管理中心
- [已知问题快速参考](KNOWN_ISSUES.md) - 常见问题快速查找

---

## 🎯 角色导航

| 角色 | 推荐文档 | 核心关注点 |
|------|----------|------------|
| **新手用户** | [管理员配置指南](ADMIN_SETUP_GUIDE.md) | 快速上手 |
| **开发人员** | [开发指南](development/DEV-GUIDE.md) | 环境搭建、编码规范 |
| **运维人员** | [生产环境部署](deployment/PRODUCTION_DEPLOYMENT.md) | 部署、监控、维护 |
| **架构师** | [技术架构](architecture/technical_architecture.md) | 系统设计、技术选型 |
| **产品经理** | [用户操作手册](user/user-guide.md) | 功能特性、用户体验 |
| **测试人员** | [API设计](architecture/api-design.md) | 接口测试、功能验证 |

---

## 🔧 维护信息

### 文档维护规则
- ✅ **及时更新**: 功能变更后立即更新相关文档
- ✅ **版本同步**: 文档版本与系统版本保持一致
- ✅ **质量保证**: 所有文档经过审核后发布
- ✅ **用户友好**: 使用清晰的语言和丰富的示例

### 技术支持
- **文档问题**: <EMAIL>
- **技术支持**: <EMAIL>
- **功能建议**: <EMAIL>

---

*最后更新: 2025-07-30*
*文档版本: v2.0.0*
*维护团队: 技术文档组*
