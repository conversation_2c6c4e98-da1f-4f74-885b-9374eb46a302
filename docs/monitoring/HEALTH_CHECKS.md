# 🏥 容器健康检查指南

**版本**: v1.0.0  
**更新时间**: 2025年7月30日  
**维护者**: 运维团队

---

## 📋 目录

1. [健康检查概述](#健康检查概述)
2. [各服务健康检查配置](#各服务健康检查配置)
3. [健康检查脚本](#健康检查脚本)
4. [监控和故障排查](#监控和故障排查)
5. [最佳实践](#最佳实践)

---

## 🔍 健康检查概述

### 什么是健康检查？

健康检查是Docker容器的一种机制，用于定期检查容器内服务的运行状态。它可以：

- **自动检测服务故障**
- **触发容器重启**
- **提供服务状态监控**
- **支持负载均衡决策**

### 健康检查状态

| 状态 | 说明 | 显示 |
|------|------|------|
| **starting** | 健康检查启动期 | `(health: starting)` |
| **healthy** | 服务正常运行 | `(healthy)` |
| **unhealthy** | 服务异常 | `(unhealthy)` |

---

## 🛠️ 各服务健康检查配置

### 1. **PostgreSQL数据库** (`mdm_postgres_dev`)

**健康检查命令**:
```bash
pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}
```

**配置参数**:
- **间隔**: 10秒
- **超时**: 5秒
- **重试**: 5次
- **启动期**: 30秒

### 2. **Redis缓存** (`mdm_redis_dev`)

**健康检查命令**:
```bash
redis-cli ping
```

**配置参数**:
- **间隔**: 10秒
- **超时**: 3秒
- **重试**: 3次
- **启动期**: 30秒

### 3. **Django后端** (`mdm_backend_dev`)

**健康检查命令**:
```bash
curl -f http://localhost:8000/api/health/ || exit 1
```

**配置参数**:
- **间隔**: 30秒
- **超时**: 10秒
- **重试**: 3次
- **启动期**: 40秒

### 4. **Vue.js前端** (`mdm_frontend_dev`)

**健康检查命令**:
```bash
curl -f http://localhost:3000/ || exit 1
```

**配置参数**:
- **间隔**: 30秒
- **超时**: 10秒
- **重试**: 3次
- **启动期**: 30秒

### 5. **Celery Worker** (`mdm_celery_dev`)

**健康检查命令**:
```bash
celery -A config inspect ping -d celery@$HOSTNAME
```

**配置参数**:
- **间隔**: 30秒
- **超时**: 10秒
- **重试**: 3次
- **启动期**: 60秒

### 6. **Celery Beat** (`mdm_celery_beat_dev`) ⭐

**健康检查命令**:
```bash
python /app/scripts/celery_beat_healthcheck.py
```

**配置参数**:
- **间隔**: 30秒
- **超时**: 15秒
- **重试**: 3次
- **启动期**: 60秒

**检查内容**:
- ✅ 数据库连接
- ✅ Redis连接
- ✅ django_celery_beat应用状态
- ✅ Celery模块可用性

---

## 📜 健康检查脚本

### Celery Beat健康检查脚本

**位置**: `backend/scripts/celery_beat_healthcheck.py`

**功能**:
1. **数据库连接检查** - 验证Django数据库连接
2. **Redis连接检查** - 验证Celery broker连接
3. **django_celery_beat检查** - 验证定时任务应用状态
4. **Celery模块检查** - 验证Celery可用性

**使用方法**:
```bash
# 手动执行健康检查
docker exec mdm_celery_beat_dev python /app/scripts/celery_beat_healthcheck.py

# 查看健康检查日志
docker inspect mdm_celery_beat_dev --format="{{json .State.Health}}"
```

---

## 🔧 监控和故障排查

### 查看所有容器健康状态

```bash
# 查看所有容器状态
docker ps

# 查看特定容器健康状态
docker inspect <container_name> --format="{{.State.Health.Status}}"

# 查看健康检查日志
docker inspect <container_name> --format="{{json .State.Health}}" | python -m json.tool
```

### 常见健康检查问题

#### 1. **容器显示 `(health: starting)`**

**原因**: 容器在启动期内，健康检查尚未完成

**解决方案**:
- 等待启动期结束（通常60-120秒）
- 检查服务启动日志：`docker logs <container_name>`

#### 2. **容器显示 `(unhealthy)`**

**原因**: 健康检查连续失败

**排查步骤**:
```bash
# 1. 查看健康检查日志
docker inspect <container_name> --format="{{json .State.Health}}"

# 2. 查看容器日志
docker logs <container_name> --tail 50

# 3. 手动执行健康检查命令
docker exec <container_name> <health_check_command>

# 4. 重启容器
docker restart <container_name>
```

#### 3. **Celery Beat健康检查失败**

**常见原因**:
- 数据库连接失败
- Redis连接失败
- django_celery_beat未正确配置
- Celery模块导入失败

**解决方案**:
```bash
# 手动执行健康检查
docker exec mdm_celery_beat_dev python /app/scripts/celery_beat_healthcheck.py

# 检查数据库连接
docker exec mdm_celery_beat_dev python manage.py dbshell

# 检查Redis连接
docker exec mdm_celery_beat_dev python -c "import redis; r=redis.from_url('redis://redis:6379/0'); print(r.ping())"

# 重新执行迁移
docker exec mdm_celery_beat_dev python manage.py migrate django_celery_beat
```

---

## 🎯 最佳实践

### 1. **健康检查设计原则**

- **轻量级**: 健康检查应该快速执行，避免消耗过多资源
- **准确性**: 检查应该能准确反映服务的真实状态
- **容错性**: 健康检查本身应该具有容错能力
- **可观测**: 提供详细的日志和状态信息

### 2. **配置建议**

- **启动期**: 设置足够的启动时间，避免误报
- **间隔时间**: 平衡检查频率和系统负载
- **重试次数**: 避免因临时故障导致的误判
- **超时时间**: 设置合理的超时，避免检查阻塞

### 3. **监控集成**

```bash
# 创建监控脚本
#!/bin/bash
for container in $(docker ps --format "{{.Names}}"); do
    status=$(docker inspect $container --format="{{.State.Health.Status}}" 2>/dev/null)
    if [ "$status" = "unhealthy" ]; then
        echo "ALERT: $container is unhealthy"
        # 发送告警通知
    fi
done
```

### 4. **自动恢复策略**

```yaml
# Docker Compose配置示例
services:
  app:
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

---

## 📊 健康检查状态总览

### 当前环境状态

| 服务 | 容器名 | 健康检查 | 状态 |
|------|--------|----------|------|
| PostgreSQL | `mdm_postgres_dev` | ✅ 已配置 | `(healthy)` |
| Redis | `mdm_redis_dev` | ✅ 已配置 | `(healthy)` |
| Django后端 | `mdm_backend_dev` | ✅ 已配置 | `(healthy)` |
| Vue.js前端 | `mdm_frontend_dev` | ✅ 已配置 | `(healthy)` |
| Celery Worker | `mdm_celery_dev` | ✅ 已配置 | `(healthy)` |
| Celery Beat | `mdm_celery_beat_dev` | ✅ **新增** | `(healthy)` |

### 环境同步状态

- ✅ **开发环境**: 所有服务已配置健康检查
- ✅ **生产环境**: 健康检查配置已同步
- ✅ **精简生产环境**: 健康检查配置已同步

---

## 📞 技术支持

### 相关文档
- [环境配置指南](../../ENVIRONMENT_GUIDE.md)
- [管理员配置指南](../ADMIN_SETUP_GUIDE.md)
- [生产环境部署](../deployment/PRODUCTION_DEPLOYMENT.md)

### 联系方式
- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>

---

*最后更新: 2025-07-30*  
*文档版本: v1.0.0*
