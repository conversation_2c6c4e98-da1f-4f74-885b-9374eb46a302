# 📊 监控体系文档

**版本**: v1.0.0  
**更新时间**: 2025年7月29日  
**维护者**: 运维负责人 & SRE团队  
**适用范围**: 设备管理平台及所有企业级项目

---

## 📋 目录

1. [监控体系概述](#监控体系概述)
2. [应用监控](#应用监控)
3. [基础设施监控](#基础设施监控)
4. [日志管理](#日志管理)
5. [告警机制](#告警机制)
6. [性能监控](#性能监控)

---

## 🎯 监控体系概述

### 监控架构

```mermaid
graph TB
    subgraph "应用层"
        A1[Django应用]
        A2[Vue.js前端]
        A3[Nginx]
        A4[Celery]
    end
    
    subgraph "中间件层"
        M1[PostgreSQL]
        M2[Redis]
        M3[消息队列]
    end
    
    subgraph "基础设施层"
        I1[Kubernetes]
        I2[Docker]
        I3[服务器]
        I4[网络]
    end
    
    subgraph "监控系统"
        P[Prometheus]
        G[Grafana]
        E[ELK Stack]
        A[AlertManager]
    end
    
    A1 --> P
    A2 --> P
    A3 --> P
    A4 --> P
    M1 --> P
    M2 --> P
    M3 --> P
    I1 --> P
    I2 --> P
    I3 --> P
    I4 --> P
    
    P --> G
    P --> A
    E --> G
```

### 监控目标

#### 1. 可用性监控
- **服务可用性**: 99.9% SLA目标
- **接口响应时间**: P95 < 2秒
- **错误率**: < 0.1%
- **系统恢复时间**: MTTR < 30分钟

#### 2. 性能监控
- **吞吐量**: QPS监控和容量规划
- **资源利用率**: CPU、内存、磁盘、网络
- **数据库性能**: 查询时间、连接数、锁等待
- **缓存命中率**: Redis缓存效率

#### 3. 业务监控
- **用户行为**: 登录、设备操作、借用流程
- **业务指标**: 设备利用率、借用成功率
- **异常检测**: 异常登录、批量操作
- **趋势分析**: 用户增长、功能使用情况

---

## 📱 应用监控

### Django应用监控

#### 1. 性能指标收集
```python
# backend/monitoring/middleware.py
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from prometheus_client import Counter, Histogram, Gauge

# Prometheus指标定义
REQUEST_COUNT = Counter(
    'django_requests_total',
    'Total Django requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'django_request_duration_seconds',
    'Django request duration',
    ['method', 'endpoint']
)

ACTIVE_REQUESTS = Gauge(
    'django_active_requests',
    'Active Django requests'
)

class MonitoringMiddleware(MiddlewareMixin):
    """监控中间件"""
    
    def process_request(self, request):
        request.start_time = time.time()
        ACTIVE_REQUESTS.inc()
        return None
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # 记录请求指标
            REQUEST_COUNT.labels(
                method=request.method,
                endpoint=request.path,
                status=response.status_code
            ).inc()
            
            REQUEST_DURATION.labels(
                method=request.method,
                endpoint=request.path
            ).observe(duration)
            
            ACTIVE_REQUESTS.dec()
            
            # 记录慢请求
            if duration > 2.0:
                logging.warning(
                    f'Slow request: {request.method} {request.path} '
                    f'took {duration:.2f}s'
                )
        
        return response
```

#### 2. 业务指标监控
```python
# backend/monitoring/metrics.py
from prometheus_client import Counter, Gauge, Histogram
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from apps.devices.models import Device
from apps.loans.models import LoanApplication

# 业务指标定义
DEVICE_COUNT = Gauge(
    'mdm_devices_total',
    'Total number of devices',
    ['status', 'category']
)

LOAN_COUNT = Counter(
    'mdm_loans_total',
    'Total number of loan applications',
    ['status', 'action']
)

USER_ACTIVITY = Counter(
    'mdm_user_activity_total',
    'User activity events',
    ['action', 'user_type']
)

@receiver(post_save, sender=Device)
def update_device_metrics(sender, instance, created, **kwargs):
    """更新设备指标"""
    if created:
        DEVICE_COUNT.labels(
            status=instance.status,
            category=instance.category.name if instance.category else 'unknown'
        ).inc()

@receiver(post_save, sender=LoanApplication)
def update_loan_metrics(sender, instance, created, **kwargs):
    """更新借用指标"""
    if created:
        LOAN_COUNT.labels(
            status=instance.status,
            action='created'
        ).inc()
    else:
        LOAN_COUNT.labels(
            status=instance.status,
            action='updated'
        ).inc()

def record_user_activity(user, action):
    """记录用户活动"""
    user_type = 'admin' if user.is_staff else 'user'
    USER_ACTIVITY.labels(
        action=action,
        user_type=user_type
    ).inc()
```

#### 3. 健康检查端点
```python
# backend/monitoring/views.py
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings
import redis
import time

def health_check(request):
    """健康检查端点"""
    health_status = {
        'status': 'healthy',
        'timestamp': time.time(),
        'checks': {}
    }
    
    # 数据库检查
    try:
        with connection.cursor() as cursor:
            cursor.execute('SELECT 1')
        health_status['checks']['database'] = 'healthy'
    except Exception as e:
        health_status['checks']['database'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
    
    # Redis检查
    try:
        cache.set('health_check', 'ok', 10)
        cache.get('health_check')
        health_status['checks']['redis'] = 'healthy'
    except Exception as e:
        health_status['checks']['redis'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'unhealthy'
    
    # 磁盘空间检查
    import shutil
    try:
        disk_usage = shutil.disk_usage('/')
        free_percent = (disk_usage.free / disk_usage.total) * 100
        if free_percent < 10:
            health_status['checks']['disk'] = f'warning: {free_percent:.1f}% free'
        else:
            health_status['checks']['disk'] = 'healthy'
    except Exception as e:
        health_status['checks']['disk'] = f'error: {str(e)}'
    
    status_code = 200 if health_status['status'] == 'healthy' else 503
    return JsonResponse(health_status, status=status_code)

def readiness_check(request):
    """就绪检查端点"""
    # 检查应用是否准备好接收流量
    return JsonResponse({'status': 'ready'})

def liveness_check(request):
    """存活检查端点"""
    # 检查应用是否还活着
    return JsonResponse({'status': 'alive'})
```

### 前端监控

#### 1. 性能监控
```typescript
// frontend/src/utils/monitoring.ts
interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
}

class FrontendMonitoring {
  private static instance: FrontendMonitoring;
  
  static getInstance(): FrontendMonitoring {
    if (!FrontendMonitoring.instance) {
      FrontendMonitoring.instance = new FrontendMonitoring();
    }
    return FrontendMonitoring.instance;
  }
  
  /**
   * 收集页面性能指标
   */
  collectPerformanceMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      pageLoadTime: navigation.loadEventEnd - navigation.fetchStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
      firstContentfulPaint: this.getFCP(),
      largestContentfulPaint: this.getLCP(),
      cumulativeLayoutShift: this.getCLS()
    };
  }
  
  /**
   * 获取首次内容绘制时间
   */
  private getFCP(): number {
    const entries = performance.getEntriesByName('first-contentful-paint');
    return entries.length > 0 ? entries[0].startTime : 0;
  }
  
  /**
   * 获取最大内容绘制时间
   */
  private getLCP(): number {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });
    });
  }
  
  /**
   * 获取累积布局偏移
   */
  private getCLS(): number {
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
    }).observe({ entryTypes: ['layout-shift'] });
    return clsValue;
  }
  
  /**
   * 监控API请求
   */
  monitorAPIRequest(url: string, method: string, duration: number, status: number): void {
    // 发送到监控系统
    this.sendMetric('api_request', {
      url,
      method,
      duration,
      status,
      timestamp: Date.now()
    });
  }
  
  /**
   * 监控用户行为
   */
  trackUserAction(action: string, details?: Record<string, any>): void {
    this.sendMetric('user_action', {
      action,
      details,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }
  
  /**
   * 监控错误
   */
  trackError(error: Error, context?: Record<string, any>): void {
    this.sendMetric('frontend_error', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now(),
      url: window.location.href
    });
  }
  
  /**
   * 发送指标到监控系统
   */
  private sendMetric(type: string, data: any): void {
    // 发送到后端监控端点
    fetch('/api/monitoring/metrics/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type, data })
    }).catch(console.error);
  }
}

export default FrontendMonitoring;
```

#### 2. 错误监控
```typescript
// frontend/src/utils/errorTracking.ts
class ErrorTracker {
  private monitoring = FrontendMonitoring.getInstance();
  
  init(): void {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.monitoring.trackError(event.error, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      });
    });
    
    // Promise 错误处理
    window.addEventListener('unhandledrejection', (event) => {
      this.monitoring.trackError(new Error(event.reason), {
        type: 'unhandled_promise_rejection'
      });
    });
    
    // Vue 错误处理
    app.config.errorHandler = (error, instance, info) => {
      this.monitoring.trackError(error, {
        component: instance?.$options.name,
        info
      });
    };
  }
}

export default new ErrorTracker();
```

---

## 🖥️ 基础设施监控

### Kubernetes监控

#### 1. 集群监控配置
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  # Kubernetes API Server
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  # Kubernetes Nodes
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)

  # Kubernetes Pods
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)

  # MDM Application
  - job_name: 'mdm-backend'
    static_configs:
    - targets: ['mdm-backend:8000']
    metrics_path: '/metrics/'
    scrape_interval: 30s

  # PostgreSQL
  - job_name: 'postgresql'
    static_configs:
    - targets: ['postgres-exporter:9187']

  # Redis
  - job_name: 'redis'
    static_configs:
    - targets: ['redis-exporter:9121']
```

#### 2. 告警规则
```yaml
# monitoring/prometheus/alert_rules.yml
groups:
- name: mdm-alerts
  rules:
  # 应用可用性告警
  - alert: MDMBackendDown
    expr: up{job="mdm-backend"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "MDM Backend is down"
      description: "MDM Backend has been down for more than 1 minute"

  # 高错误率告警
  - alert: HighErrorRate
    expr: rate(django_requests_total{status=~"5.."}[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  # 响应时间告警
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(django_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time"
      description: "95th percentile response time is {{ $value }}s"

  # 资源使用告警
  - alert: HighCPUUsage
    expr: rate(container_cpu_usage_seconds_total{pod=~"mdm-.*"}[5m]) > 0.8
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage"
      description: "CPU usage is {{ $value }}%"

  - alert: HighMemoryUsage
    expr: container_memory_usage_bytes{pod=~"mdm-.*"} / container_spec_memory_limit_bytes > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High memory usage"
      description: "Memory usage is {{ $value }}%"

  # 数据库告警
  - alert: PostgreSQLDown
    expr: up{job="postgresql"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "PostgreSQL is down"
      description: "PostgreSQL has been down for more than 1 minute"

  - alert: PostgreSQLTooManyConnections
    expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "PostgreSQL too many connections"
      description: "PostgreSQL has {{ $value }}% connections used"

  # Redis告警
  - alert: RedisDown
    expr: up{job="redis"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Redis is down"
      description: "Redis has been down for more than 1 minute"

  - alert: RedisHighMemoryUsage
    expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Redis high memory usage"
      description: "Redis memory usage is {{ $value }}%"
```

### Grafana仪表板

#### 1. 应用监控仪表板
```json
{
  "dashboard": {
    "title": "MDM Application Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(django_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(django_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(django_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(django_requests_total{status=~\"4..|5..\"}[5m])",
            "legendFormat": "{{status}}"
          }
        ]
      },
      {
        "title": "Active Users",
        "type": "stat",
        "targets": [
          {
            "expr": "django_active_requests",
            "legendFormat": "Active Requests"
          }
        ]
      }
    ]
  }
}
```

#### 2. 业务监控仪表板
```json
{
  "dashboard": {
    "title": "MDM Business Metrics",
    "panels": [
      {
        "title": "Device Status Distribution",
        "type": "piechart",
        "targets": [
          {
            "expr": "mdm_devices_total",
            "legendFormat": "{{status}}"
          }
        ]
      },
      {
        "title": "Loan Applications",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(mdm_loans_total[1h])",
            "legendFormat": "{{status}}"
          }
        ]
      },
      {
        "title": "User Activity",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(mdm_user_activity_total[5m])",
            "legendFormat": "{{action}}"
          }
        ]
      }
    ]
  }
}
```

---

## 📝 日志管理

### 日志架构

#### 1. 日志收集配置
```yaml
# logging/filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/django.log
    - /app/logs/celery.log
  fields:
    service: mdm-backend
    environment: production
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

- type: log
  enabled: true
  paths:
    - /var/log/nginx/access.log
    - /var/log/nginx/error.log
  fields:
    service: nginx
    environment: production

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "mdm-logs-%{+yyyy.MM.dd}"

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
- add_docker_metadata: ~
- add_kubernetes_metadata: ~
```

#### 2. 结构化日志配置
```python
# backend/config/logging.py
import os
import logging.config

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/django.log',
            'maxBytes': 1024*1024*100,  # 100MB
            'backupCount': 10,
            'formatter': 'json'
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/django_error.log',
            'maxBytes': 1024*1024*100,  # 100MB
            'backupCount': 10,
            'formatter': 'json'
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['error_file'],
            'level': 'ERROR',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

#### 3. 业务日志记录
```python
# backend/apps/common/logging.py
import logging
import json
from django.contrib.auth import get_user_model

User = get_user_model()

class BusinessLogger:
    """业务日志记录器"""
    
    def __init__(self):
        self.logger = logging.getLogger('apps.business')
    
    def log_user_action(self, user: User, action: str, resource: str, 
                       resource_id: str = None, details: dict = None):
        """记录用户操作日志"""
        log_data = {
            'event_type': 'user_action',
            'user_id': str(user.id),
            'username': user.username,
            'action': action,
            'resource': resource,
            'resource_id': resource_id,
            'details': details or {},
            'ip_address': getattr(user, '_ip_address', None),
            'user_agent': getattr(user, '_user_agent', None),
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_device_operation(self, device, operation: str, user: User, 
                           old_status: str = None, new_status: str = None):
        """记录设备操作日志"""
        log_data = {
            'event_type': 'device_operation',
            'device_id': str(device.id),
            'device_name': device.name,
            'operation': operation,
            'user_id': str(user.id),
            'username': user.username,
            'old_status': old_status,
            'new_status': new_status,
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_loan_event(self, loan, event: str, user: User, details: dict = None):
        """记录借用事件日志"""
        log_data = {
            'event_type': 'loan_event',
            'loan_id': str(loan.id),
            'device_id': str(loan.device.id),
            'borrower_id': str(loan.borrower.id),
            'event': event,
            'operator_id': str(user.id),
            'operator_username': user.username,
            'details': details or {},
        }
        
        self.logger.info(json.dumps(log_data))
    
    def log_security_event(self, event_type: str, user: User = None, 
                          ip_address: str = None, details: dict = None):
        """记录安全事件日志"""
        log_data = {
            'event_type': 'security_event',
            'security_event_type': event_type,
            'user_id': str(user.id) if user else None,
            'username': user.username if user else None,
            'ip_address': ip_address,
            'details': details or {},
        }
        
        self.logger.warning(json.dumps(log_data))

# 全局业务日志实例
business_logger = BusinessLogger()
```

### 日志分析

#### 1. Kibana仪表板配置
```json
{
  "version": "7.15.0",
  "objects": [
    {
      "id": "mdm-logs-dashboard",
      "type": "dashboard",
      "attributes": {
        "title": "MDM Logs Dashboard",
        "panelsJSON": "[{\"version\":\"7.15.0\",\"panelIndex\":\"1\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelRefName\":\"panel_1\",\"embeddableConfig\":{}}]"
      }
    },
    {
      "id": "error-logs-visualization",
      "type": "visualization",
      "attributes": {
        "title": "Error Logs Over Time",
        "visState": "{\"title\":\"Error Logs Over Time\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":\"true\",\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"customInterval\":\"2h\",\"min_doc_count\":1,\"extended_bounds\":{}}}]}"
      }
    }
  ]
}
```

#### 2. 日志查询示例
```bash
# 查询错误日志
GET /mdm-logs-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"term": {"levelname": "ERROR"}},
        {"range": {"@timestamp": {"gte": "now-1h"}}}
      ]
    }
  },
  "sort": [{"@timestamp": {"order": "desc"}}]
}

# 查询用户操作日志
GET /mdm-logs-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"term": {"event_type": "user_action"}},
        {"term": {"action": "device_borrow"}}
      ]
    }
  },
  "aggs": {
    "users": {
      "terms": {"field": "username.keyword"}
    }
  }
}

# 查询安全事件
GET /mdm-logs-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"term": {"event_type": "security_event"}},
        {"range": {"@timestamp": {"gte": "now-24h"}}}
      ]
    }
  }
}
```

---

## 🚨 告警机制

### 告警配置

#### 1. AlertManager配置
```yaml
# monitoring/alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
  - match:
      severity: warning
    receiver: 'warning-alerts'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://webhook-service:5000/alerts'

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '[CRITICAL] MDM Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#alerts-critical'
    title: 'Critical Alert'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'warning-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '[WARNING] MDM Alert: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}

inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'instance']
```

#### 2. 自定义告警处理
```python
# backend/monitoring/alerts.py
import requests
import logging
from django.conf import settings
from typing import Dict, List

logger = logging.getLogger(__name__)

class AlertHandler:
    """告警处理器"""
    
    def __init__(self):
        self.webhook_url = settings.ALERT_WEBHOOK_URL
        self.slack_webhook = settings.SLACK_WEBHOOK_URL
    
    def send_alert(self, alert_type: str, message: str, severity: str = 'warning',
                   details: Dict = None):
        """发送告警"""
        alert_data = {
            'alert_type': alert_type,
            'message': message,
            'severity': severity,
            'details': details or {},
            'timestamp': time.time(),
            'service': 'mdm-platform'
        }
        
        try:
            # 发送到告警系统
            response = requests.post(
                self.webhook_url,
                json=alert_data,
                timeout=10
            )
            response.raise_for_status()
            
            # 如果是严重告警，同时发送到Slack
            if severity == 'critical':
                self.send_slack_alert(alert_data)
                
        except Exception as e:
            logger.error(f'Failed to send alert: {e}')
    
    def send_slack_alert(self, alert_data: Dict):
        """发送Slack告警"""
        slack_message = {
            'text': f"🚨 {alert_data['alert_type']}: {alert_data['message']}",
            'attachments': [
                {
                    'color': 'danger' if alert_data['severity'] == 'critical' else 'warning',
                    'fields': [
                        {
                            'title': 'Severity',
                            'value': alert_data['severity'],
                            'short': True
                        },
                        {
                            'title': 'Service',
                            'value': alert_data['service'],
                            'short': True
                        }
                    ]
                }
            ]
        }
        
        try:
            requests.post(self.slack_webhook, json=slack_message, timeout=10)
        except Exception as e:
            logger.error(f'Failed to send Slack alert: {e}')

# 全局告警处理实例
alert_handler = AlertHandler()
```

### 告警规则

#### 1. 业务告警规则
```python
# backend/monitoring/business_alerts.py
from django.core.management.base import BaseCommand
from django.db.models import Count
from django.utils import timezone
from datetime import timedelta
from apps.devices.models import Device
from apps.loans.models import LoanApplication
from .alerts import alert_handler

class BusinessAlertsChecker:
    """业务告警检查器"""
    
    def check_device_alerts(self):
        """检查设备相关告警"""
        # 检查设备利用率过低
        total_devices = Device.objects.filter(is_deleted=False).count()
        borrowed_devices = Device.objects.filter(
            status='borrowed',
            is_deleted=False
        ).count()
        
        if total_devices > 0:
            utilization_rate = borrowed_devices / total_devices
            if utilization_rate < 0.3:  # 利用率低于30%
                alert_handler.send_alert(
                    'low_device_utilization',
                    f'Device utilization rate is {utilization_rate:.1%}',
                    'warning',
                    {'total_devices': total_devices, 'borrowed_devices': borrowed_devices}
                )
        
        # 检查长期未归还设备
        overdue_loans = LoanApplication.objects.filter(
            status='borrowed',
            expected_end_date__lt=timezone.now() - timedelta(days=7)
        )
        
        if overdue_loans.exists():
            alert_handler.send_alert(
                'overdue_devices',
                f'{overdue_loans.count()} devices are overdue for more than 7 days',
                'warning',
                {'overdue_count': overdue_loans.count()}
            )
    
    def check_user_alerts(self):
        """检查用户相关告警"""
        # 检查异常登录
        from django.contrib.auth.models import User
        recent_logins = User.objects.filter(
            last_login__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        if recent_logins > 100:  # 1小时内登录超过100次
            alert_handler.send_alert(
                'high_login_rate',
                f'{recent_logins} logins in the last hour',
                'warning',
                {'login_count': recent_logins}
            )
    
    def check_system_alerts(self):
        """检查系统相关告警"""
        # 检查待审批申请积压
        pending_loans = LoanApplication.objects.filter(
            status='pending',
            created_at__lt=timezone.now() - timedelta(hours=24)
        ).count()
        
        if pending_loans > 10:
            alert_handler.send_alert(
                'pending_loans_backlog',
                f'{pending_loans} loan applications pending for more than 24 hours',
                'warning',
                {'pending_count': pending_loans}
            )

# 定时任务检查业务告警
class Command(BaseCommand):
    help = 'Check business alerts'
    
    def handle(self, *args, **options):
        checker = BusinessAlertsChecker()
        checker.check_device_alerts()
        checker.check_user_alerts()
        checker.check_system_alerts()
        self.stdout.write('Business alerts check completed')
```

---

## 📈 性能监控

### 应用性能监控

#### 1. 数据库性能监控
```python
# backend/monitoring/db_monitoring.py
from django.db import connection
from django.core.management.base import BaseCommand
import time
import logging

logger = logging.getLogger(__name__)

class DatabaseMonitor:
    """数据库监控"""
    
    def get_slow_queries(self, threshold_seconds=1.0):
        """获取慢查询"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT query, mean_time, calls, total_time
                FROM pg_stat_statements
                WHERE mean_time > %s
                ORDER BY mean_time DESC
                LIMIT 10
            """, [threshold_seconds * 1000])
            
            return cursor.fetchall()
    
    def get_connection_stats(self):
        """获取连接统计"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    count(*) as total_connections,
                    count(*) FILTER (WHERE state = 'active') as active_connections,
                    count(*) FILTER (WHERE state = 'idle') as idle_connections
                FROM pg_stat_activity
                WHERE datname = current_database()
            """)
            
            return cursor.fetchone()
    
    def get_table_stats(self):
        """获取表统计信息"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_tuples,
                    n_dead_tup as dead_tuples
                FROM pg_stat_user_tables
                ORDER BY n_live_tup DESC
                LIMIT 10
            """)
            
            return cursor.fetchall()
    
    def monitor_and_alert(self):
        """监控并告警"""
        # 检查慢查询
        slow_queries = self.get_slow_queries()
        if slow_queries:
            logger.warning(f'Found {len(slow_queries)} slow queries')
            for query in slow_queries:
                logger.warning(f'Slow query: {query[0][:100]}... (avg: {query[1]}ms)')
        
        # 检查连接数
        conn_stats = self.get_connection_stats()
        if conn_stats[0] > 80:  # 总连接数超过80
            logger.warning(f'High connection count: {conn_stats[0]}')
        
        # 检查死元组
        table_stats = self.get_table_stats()
        for stat in table_stats:
            if stat[6] > stat[5] * 0.1:  # 死元组超过活元组的10%
                logger.warning(f'High dead tuples in {stat[1]}: {stat[6]}')
```

#### 2. 缓存性能监控
```python
# backend/monitoring/cache_monitoring.py
from django.core.cache import cache
from django.core.management.base import BaseCommand
import redis
import logging

logger = logging.getLogger(__name__)

class CacheMonitor:
    """缓存监控"""
    
    def __init__(self):
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL)
    
    def get_redis_info(self):
        """获取Redis信息"""
        info = self.redis_client.info()
        return {
            'used_memory': info['used_memory'],
            'used_memory_human': info['used_memory_human'],
            'used_memory_peak': info['used_memory_peak'],
            'used_memory_peak_human': info['used_memory_peak_human'],
            'connected_clients': info['connected_clients'],
            'total_commands_processed': info['total_commands_processed'],
            'keyspace_hits': info['keyspace_hits'],
            'keyspace_misses': info['keyspace_misses'],
        }
    
    def get_cache_hit_rate(self):
        """获取缓存命中率"""
        info = self.redis_client.info()
        hits = info['keyspace_hits']
        misses = info['keyspace_misses']
        
        if hits + misses == 0:
            return 0
        
        return hits / (hits + misses)
    
    def monitor_and_alert(self):
        """监控并告警"""
        info = self.get_redis_info()
        hit_rate = self.get_cache_hit_rate()
        
        # 检查内存使用
        if info['used_memory'] > 1024 * 1024 * 1024:  # 1GB
            logger.warning(f"High Redis memory usage: {info['used_memory_human']}")
        
        # 检查命中率
        if hit_rate < 0.8:  # 命中率低于80%
            logger.warning(f"Low cache hit rate: {hit_rate:.2%}")
        
        # 检查连接数
        if info['connected_clients'] > 100:
            logger.warning(f"High Redis connections: {info['connected_clients']}")
```

---

**文档维护**: 本文档由运维负责人负责维护，每月更新一次，监控配置变更时及时更新。
