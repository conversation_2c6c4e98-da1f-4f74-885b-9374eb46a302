# 🔌 API设计规范文档

**版本**: v1.0.0  
**更新时间**: 2025年7月28日  
**维护者**: 技术负责人  
**API版本**: v1

---

## 📋 目录

1. [设计原则](#设计原则)
2. [API概览](#api概览)
3. [认证授权](#认证授权)
4. [接口规范](#接口规范)
5. [错误处理](#错误处理)
6. [接口文档](#接口文档)

---

## 🎯 设计原则

### RESTful设计原则
1. **资源导向**：URL表示资源，HTTP方法表示操作
2. **无状态**：每个请求包含所有必要信息
3. **统一接口**：使用标准HTTP方法和状态码
4. **分层系统**：支持缓存、负载均衡等中间层
5. **按需代码**：支持客户端扩展（可选）

### API设计规范
1. **版本控制**：通过URL路径进行版本控制 `/api/v1/`
2. **命名规范**：使用复数名词，小写字母，连字符分隔
3. **HTTP方法**：正确使用GET、POST、PUT、PATCH、DELETE
4. **状态码**：使用标准HTTP状态码
5. **响应格式**：统一JSON格式，包含数据和元信息

---

## 🌐 API概览

### 基础URL
```
开发环境: http://localhost:8000/api/
生产环境: https://your-domain.com/api/
```

### 模块划分
```
/api/
├── auth/           # 用户认证模块
├── devices/        # 设备管理模块
├── loans/          # 借用管理模块
├── notifications/  # 通知管理模块
├── reports/        # 报表统计模块
├── health/         # 健康检查
├── docs/           # API文档
└── schema/         # OpenAPI Schema
```

---

## 🔐 认证授权

### JWT认证机制
```http
Authorization: Bearer <access_token>
```

### 认证流程
1. **登录获取Token**：`POST /api/auth/login/`
2. **使用Token访问**：在请求头中携带Token
3. **刷新Token**：`POST /api/auth/token/refresh/`
4. **登出销毁Token**：`POST /api/auth/logout/`

### 权限级别
| 角色 | 权限说明 |
|------|----------|
| super_admin | 系统最高权限，可访问所有接口 |
| device_admin | 设备管理权限，可管理设备和审批借用 |
| device_owner | 设备归属者权限，可管理自己的设备 |
| normal_user | 普通用户权限，可申请借用设备 |

---

## 📝 接口规范

### 1. 用户认证模块 (`/api/auth/`)

#### 用户登录
```http
POST /api/auth/login/
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应**：
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "uuid",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "super_admin",
    "role_display": "超级管理员"
  }
}
```

#### 用户注册
```http
POST /api/auth/register/
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirm": "password123",
  "first_name": "张",
  "last_name": "三",
  "department": "技术部"
}
```

#### 获取用户信息
```http
GET /api/auth/profile/
Authorization: Bearer <access_token>
```

#### 更新用户信息
```http
PATCH /api/auth/update/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "first_name": "李",
  "last_name": "四",
  "phone": "13800138000"
}
```

### 2. 设备管理模块 (`/api/devices/`)

#### 获取设备列表
```http
GET /api/devices/?page=1&page_size=20&status=available&search=iPhone
Authorization: Bearer <access_token>
```

**查询参数**：
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `status`: 设备状态筛选
- `search`: 搜索关键词
- `category`: 设备分类筛选
- `owner`: 归属者筛选

**响应**：
```json
{
  "count": 100,
  "next": "http://localhost:8000/api/devices/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "name": "iPhone 14 Pro",
      "model": "A2894",
      "serial_number": "F2LW8J9N14",
      "brand": "Apple",
      "device_number": "DEV20240001",
      "status": "available",
      "status_display": "可借用",
      "owner_info": {
        "id": "uuid",
        "username": "admin",
        "email": "<EMAIL>"
      },
      "category_info": {
        "id": 1,
        "name": "智能手机"
      },
      "created_at": "2025-07-28T10:00:00Z"
    }
  ]
}
```

#### 创建设备
```http
POST /api/devices/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "iPhone 15 Pro",
  "model": "A3102",
  "serial_number": "F2LW8J9N15",
  "brand": "Apple",
  "category": 1,
  "cpu": "A17 Pro",
  "memory": "8GB",
  "storage": "256GB",
  "purchase_price": 8999.00,
  "purchase_date": "2025-07-28"
}
```

#### 更新设备状态
```http
POST /api/devices/{id}/change-status/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "status": "maintenance",
  "notes": "设备进入维修状态"
}
```

### 3. 借用管理模块 (`/api/loans/`)

#### 创建借用申请
```http
POST /api/loans/applications/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "device": "device_uuid",
  "reason": "项目开发需要",
  "expected_start_date": "2025-07-29T09:00:00Z",
  "expected_end_date": "2025-08-05T18:00:00Z",
  "priority": "normal"
}
```

#### 审批借用申请
```http
POST /api/loans/applications/{id}/approve/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "action": "approve",
  "notes": "申请通过，请按时归还"
}
```

#### 开始借用
```http
POST /api/loans/applications/{id}/start/
Authorization: Bearer <access_token>
```

#### 归还设备
```http
POST /api/loans/applications/{id}/return/
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "condition": "良好",
  "damage_report": ""
}
```

### 4. 报表统计模块 (`/api/reports/`)

#### 获取仪表盘统计
```http
GET /api/reports/dashboard/
Authorization: Bearer <access_token>
```

**响应**：
```json
{
  "total_devices": 150,
  "available_devices": 80,
  "borrowed_devices": 45,
  "maintenance_devices": 15,
  "total_users": 50,
  "active_loans": 45,
  "pending_applications": 8,
  "overdue_loans": 3
}
```

---

## ⚠️ 错误处理

### 标准错误格式
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "username": ["该字段是必填项"],
      "email": ["请输入有效的邮箱地址"]
    }
  },
  "timestamp": "2025-07-28T10:00:00Z",
  "path": "/api/auth/register/"
}
```

### 常见错误码
| HTTP状态码 | 错误码 | 说明 |
|------------|--------|------|
| 400 | VALIDATION_ERROR | 请求参数验证失败 |
| 401 | AUTHENTICATION_FAILED | 认证失败 |
| 403 | PERMISSION_DENIED | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 409 | CONFLICT | 资源冲突 |
| 429 | RATE_LIMIT_EXCEEDED | 请求频率超限 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

---

## 📊 响应格式规范

### 成功响应格式
```json
{
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2025-07-28T10:00:00Z"
}
```

### 分页响应格式
```json
{
  "count": 100,
  "next": "http://localhost:8000/api/devices/?page=3",
  "previous": "http://localhost:8000/api/devices/?page=1",
  "results": [
    // 数据列表
  ]
}
```

### 列表筛选参数
- `page`: 页码
- `page_size`: 每页数量 (最大100)
- `ordering`: 排序字段 (支持 `-` 前缀倒序)
- `search`: 搜索关键词
- 其他模型特定筛选参数

---

## 🔍 健康检查接口

### 基础健康检查
```http
GET /api/health/
```

### 就绪状态检查
```http
GET /api/health/ready/
```

### 存活状态检查
```http
GET /api/health/live/
```

### 系统信息
```http
GET /api/system/info/
```

---

## 📚 API文档

### 在线文档
- **Swagger UI**: http://localhost:8000/api/docs/
- **ReDoc**: http://localhost:8000/api/redoc/
- **OpenAPI Schema**: http://localhost:8000/api/schema/

### 文档特性
1. **交互式测试**：直接在文档中测试API
2. **自动生成**：基于代码自动生成文档
3. **实时更新**：代码变更后文档自动更新
4. **多格式支持**：支持JSON、YAML格式

---

## 🚀 性能优化

### 缓存策略
1. **查询缓存**：对频繁查询的数据进行缓存
2. **分页优化**：使用游标分页提高性能
3. **预加载**：使用select_related和prefetch_related
4. **压缩响应**：启用GZIP压缩

### 限流策略
1. **用户限流**：每用户每分钟100次请求
2. **IP限流**：每IP每分钟1000次请求
3. **接口限流**：敏感接口单独限流
4. **突发处理**：支持短时间突发请求

---

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0.0 | 2025-07-28 | 初始版本，包含所有核心API接口 | 技术负责人 |

---

*最后更新：2025年7月28日*
