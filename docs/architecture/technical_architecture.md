# 设备管理平台技术架构文档

## 1. 技术栈确认

### 1.1 后端技术栈（最终确定）
- **Web框架**: Django 4.2.7 + Django REST Framework 3.14.0
- **数据库**: PostgreSQL 15 + Redis 7
- **认证**: Django内置认证 + JWT (djangorestframework-simplejwt 5.3.0)
- **任务队列**: Celery 5.3.4 + Redis
- **文件存储**: Django-storages 1.14.2 + 本地存储/云存储
- **API文档**: drf-spectacular 0.26.5 (OpenAPI 3.0)
- **测试**: pytest 7.4.3 + pytest-django 4.6.0 + factory-boy 3.3.0
- **缓存**: Redis + Django缓存框架
- **图片处理**: Pillow 10.0.1
- **二维码生成**: qrcode 7.4.2 + Pillow
- **WSGI服务器**: Gunicorn 21.2.0
- **静态文件**: WhiteNoise 6.6.0

### 1.2 前端技术栈
- **框架**: Vue.js 3.3.8 + Composition API + TypeScript 5.2.2
- **构建工具**: Vite 6.0.0
- **UI组件库**: Element Plus 2.4.2
- **图标**: @element-plus/icons-vue 2.1.0
- **状态管理**: Pinia 2.1.7
- **HTTP客户端**: Axios 1.6.0
- **路由**: Vue Router 4.2.5
- **样式**: SCSS + PostCSS + Autoprefixer
- **图表**: ECharts 5.6.0 + vue-echarts 6.7.3
- **表格**: @tanstack/vue-table 8.10.7
- **表单验证**: VeeValidate 4.11.8
- **日期处理**: dayjs 1.11.10

### 1.3 基础设施
- **Web服务器**: Nginx (最新稳定版)
- **应用服务器**: Gunicorn 21.2.0
- **数据库**: PostgreSQL 15-alpine
- **缓存/消息队列**: Redis 7-alpine
- **监控**: Prometheus + Grafana
- **日志**: 集中化日志管理
- **容器化**: Docker & Docker Compose (最新版)
- **CI/CD**: GitLab CI 或 GitHub Actions
- **安全**: Sentry SDK 1.38.0 + Cryptography 41.0.7

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │   CDN/静态资源   │    │   移动端 (H5)    │
└─────────┬───────┘    └─────────────────┘    └─────────┬───────┘
          │                                              │
          └──────────────────┬───────────────────────────┘
                             │
                    ┌────────▼────────┐
                    │   Nginx (反向代理) │
                    └────────┬────────┘
                             │
                    ┌────────▼────────┐
                    │  Django + DRF   │
                    │   (API服务器)    │
                    └────────┬────────┘
                             │
          ┌──────────────────┼──────────────────┐
          │                  │                  │
    ┌─────▼─────┐    ┌───────▼───────┐    ┌─────▼─────┐
    │PostgreSQL │    │     Redis     │    │  Celery   │
    │  (主数据库) │    │ (缓存/消息队列) │    │ (异步任务) │
    └───────────┘    └───────────────┘    └───────────┘
```

### 2.2 数据库架构
```
┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │      Redis      │
│   (主数据库)     │    │                 │
│                 │    │ - 缓存数据       │
│ - 用户数据       │    │ - 会话存储       │
│ - 设备信息       │    │ - 消息队列       │
│ - 借用记录       │    │ - 分布式锁       │
│ - 审计日志       │    └─────────────────┘
│ - 系统配置       │
└─────────────────┘
```

### 2.3 服务层架构
```
┌─────────────────────────────────────────────────────────┐
│                    API Layer (DRF)                     │
├─────────────────────────────────────────────────────────┤
│                  Service Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │DeviceService│ │ LoanService │ │ UserService │ ...  │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                   Model Layer (ORM)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │
│  │   Device    │ │    Loan     │ │    User     │ ...  │
│  └─────────────┘ └─────────────┘ └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                 Database Layer                         │
│              PostgreSQL + Redis                        │
└─────────────────────────────────────────────────────────┘
```

## 3. 核心模块设计

### 3.1 认证授权模块
```python
# 使用Django内置User模型扩展
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100)
    phone = models.CharField(max_length=20)
    is_active = models.BooleanField(default=True)

# JWT认证配置
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}
```

### 3.2 权限控制模块
```python
# 基于Django权限系统的扩展
class DevicePermission(BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.user.is_superuser:
            return True
        if request.user.profile.role == 'device_admin':
            return True
        if request.user.profile.role == 'device_owner':
            return obj.owner == request.user
        return False
```

### 3.3 设备状态机模块
```python
from django_fsm import FSMField, transition

class Device(models.Model):
    status = FSMField(default='in_stock', choices=STATUS_CHOICES)
    
    @transition(field=status, source='in_stock', target='available')
    def make_available(self):
        pass
    
    @transition(field=status, source='available', target='borrowed')
    def borrow(self):
        pass
```

### 3.4 异步任务模块
```python
# Celery任务配置
@shared_task
def send_notification(user_id, message_type, context):
    """发送通知任务"""
    pass

@shared_task
def cleanup_deleted_devices():
    """清理已删除设备任务"""
    pass

@shared_task
def generate_report(report_type, user_id):
    """生成报表任务"""
    pass
```

## 4. 数据库设计优化

### 4.1 索引策略
```sql
-- 设备表关键索引
CREATE INDEX idx_device_status ON devices(status);
CREATE INDEX idx_device_owner ON devices(owner_id);
CREATE INDEX idx_device_serial ON devices(serial_number);
CREATE INDEX idx_device_deleted ON devices(is_deleted, deleted_at);

-- 借用记录表索引
CREATE INDEX idx_loan_device ON device_loans(device_id);
CREATE INDEX idx_loan_borrower ON device_loans(borrower_id);
CREATE INDEX idx_loan_status ON device_loans(status);
CREATE INDEX idx_loan_dates ON device_loans(loan_start, loan_end);
```

### 4.2 分区策略
```sql
-- 审计日志表按月分区
CREATE TABLE audit_logs (
    id SERIAL,
    created_at TIMESTAMP NOT NULL,
    ...
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE audit_logs_2024_01 PARTITION OF audit_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

## 5. 缓存策略

### 5.1 Redis缓存配置
```python
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# 缓存策略
- 设备列表: 缓存5分钟
- 用户权限: 缓存30分钟  
- 系统配置: 缓存1小时
- 统计数据: 缓存15分钟
```

### 5.2 分布式锁
```python
from django_redis import get_redis_connection

def acquire_lock(key, timeout=10):
    """获取分布式锁"""
    redis_conn = get_redis_connection("default")
    return redis_conn.set(f"lock:{key}", "1", nx=True, ex=timeout)
```

## 6. 安全架构

### 6.1 API安全
```python
# API限流配置
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }
}
```

### 6.2 数据加密
```python
from cryptography.fernet import Fernet

class EncryptedField(models.TextField):
    """加密字段"""
    def from_db_value(self, value, expression, connection):
        if value is None:
            return value
        return decrypt_data(value)
    
    def to_python(self, value):
        return decrypt_data(value) if value else value
```

## 7. 部署架构

### 7.1 Docker配置
```dockerfile
# Dockerfile.backend
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "config.wsgi:application"]
```

### 7.2 Docker Compose配置
```yaml
version: '3.8'
services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: device_management
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
  
  redis:
    image: redis:7-alpine
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    depends_on:
      - backend
```

## 8. 监控和日志

### 8.1 应用监控
```python
# Django监控配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### 8.2 性能监控
```python
# 使用django-prometheus监控
INSTALLED_APPS = [
    'django_prometheus',
    # ... 其他应用
]

MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',
    # ... 其他中间件
    'django_prometheus.middleware.PrometheusAfterMiddleware',
]
```
