# 🗄️ 数据库设计文档

**版本**: v1.0.0  
**更新时间**: 2025年7月28日  
**维护者**: 技术负责人  
**数据库**: PostgreSQL 15+ / SQLite (开发环境)

---

## 📋 目录

1. [设计原则](#设计原则)
2. [数据模型概览](#数据模型概览)
3. [核心表结构](#核心表结构)
4. [关系图](#关系图)
5. [索引设计](#索引设计)
6. [数据字典](#数据字典)
7. [迁移管理](#迁移管理)

---

## 🎯 设计原则

### 核心原则
1. **遵循Django ORM规范**：充分利用Django的ORM特性
2. **使用UUID主键**：提高安全性，避免ID猜测攻击
3. **软删除机制**：重要数据不物理删除，保留审计痕迹
4. **审计字段**：记录创建时间、更新时间、操作人等信息
5. **外键约束**：保证数据一致性和完整性
6. **索引优化**：针对查询频繁的字段建立索引

### 命名规范
- **表名**：使用复数形式，如 `users`, `devices`
- **字段名**：使用下划线分隔，如 `created_at`, `device_number`
- **外键字段**：使用 `_id` 后缀，如 `owner_id`, `category_id`
- **布尔字段**：使用 `is_` 前缀，如 `is_active`, `is_deleted`

---

## 📊 数据模型概览

### 模块划分
```
用户管理模块 (users)
├── User - 用户基础信息
├── UserProfile - 用户扩展信息
└── LoginLog - 登录日志

设备管理模块 (devices)
├── DeviceCategory - 设备分类
├── Device - 设备信息
└── DeviceStatusLog - 设备状态变更日志

借用管理模块 (loans)
├── LoanApplication - 借用申请
├── LoanRecord - 借用记录
└── LoanExtension - 借用延期

通知管理模块 (notifications)
├── Notification - 系统通知
└── NotificationTemplate - 通知模板

报表管理模块 (reports)
└── ReportCache - 报表缓存
```

---

## 🏗️ 核心表结构

### 1. 用户管理模块

#### users 表 (用户基础信息)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(150) UNIQUE NOT NULL,
    email VARCHAR(254) UNIQUE NOT NULL,
    password VARCHAR(128) NOT NULL,
    first_name VARCHAR(150),
    last_name VARCHAR(150),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'normal_user',
    department VARCHAR(100),
    employee_id VARCHAR(50) UNIQUE,
    avatar VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_staff BOOLEAN DEFAULT false,
    is_superuser BOOLEAN DEFAULT false,
    date_joined TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    login_count INTEGER DEFAULT 0,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**字段说明**：
- `role`: 用户角色 (super_admin, device_admin, device_owner, normal_user)
- `employee_id`: 员工编号，用于企业内部管理
- `failed_login_attempts`: 失败登录次数，用于账户安全
- `locked_until`: 账户锁定时间，防止暴力破解

#### user_profiles 表 (用户扩展信息)
```sql
CREATE TABLE user_profiles (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(200),
    email_notifications BOOLEAN DEFAULT true,
    sms_notifications BOOLEAN DEFAULT false,
    wechat_notifications BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'zh-hans',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### login_logs 表 (登录日志)
```sql
CREATE TABLE login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    ip_address INET,
    user_agent TEXT,
    login_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    logout_time TIMESTAMP WITH TIME ZONE,
    session_duration INTEGER,
    login_method VARCHAR(20) DEFAULT 'password',
    is_successful BOOLEAN DEFAULT true,
    failure_reason VARCHAR(100)
);
```

### 2. 设备管理模块

#### device_categories 表 (设备分类)
```sql
CREATE TABLE device_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id BIGINT REFERENCES device_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### devices 表 (设备信息)
```sql
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    model VARCHAR(100) NOT NULL,
    serial_number VARCHAR(100) UNIQUE NOT NULL,
    brand VARCHAR(100) NOT NULL,
    device_number VARCHAR(50) UNIQUE,
    category_id BIGINT REFERENCES device_categories(id),
    
    -- 技术规格
    cpu VARCHAR(200),
    gpu VARCHAR(200),
    memory VARCHAR(100),
    storage VARCHAR(100),
    resolution VARCHAR(50),
    screen_size VARCHAR(50),
    special_screen VARCHAR(100),
    
    -- 系统信息
    os VARCHAR(100),
    os_version VARCHAR(100),
    
    -- 管理信息
    status VARCHAR(20) DEFAULT 'in_stock',
    owner_id UUID REFERENCES users(id),
    current_user_id UUID REFERENCES users(id),
    
    -- 采购信息
    purchase_price DECIMAL(10,2),
    purchase_date DATE,
    warranty_period VARCHAR(100),
    
    -- 其他信息
    special_notes TEXT,
    image VARCHAR(100),
    qr_code VARCHAR(100),
    
    -- 软删除和审计
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by_id UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by_id UUID REFERENCES users(id),
    updated_by_id UUID REFERENCES users(id)
);
```

**设备状态说明**：
- `in_stock`: 库存中
- `available`: 可借用
- `idle`: 闲置
- `locked`: 锁定
- `borrowed`: 借出中
- `maintenance`: 维修中
- `lost`: 丢失
- `scrapped`: 报废

### 3. 借用管理模块

#### loan_applications 表 (借用申请)
```sql
CREATE TABLE loan_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID NOT NULL REFERENCES devices(id),
    borrower_id UUID NOT NULL REFERENCES users(id),
    approver_id UUID REFERENCES users(id),
    
    status VARCHAR(20) DEFAULT 'pending',
    priority VARCHAR(10) DEFAULT 'normal',
    
    expected_start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    expected_end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    
    reason TEXT NOT NULL,
    approval_notes TEXT,
    return_condition TEXT,
    damage_report TEXT,
    
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**申请状态说明**：
- `pending`: 待审批
- `approved`: 已批准
- `rejected`: 已拒绝
- `cancelled`: 已取消
- `borrowed`: 借用中
- `returned`: 已归还
- `overdue`: 超期

---

## 🔗 关系图

### 核心实体关系
```mermaid
erDiagram
    User ||--o{ Device : owns
    User ||--o{ LoanApplication : applies
    User ||--o{ LoanApplication : approves
    Device ||--o{ LoanApplication : "is borrowed"
    DeviceCategory ||--o{ Device : categorizes
    User ||--|| UserProfile : extends
    User ||--o{ LoginLog : logs
    Device ||--o{ DeviceStatusLog : tracks
    LoanApplication ||--o{ LoanExtension : extends
```

### 详细关系说明
1. **用户-设备关系**：一个用户可以拥有多个设备（owner关系）
2. **用户-借用关系**：一个用户可以申请借用多个设备
3. **设备-分类关系**：每个设备属于一个分类
4. **借用-延期关系**：一个借用申请可以有多次延期

---

## 📈 索引设计

### 主要索引
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_department ON users(department);
CREATE INDEX idx_users_is_active ON users(is_active);

-- 设备表索引
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_owner ON devices(owner_id);
CREATE INDEX idx_devices_serial_number ON devices(serial_number);
CREATE INDEX idx_devices_soft_delete ON devices(is_deleted, deleted_at);
CREATE INDEX idx_devices_category ON devices(category_id);

-- 借用申请表索引
CREATE INDEX idx_loan_applications_borrower ON loan_applications(borrower_id);
CREATE INDEX idx_loan_applications_device ON loan_applications(device_id);
CREATE INDEX idx_loan_applications_status ON loan_applications(status);
CREATE INDEX idx_loan_applications_dates ON loan_applications(expected_start_date, expected_end_date);

-- 复合索引
CREATE INDEX idx_devices_status_owner ON devices(status, owner_id);
CREATE INDEX idx_loan_applications_status_borrower ON loan_applications(status, borrower_id);
```

### 索引优化策略
1. **查询频率优先**：为经常查询的字段建立索引
2. **复合索引**：为多字段查询建立复合索引
3. **避免过度索引**：平衡查询性能和写入性能
4. **定期维护**：定期分析和优化索引使用情况

---

## 📚 数据字典

### 枚举值定义

#### 用户角色 (User.role)
| 值 | 显示名称 | 权限说明 |
|---|---------|----------|
| super_admin | 超级管理员 | 系统最高权限，可管理所有功能 |
| device_admin | 设备管理员 | 可管理设备信息、审批借用申请 |
| device_owner | 设备归属者 | 可管理自己的设备、查看借用情况 |
| normal_user | 普通用户 | 可申请借用设备、查看个人记录 |

#### 设备状态 (Device.status)
| 值 | 显示名称 | 状态说明 |
|---|---------|----------|
| in_stock | 库存中 | 设备已入库，未分配归属者 |
| available | 可借用 | 设备可供借用 |
| idle | 闲置 | 设备暂时不可借用 |
| locked | 锁定 | 设备被管理员锁定 |
| borrowed | 借出中 | 设备正在被借用 |
| maintenance | 维修中 | 设备正在维修 |
| lost | 丢失 | 设备丢失 |
| scrapped | 报废 | 设备已报废 |

#### 借用状态 (LoanApplication.status)
| 值 | 显示名称 | 状态说明 |
|---|---------|----------|
| pending | 待审批 | 申请已提交，等待审批 |
| approved | 已批准 | 申请已批准，可以借用 |
| rejected | 已拒绝 | 申请被拒绝 |
| cancelled | 已取消 | 申请被取消 |
| borrowed | 借用中 | 设备正在借用中 |
| returned | 已归还 | 设备已归还 |
| overdue | 超期 | 借用超过预期时间 |

---

## 🔧 数据库配置

### 开发环境 (SQLite)
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'device_management.db',
    }
}
```

### 生产环境 (PostgreSQL)
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'device_management',
        'USER': 'admin',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

---

## 📊 性能优化建议

### 查询优化
1. **使用select_related**：减少数据库查询次数
2. **使用prefetch_related**：优化多对多和反向外键查询
3. **分页查询**：避免一次性加载大量数据
4. **缓存策略**：对频繁查询的数据进行缓存

### 数据维护
1. **定期清理**：清理过期的日志和临时数据
2. **数据归档**：将历史数据归档到其他存储
3. **索引维护**：定期分析和重建索引
4. **统计信息更新**：保持数据库统计信息最新

---

## 🔄 迁移管理

### 迁移策略
Django使用迁移系统来管理数据库结构变更，确保开发、测试和生产环境的数据库结构一致性。

### 迁移文件管理
```bash
# 查看迁移状态
python manage.py showmigrations

# 生成迁移文件
python manage.py makemigrations [app_name]

# 应用迁移
python manage.py migrate [app_name] [migration_name]

# 回滚迁移
python manage.py migrate [app_name] [previous_migration_name]
```

### 常见迁移场景

#### 1. 字段约束修改
当模型字段约束与数据库不一致时（如employee_id字段的null约束问题）：

```python
# 迁移文件示例: 0002_alter_user_employee_id.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='employee_id',
            field=models.CharField(
                blank=True,
                max_length=50,
                null=True,
                unique=True,
                verbose_name='员工编号'
            ),
        ),
    ]
```

#### 2. 数据迁移
需要修改现有数据时：

```python
# 数据迁移示例
from django.db import migrations

def update_employee_ids(apps, schema_editor):
    User = apps.get_model('users', 'User')
    for user in User.objects.filter(employee_id__isnull=True):
        user.employee_id = f"USER_{user.username.upper()}_{user.id.hex[:8]}"
        user.save()

def reverse_employee_ids(apps, schema_editor):
    # 回滚逻辑
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('users', '0002_alter_user_employee_id'),
    ]

    operations = [
        migrations.RunPython(update_employee_ids, reverse_employee_ids),
    ]
```

### 迁移最佳实践

#### 开发阶段
1. **频繁生成迁移**：每次模型变更后立即生成迁移文件
2. **检查迁移内容**：确认生成的迁移文件符合预期
3. **测试迁移**：在本地环境测试迁移的正确性

#### 生产部署
1. **备份数据库**：执行迁移前必须备份生产数据库
2. **测试环境验证**：在与生产环境相同的测试环境中验证迁移
3. **分步执行**：复杂迁移分步执行，便于问题定位和回滚

#### 团队协作
1. **迁移文件版本控制**：所有迁移文件必须提交到版本控制系统
2. **冲突解决**：多人开发时注意解决迁移文件冲突
3. **文档记录**：重要迁移操作需要在文档中记录

### 故障排除

#### 迁移冲突
```bash
# 查看冲突的迁移
python manage.py showmigrations --plan

# 合并迁移冲突
python manage.py makemigrations --merge
```

#### 迁移回滚
```bash
# 回滚到指定迁移
python manage.py migrate users 0001

# 完全回滚应用的所有迁移
python manage.py migrate users zero
```

#### 强制重置迁移（仅开发环境）
```bash
# 删除迁移文件（保留__init__.py）
rm apps/users/migrations/0*.py

# 重新生成初始迁移
python manage.py makemigrations users --name initial

# 伪应用迁移（如果数据库已存在表）
python manage.py migrate users --fake-initial
```

### 监控和维护

#### 迁移状态检查
定期检查各环境的迁移状态，确保一致性：

```bash
# 生产环境迁移状态检查脚本
#!/bin/bash
echo "=== 迁移状态检查 ==="
python manage.py showmigrations | grep -E "^\[.\]"
echo "=== 未应用的迁移 ==="
python manage.py showmigrations | grep "^\[ \]"
```

#### 性能监控
监控迁移对数据库性能的影响：
- 大表结构变更的执行时间
- 索引重建的影响
- 锁表时间和业务影响

---

## 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0.0 | 2025-07-28 | 初始版本，包含所有核心表结构 | 技术负责人 |
| v1.1.0 | 2025-07-30 | 增加迁移管理章节，修复用户注册同步问题 | 技术负责人 |

---

*最后更新：2025年7月30日*
