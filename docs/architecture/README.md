# 系统架构设计

## 📋 架构概览

MDM 设备管理系统采用现代化的前后端分离架构，基于微服务理念设计，支持容器化部署和水平扩展。

## 🏗️ 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │   CDN/静态资源   │    │   移动端 (H5)    │
└─────────┬───────┘    └─────────────────┘    └─────────┬───────┘
          │                                              │
          └──────────────────┬───────────────────────────┘
                             │
                    ┌────────▼────────┐
                    │   Nginx (反向代理) │
                    └────────┬────────┘
                             │
                    ┌────────▼────────┐
                    │   Django (后端)  │
                    └────────┬────────┘
                             │
          ┌──────────────────┼──────────────────┐
          │                  │                  │
    ┌─────▼─────┐    ┌───────▼───────┐    ┌─────▼─────┐
    │PostgreSQL │    │     Redis     │    │  Celery   │
    │  (数据库)  │    │   (缓存队列)   │    │ (任务队列) │
    └───────────┘    └───────────────┘    └───────────┘
```

## 💻 技术栈

### 前端技术栈
- **框架**: Vue.js 3.3.8 + Composition API
- **语言**: TypeScript 5.2.2
- **构建工具**: Vite 6.0.0
- **UI库**: Element Plus 2.4.2
- **状态管理**: Pinia 2.1.7
- **路由**: Vue Router 4.2.5
- **HTTP客户端**: Axios 1.6.0
- **图表**: ECharts 5.6.0

### 后端技术栈
- **框架**: Django 4.2.7
- **API**: Django REST Framework 3.14.0
- **认证**: Django REST Framework SimpleJWT 5.3.0
- **语言**: Python 3.11
- **WSGI服务器**: Gunicorn 21.2.0
- **任务队列**: Celery 5.3.4
- **API文档**: drf-spectacular 0.26.5

### 数据存储
- **主数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **文件存储**: 本地存储 + 云存储支持
- **静态文件**: WhiteNoise 6.6.0

### 基础设施
- **容器化**: Docker & Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: 集中化日志管理

## 📚 架构文档

### 详细设计
- [技术架构](technical_architecture.md) - 详细技术架构设计
- [数据库设计](database-design.md) - 数据模型和表结构
- [API设计](api-design.md) - RESTful API设计规范

### 部署架构
- [部署指南](../deployment/README.md) - 生产环境部署
- [Docker最佳实践](../deployment/DOCKER_BEST_PRACTICES.md) - 容器化部署

## 🔧 核心模块

### 用户管理模块
- **功能**: 用户注册、登录、权限管理
- **技术**: Django Auth + JWT
- **数据库**: User, UserProfile 表

### 设备管理模块
- **功能**: 设备信息管理、状态跟踪
- **技术**: Django Models + PostgreSQL
- **数据库**: Device, DeviceCategory 表

### 借用管理模块
- **功能**: 借用申请、审批流程、归还管理
- **技术**: Django Workflow + Celery
- **数据库**: Loan, LoanApproval 表

### 通知模块
- **功能**: 系统通知、邮件提醒
- **技术**: Celery + Redis
- **集成**: 邮件服务、短信服务

## 🌍 部署架构

### 开发环境 (dev)
```yaml
services:
  frontend:    # Vue.js 开发服务器
  backend:     # Django 开发服务器
  db:          # PostgreSQL 数据库
  redis:       # Redis 缓存
```

### 生产环境 (prod)
```yaml
services:
  nginx:       # 反向代理和静态文件服务
  frontend:    # Vue.js 生产构建
  backend:     # Django + Gunicorn
  db:          # PostgreSQL 数据库
  redis:       # Redis 缓存和队列
  celery:      # 异步任务处理
  monitoring:  # Prometheus + Grafana
```

### 精简生产环境 (prod-lite)
```yaml
services:
  nginx:       # 反向代理
  app:         # 前后端合并服务
  db:          # PostgreSQL 数据库
  redis:       # Redis 缓存
```

## 🔒 安全架构

### 认证授权
- **JWT Token**: 无状态认证
- **RBAC**: 基于角色的访问控制
- **CORS**: 跨域资源共享配置
- **CSRF**: 跨站请求伪造防护

### 数据安全
- **数据加密**: 敏感数据加密存储
- **传输安全**: HTTPS/TLS 加密
- **SQL注入防护**: ORM 参数化查询
- **XSS防护**: 输入输出过滤

### 系统安全
- **容器安全**: Docker 安全配置
- **网络安全**: 防火墙和网络隔离
- **日志审计**: 操作日志记录
- **备份恢复**: 数据备份策略

## 📊 性能架构

### 缓存策略
- **Redis缓存**: 热点数据缓存
- **数据库缓存**: 查询结果缓存
- **静态文件缓存**: CDN 和浏览器缓存
- **API缓存**: 接口响应缓存

### 优化策略
- **数据库优化**: 索引优化、查询优化
- **前端优化**: 代码分割、懒加载
- **网络优化**: 压缩、CDN 加速
- **并发优化**: 异步处理、队列机制

## 🔍 监控架构

### 应用监控
- **性能监控**: 响应时间、吞吐量
- **错误监控**: 异常捕获和报告
- **业务监控**: 关键指标跟踪
- **用户行为**: 访问统计分析

### 基础设施监控
- **服务器监控**: CPU、内存、磁盘
- **数据库监控**: 连接数、查询性能
- **网络监控**: 带宽、延迟
- **容器监控**: Docker 容器状态

## 🚀 扩展架构

### 水平扩展
- **负载均衡**: Nginx 负载均衡
- **数据库分片**: 读写分离
- **缓存集群**: Redis 集群
- **微服务**: 服务拆分

### 高可用
- **服务冗余**: 多实例部署
- **数据备份**: 定期备份策略
- **故障转移**: 自动故障切换
- **灾难恢复**: 异地备份

## 📞 技术支持

- **架构设计**: 技术架构师
- **开发团队**: 前后端开发工程师
- **运维团队**: DevOps 工程师
- **文档维护**: 开发团队

---

*最后更新: 2025-07-30*
*文档版本: v2.4*
*架构版本: v2.3+*
