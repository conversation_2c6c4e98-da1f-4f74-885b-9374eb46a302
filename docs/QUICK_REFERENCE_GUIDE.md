# ⚡ MDM 设备管理系统快速参考指南

**版本**: v2.4
**更新时间**: 2025年7月30日
**适用范围**: MDM 设备管理系统 v2.3+

---

## 🎯 核心原则

### 项目管理原则
- **透明化管理**: 所有决策过程公开透明，可追溯
- **责任明确**: 每个角色职责清晰，避免推诿
- **质量优先**: 质量门禁不可妥协
- **预防胜于治疗**: 通过规范和流程预防问题

### 技术开发原则
- **代码质量**: 遵循编码规范，通过代码审查
- **测试驱动**: 单元测试覆盖率 ≥ 80%
- **安全第一**: 所有代码必须通过安全扫描
- **性能优先**: 接口响应时间 P95 < 2秒

---

## 📋 日常工作检查清单

### 开发人员日常清单

#### 每日必做
- [ ] 查看并处理分配给自己的问题
- [ ] 提交代码前运行完整测试套件
- [ ] 代码提交使用规范的commit message
- [ ] 参加每日站会，汇报进度和问题

#### 每周必做
- [ ] 更新相关技术文档
- [ ] 参与代码审查，审查他人代码
- [ ] 检查和更新单元测试
- [ ] 关注系统监控指标

#### 代码提交前检查
- [ ] 代码格式化 (black, prettier)
- [ ] 静态代码检查通过 (flake8, eslint)
- [ ] 单元测试全部通过
- [ ] 代码覆盖率达标
- [ ] 安全扫描无高危问题
- [ ] 提交信息符合规范

### 测试人员日常清单

#### 每日必做
- [ ] 执行当日测试计划
- [ ] 更新测试用例和测试数据
- [ ] 跟踪和验证缺陷修复
- [ ] 记录测试执行结果

#### 每周必做
- [ ] 分析测试覆盖率和缺陷趋势
- [ ] 更新自动化测试脚本
- [ ] 参与需求评审和技术评审
- [ ] 整理测试报告

#### 测试执行前检查
- [ ] 测试环境准备就绪
- [ ] 测试数据准备完成
- [ ] 测试用例评审通过
- [ ] 自动化测试脚本可用

### 运维人员日常清单

#### 每日必做
- [ ] 检查系统监控指标
- [ ] 处理告警和异常事件
- [ ] 检查备份任务执行状态
- [ ] 更新运维日志

#### 每周必做
- [ ] 分析系统性能趋势
- [ ] 检查安全扫描结果
- [ ] 更新监控配置
- [ ] 容量规划评估

#### 部署前检查
- [ ] 部署脚本测试通过
- [ ] 回滚方案准备就绪
- [ ] 监控告警配置更新
- [ ] 相关团队通知到位

---

## 🚨 应急响应流程

### P0级紧急问题 (系统宕机)

#### 立即行动 (15分钟内)
1. **确认问题**: 验证问题影响范围
2. **启动应急**: 通知应急响应团队
3. **初步诊断**: 快速定位问题原因
4. **临时措施**: 实施临时解决方案

#### 短期处理 (2小时内)
1. **根因分析**: 深入分析问题根本原因
2. **永久修复**: 实施永久解决方案
3. **验证测试**: 确认问题完全解决
4. **服务恢复**: 恢复正常服务

#### 后续跟进 (24小时内)
1. **事后分析**: 编写详细的事故报告
2. **改进措施**: 制定预防措施
3. **流程优化**: 优化应急响应流程
4. **团队培训**: 分享经验教训

### 联系方式
- **技术负责人**: [电话] [邮箱]
- **运维负责人**: [电话] [邮箱]
- **项目经理**: [电话] [邮箱]

---

## 🔧 常用命令和工具

### 开发环境

#### 后端开发 (Django)
```bash
# 启动开发服务器
python manage.py runserver

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 运行测试
python manage.py test
pytest --cov=apps --cov-report=html

# 代码质量检查
black .
isort .
flake8 .
mypy .
```

#### 前端开发 (Vue.js)
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test:unit
npm run test:e2e

# 代码质量检查
npm run lint
npm run type-check
```

#### Docker 操作
```bash
# 构建镜像
docker build -t mdm-backend -f docker/Dockerfile.backend ./backend
docker build -t mdm-frontend -f docker/Dockerfile.frontend ./frontend

# 启动开发环境
docker compose -f docker-compose.dev.yml up -d

# 启动生产环境
docker compose -f docker-compose.prod.yml up -d

# 启动开发工具（可选）
docker compose -f docker-compose.dev.yml --profile tools up -d

# 查看日志
docker compose logs -f backend
docker compose logs -f frontend

# 查看服务状态
docker compose ps

# 停止服务
docker compose down

# 停止并删除卷
docker compose down -v
```

### 生产环境

#### 部署操作
```bash
# 部署到开发环境
docker compose -f docker-compose.dev.yml up -d --build

# 部署到预生产环境
docker compose -f docker-compose.staging.yml up -d --build

# 部署到生产环境
docker compose -f docker-compose.prod.yml up -d --build

# 健康检查
docker compose exec backend python manage.py check
docker compose exec backend curl -f http://localhost:8000/api/health/

# 查看容器状态
docker compose ps
docker compose top

# 重启服务
docker compose restart backend
docker compose restart frontend

# 回滚操作（使用特定镜像标签）
docker compose -f docker-compose.prod.yml down
docker compose -f docker-compose.prod.yml up -d --build
```

#### 监控命令
```bash
# 查看系统状态
kubectl get pods -n mdm-prod
kubectl get services -n mdm-prod

# 查看日志
kubectl logs -f deployment/mdm-backend -n mdm-prod
kubectl logs -f deployment/mdm-frontend -n mdm-prod

# 查看资源使用
kubectl top pods -n mdm-prod
kubectl top nodes
```

---

## 📊 关键指标监控

### 系统健康指标

#### 应用指标
- **响应时间**: P95 < 2秒, P99 < 5秒
- **错误率**: < 0.1%
- **吞吐量**: 根据业务需求设定
- **可用性**: ≥ 99.9%

#### 基础设施指标
- **CPU使用率**: < 80%
- **内存使用率**: < 85%
- **磁盘使用率**: < 80%
- **网络延迟**: < 100ms

#### 业务指标
- **用户活跃度**: 日活用户数
- **功能使用率**: 各功能模块使用情况
- **设备利用率**: 设备借用率和周转率
- **用户满意度**: ≥ 4.5/5.0

### 告警阈值

#### 严重告警 (立即处理)
- 系统宕机或不可访问
- 错误率 > 1%
- 响应时间 P95 > 10秒
- CPU/内存使用率 > 90%

#### 警告告警 (1小时内处理)
- 错误率 > 0.5%
- 响应时间 P95 > 5秒
- CPU/内存使用率 > 80%
- 磁盘使用率 > 85%

---

## 🔍 问题排查指南

### 常见问题快速诊断

#### 1. 用户无法登录
**检查步骤**:
1. 确认用户名密码正确性
2. 检查账户是否被锁定
3. 验证JWT令牌是否有效
4. 检查数据库连接状态
5. 查看认证服务日志

**常见原因**:
- 密码错误或过期
- 账户被管理员禁用
- 会话超时
- 数据库连接问题

#### 1.1 用户注册数据不同步
**检查步骤**:
1. 验证API响应状态码和内容
2. 检查数据库迁移状态
3. 查看Django应用日志
4. 验证模型字段约束
5. 测试数据库事务完整性

**常见原因**:
- 数据库迁移未完成或不一致
- 字段约束冲突（如NOT NULL约束）
- 数据库事务回滚但API返回成功
- 模型定义与数据库结构不匹配

**解决方案**:
```bash
# 检查迁移状态
python manage.py showmigrations

# 生成并应用迁移
python manage.py makemigrations
python manage.py migrate

# 验证用户创建
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
print(f'用户总数: {User.objects.count()}')
"
```

#### 2. 接口响应慢
**检查步骤**:
1. 查看接口响应时间监控
2. 检查数据库查询性能
3. 分析慢查询日志
4. 检查缓存命中率
5. 查看服务器资源使用

**常见原因**:
- 数据库查询未优化
- 缓存失效或未命中
- 服务器资源不足
- 网络延迟问题

#### 3. 系统内存不足
**检查步骤**:
1. 查看内存使用情况
2. 分析内存泄漏点
3. 检查垃圾回收情况
4. 查看进程内存占用
5. 分析内存增长趋势

**常见原因**:
- 内存泄漏
- 缓存数据过多
- 大对象未及时释放
- 并发请求过多

### 日志查看位置

#### 应用日志
```bash
# Django应用日志
tail -f /app/logs/django.log
tail -f /app/logs/django_error.log

# Nginx访问日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Celery任务日志
tail -f /app/logs/celery.log
```

#### 系统日志
```bash
# 系统日志
journalctl -u mdm-backend -f
journalctl -u mdm-frontend -f

# Docker容器日志
docker logs -f mdm-backend
docker logs -f mdm-frontend

# Kubernetes Pod日志
kubectl logs -f pod/mdm-backend-xxx -n mdm-prod
```

---

## 📚 快速链接

### 文档链接
- [文档总览](README.md)
- [项目概览](project/README.md)
- [技术架构](architecture/technical_architecture.md)
- [开发指南](development/DEV-GUIDE.md)
- [部署指南](deployment/README.md)
- [监控系统](monitoring/MONITORING_SYSTEM.md)
- [问题注册表](MASTER_ISSUE_REGISTRY.md)
- [安全规范](security/SECURITY_STANDARDS.md)

### 工具链接
- **代码仓库**: [Git Repository URL]
- **项目管理**: [Jira/Azure DevOps URL]
- **CI/CD**: [GitLab CI/GitHub Actions URL]
- **监控系统**: [Grafana Dashboard URL]
- **日志系统**: [Kibana URL]
- **文档平台**: [Confluence/Notion URL]

### 环境链接
- **开发环境**: https://dev.mdm.company.com
- **预生产环境**: https://staging.mdm.company.com
- **生产环境**: https://mdm.company.com
- **API文档**: https://api.mdm.company.com/docs/

---

## 🎓 学习资源

### 新人入门
1. **第1天**: 阅读项目治理文档，了解团队结构
2. **第2-3天**: 学习技术规范，搭建开发环境
3. **第4-5天**: 熟悉开发流程，完成第一个小任务
4. **第2周**: 深入学习业务逻辑，参与代码审查
5. **第3-4周**: 独立完成功能开发，学习运维知识

### 技能提升
- **编程技能**: 定期参加技术分享和代码审查
- **架构设计**: 学习系统设计和最佳实践
- **运维技能**: 了解Docker、Kubernetes、监控系统
- **安全意识**: 学习安全编码和威胁防护

### 认证考试
- **云平台认证**: AWS/Azure/GCP 相关认证
- **容器技术**: Docker/Kubernetes 认证
- **安全认证**: CISSP/CEH 等安全认证
- **项目管理**: PMP/Scrum Master 认证

---

## 📞 紧急联系方式

### 技术支持
- **技术负责人**: [姓名] - [电话] - [邮箱]
- **运维负责人**: [姓名] - [电话] - [邮箱]
- **安全负责人**: [姓名] - [电话] - [邮箱]

### 管理层
- **项目经理**: [姓名] - [电话] - [邮箱]
- **产品经理**: [姓名] - [电话] - [邮箱]
- **研发经理**: [姓名] - [电话] - [邮箱]

### 外部支持
- **云服务商支持**: [联系方式]
- **第三方服务支持**: [联系方式]
- **硬件供应商支持**: [联系方式]

---

**最后更新**: 2025年7月29日  
**维护者**: 研发经理 & 产品经理

> 💡 **提示**: 本指南提供日常工作的快速参考，详细信息请查阅对应的完整文档。如有疑问，请及时联系相关负责人。
