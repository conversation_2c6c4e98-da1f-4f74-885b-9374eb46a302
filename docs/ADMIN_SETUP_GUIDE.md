# 👤 管理员配置快速指南

**版本**: v1.0.0  
**更新时间**: 2025年7月30日  
**适用场景**: 所有环境的管理员账户配置

---

## 🎯 快速导航

| 环境类型 | 管理员创建方式 | 访问地址 | 默认账户 |
|---------|---------------|----------|----------|
| **开发环境** | 自动创建 | http://localhost:8000/admin/ | admin/admin123 |
| **生产环境** | 手动创建 | http://localhost/admin/ | 需要创建 |
| **精简生产环境** | 手动创建 | http://localhost/admin/ | 需要创建 |

---

## 🔧 开发环境

### 自动创建的管理员
开发环境启动时会自动创建默认管理员账户：

```
用户名: admin
密码: admin123
邮箱: <EMAIL>
权限: 超级管理员
```

### 访问方式
```bash
# 启动开发环境
start-dev.bat

# 访问管理后台
http://localhost:8000/admin/
```

---

## 🚀 生产环境

### ⚠️ 重要提醒
**生产环境不会自动创建任何管理员账户！**

### 快速创建管理员

#### 方法1: 交互式创建（推荐）
```bash
# 启动生产环境
start-prod.bat

# 创建超级管理员
docker exec -it mdm_backend_prod python manage.py createsuperuser

# 按提示输入：
# - 用户名（避免使用admin）
# - 邮箱地址
# - 密码（强密码）
# - 员工编号
```

#### 方法2: 脚本创建
```bash
# 一键创建管理员
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
admin_user = User.objects.create_superuser(
    username='mdm_admin',                # 修改为安全用户名
    email='<EMAIL>',       # 修改为公司邮箱
    password='YourSecurePassword123!',   # 修改为强密码
    employee_id='ADMIN001',              # 修改为实际员工编号
    first_name='系统管理员',
    role='super_admin'
)
print(f'✅ 管理员创建成功: {admin_user.username}')
"
```

### 验证管理员
```bash
# 检查管理员账户
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
admins = User.objects.filter(is_superuser=True)
print(f'管理员数量: {admins.count()}')
for admin in admins:
    print(f'用户名: {admin.username}, 邮箱: {admin.email}')
"
```

---

## ⚡ 精简生产环境

### 创建管理员
```bash
# 启动精简生产环境
start-prod-lite.bat

# 创建管理员（与生产环境相同）
docker exec -it mdm_backend_prod_lite python manage.py createsuperuser
```

---

## 🔒 安全配置建议

### 强密码要求
✅ **推荐密码格式**:
- 长度: 至少12个字符
- 包含: 大写字母、小写字母、数字、特殊字符
- 示例: `MySecure2025!Pass`

❌ **避免使用**:
- 常见密码: password123, admin123456
- 个人信息: 姓名、生日、电话
- 公司信息: 公司名、产品名

### 安全用户名建议
✅ **推荐用户名**:
- `mdm_admin`
- `system_admin`
- `it_zhangsan`

❌ **避免使用**:
- `admin`
- `administrator`
- `root`
- `superuser`

### 创建后安全措施
1. **立即登录测试**: 确认账户可正常使用
2. **完善个人信息**: 填写真实的联系方式
3. **启用通知**: 配置邮件和系统通知
4. **定期维护**: 定期更换密码

---

## 🚨 紧急情况处理

### 忘记管理员密码
```bash
# 重置密码
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
admin = User.objects.get(username='your_admin_username')
admin.set_password('new_secure_password')
admin.save()
print('✅ 密码重置成功')
"
```

### 管理员账户被锁定
```bash
# 解锁账户
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
admin = User.objects.get(username='your_admin_username')
admin.is_active = True
admin.failed_login_attempts = 0
admin.locked_until = None
admin.save()
print('✅ 账户解锁成功')
"
```

### 创建备用管理员
```bash
# 创建备用管理员账户
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
backup_admin = User.objects.create_superuser(
    username='backup_admin',
    email='<EMAIL>',
    password='backup_secure_password',
    employee_id='BACKUP001',
    first_name='备用管理员',
    role='super_admin'
)
print(f'✅ 备用管理员创建成功: {backup_admin.username}')
"
```

---

## 📋 常用命令速查

### 查看所有管理员
```bash
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
for user in User.objects.filter(is_superuser=True):
    print(f'{user.username} - {user.email} - {user.is_active}')
"
```

### 升级普通用户为管理员
```bash
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.get(username='existing_username')
user.is_superuser = True
user.is_staff = True
user.role = 'super_admin'
user.save()
print(f'✅ 用户 {user.username} 已升级为管理员')
"
```

### 降级管理员为普通用户
```bash
docker exec -it mdm_backend_prod python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.get(username='admin_username')
user.is_superuser = False
user.is_staff = False
user.role = 'normal_user'
user.save()
print(f'✅ 用户 {user.username} 已降级为普通用户')
"
```

---

## 📞 技术支持

### 遇到问题？
- **部署问题**: 查看 [生产环境部署指南](deployment/PRODUCTION_DEPLOYMENT.md)
- **环境配置**: 查看 [环境配置指南](../ENVIRONMENT_GUIDE.md)
- **技术支持**: <EMAIL>

### 相关文档
- [生产环境部署指南](deployment/PRODUCTION_DEPLOYMENT.md)
- [环境配置指南](../ENVIRONMENT_GUIDE.md)
- [安全标准](security/SECURITY_STANDARDS.md)

---

## 🔄 环境配置同步状态

### 最新更新 (2025-07-30)

**Celery Beat配置同步**:
- ✅ 所有环境已配置DatabaseScheduler
- ✅ django_celery_beat应用已添加到Django设置
- ✅ 数据库迁移已集成到部署流程
- ✅ 定时任务可通过管理后台管理

**环境一致性**:
- ✅ 开发环境：自动迁移和管理员创建
- ✅ 生产环境：部署时迁移，手动创建管理员
- ✅ 精简生产环境：部署时迁移，手动创建管理员

**新增工具**:
- `scripts/migrate-celery-beat.bat` - Celery Beat迁移
- `scripts/check-env-sync.bat` - 环境同步检查

## ✅ 检查清单

部署完成后，请确认以下项目：

- [ ] 管理员账户创建成功
- [ ] 管理员可以正常登录
- [ ] 管理员权限验证正常
- [ ] 密码符合安全要求
- [ ] 用户名符合安全规范
- [ ] 联系信息已完善
- [ ] 通知设置已配置
- [ ] Celery Beat迁移已执行
- [ ] 定时任务功能正常
- [ ] 备用管理员已创建（可选）

---

## 🐛 问题修复记录

### 添加设备功能修复 (2025-07-30)

**问题描述**: 点击添加设备按钮无网络请求，功能无响应

**修复内容**:
1. **后端API修复**:
   - ✅ 修复`DeviceCreateUpdateSerializer`缺少`id`字段的问题
   - ✅ 重置admin用户密码为`admin123`
   - ✅ 验证设备创建API功能正常

2. **前端调试增强**:
   - ✅ 添加详细的调试日志到设备相关组件
   - ✅ 在API层、Store层、组件层都添加了调试信息
   - ✅ 创建了设备功能测试页面 `/device-test`

3. **环境配置同步**:
   - ✅ 所有环境的Celery Beat配置已同步
   - ✅ Django设置中添加了`django_celery_beat`应用
   - ✅ 数据库迁移已执行

**测试验证**:
- ✅ 后端API测试通过 (`python scripts/test-device-api.py`)
- ✅ 设备创建、获取、删除功能正常
- ✅ 前端调试工具已部署

**使用说明**:
- 访问 `/device-test` 页面进行功能测试
- 在浏览器控制台查看详细的调试信息
- 使用 `debugDeviceAPI.runFullTest()` 进行完整测试

*最后更新: 2025-07-30*
*文档版本: v1.1.0*
