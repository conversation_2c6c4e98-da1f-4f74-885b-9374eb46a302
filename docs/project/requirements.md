# 企业设备管理平台完整需求文档 v2.0

## 1. 项目概述

### 1.1 业务模式
本系统采用"归属者管理制"，即每台设备都有一个固定的归属者（项目负责人或设备管理员），归属者负责设备的分配、审批和监管，其他用户通过向归属者申请来借用设备。

### 1.2 核心业务流程
```
设备入库 → 管理员分配归属者 → 归属者管理设备状态 → 用户申请借用 → 归属者审批 → 设备借出 → 到期归还 → 回到归属者
```

## 2. 用户角色与权限

### 2.1 角色定义
- **超级管理员**：系统管理、全局设备管理
- **设备管理员**：设备录入、归属者分配、采购管理
- **设备归属者**：负责特定设备的管理和借用审批
- **普通用户**：设备借用和归还

### 2.2 详细权限矩阵
| 功能模块 | 超级管理员 | 设备管理员 | 设备归属者 | 普通用户 |
|----------|------------|------------|------------|----------|
| **用户管理** |
| 创建/删除用户 | ✓ | ✗ | ✗ | ✗ |
| 分配权限组 | ✓ | ✗ | ✗ | ✗ |
| **设备管理** |
| 录入新设备 | ✓ | ✓ | ✗ | ✗ |
| 分配归属者 | ✓ | ✓ | ✗ | ✗ |
| 编辑设备信息 | ✓ | ✓ | 仅管理设备 | ✗ |
| 设置设备状态 | ✓ | ✓ | 仅管理设备 | ✗ |
| **借用管理** |
| 申请借用 | ✓ | ✓ | ✓ | ✓ |
| 审批借用 | ✓ | ✓ | 仅管理设备 | ✗ |
| 强制归还 | ✓ | ✓ | 仅管理设备 | ✗ |

## 3. 设备状态系统

### 3.1 设备状态定义
- **库存中**：设备已入库，等待分配归属者
- **可借用**：归属者管理，允许其他人申请借用
- **闲置**：归属者管理，暂不外借（归属者可随时切换为可借用）
- **锁定**：归属者长期占用或指定人员使用，不允许借用申请
- **借出中**：设备已借给他人，显示借用人和预期归还时间
- **维修中**：设备故障维修，不可借用
- **丢失**：设备遗失，仅管理员可设置
- **报废**：设备废弃，终态

### 3.2 状态流转图
```mermaid
graph TD
    A[库存中] --> B[可借用]
    A --> C[闲置]
    A --> D[锁定]
    
    B --> E[借出中]
    B --> C
    B --> D
    B --> F[维修中]
    
    C --> B
    C --> D
    C --> F
    
    D --> B
    D --> C
    D --> F
    
    E --> B
    E --> C
    E --> D
    
    F --> B
    F --> C
    F --> D
    
    B --> G[丢失]
    C --> G
    D --> G
    E --> G
    F --> G
    
    B --> H[报废]
    C --> H
    D --> H
    F --> H
```

### 3.3 状态变更权限
| 状态变更 | 超级管理员 | 设备管理员 | 设备归属者 | 借用人 |
|----------|------------|------------|------------|--------|
| 库存中→可借用/闲置/锁定 | ✓ | ✓ | ✓ | ✗ |
| 可借用↔闲置↔锁定 | ✓ | ✓ | ✓ | ✗ |
| 任意状态→借出中 | ✓ | ✓ | ✓ | ✗ |
| 借出中→原状态 | ✓ | ✓ | ✓ | 申请 |
| 任意状态→维修中 | ✓ | ✓ | ✓ | 申请 |
| 任意状态→丢失/报废 | ✓ | ✓ | ✗ | ✗ |

## 4. 详细功能需求

### 4.1 设备管理

#### 4.1.1 设备录入
- 新设备录入后默认状态为"库存中"
- 必须由管理员分配归属者后才能正常流转
- 支持批量导入（Excel模板）
- 支持设备图片上传和二维码生成

#### 4.1.2 设备信息字段
#### 4.1.2 设备信息字段
```json
{
  "基础信息": {
    "设备名称": "必填",
    "设备型号": "必填", 
    "序列号": "必填，唯一",
    "品牌": "必填",
    "设备编号": "系统自动生成"
  },
  "技术规格": {
    "CPU": "选填",
    "GPU": "选填", 
    "内存": "选填",
    "存储": "选填",
    "分辨率": "选填",
    "屏幕尺寸": "选填",
    "特殊屏幕": "选填，如OLED、曲面屏等"
  },
  "系统信息": {
    "操作系统": "选填",
    "系统版本": "选填"
  },
  "管理信息": {
    "当前状态": "必填",
    "设备归属者": "必填",
    "当前使用人": "系统自动",
    "采购价格": "选填",
    "采购日期": "选填", 
    "保修期": "选填",
    "特殊说明": "选填，如屏幕锁、损坏等",
    "是否已删除": "系统字段",
    "删除时间": "系统字段"
  }
}
```

#### 4.1.3 设备删除与恢复机制
- **软删除设计**：设备删除后进入回收站，不立即物理删除
- **回收站管理**：
  - 显示所有已删除设备的列表
  - 支持单个或批量恢复
  - 数据保留周期可配置（默认30天）
  - 超期后系统自动物理删除
- **恢复权限**：只有超级管理员和设备管理员可以恢复设备
- **恢复状态**：恢复的设备状态重置为"库存中"，需重新分配归属者

#### 4.1.4 状态强制修改机制
- **错误确认救济**：当出现误操作确认时，支持申请状态修改
- **申请流程**：
  - 归属者发起强制状态修改申请
  - 填写修改原因和目标状态
  - 超级管理员审核并决定是否同意
  - 修改操作记录详细日志
- **限制条件**：
  - 只能修改非终态状态（丢失、报废除外）
  - 需要提供充分的修改理由
  - 超级管理员有完全的修改权限

### 4.2 归属者管理

#### 4.2.1 归属者分配与转移
- 新设备入库后，管理员必须分配归属者
- 归属者可以是项目经理、部门负责人或专门的设备管理员
- 支持批量分配和归属者转移
- 归属者变更需要新旧归属者确认

#### 4.2.2 归属者离职处理
- **设备转移模式**：将设备批量转移给新归属者
  - 原归属者发起转移申请
  - 新归属者确认接收
  - 管理员审核通过后完成转移
- **设备入库模式**：设备归还公司统一管理
  - 设备状态重置为"库存中"
  - 等待重新分配归属者
  - 借出中的设备需先完成归还流程

#### 4.2.3 归属者权限
- 查看管理的所有设备状态
- 设置设备为可借用/闲置/锁定状态
- 审批设备借用申请
- 发起临时借出和设备维护申请
- 查看设备借用历史和统计

### 4.3 借用流程设计

#### 4.3.1 正常借用申请流程
```
1. 用户搜索/浏览可借用设备
2. 选择设备，填写借用申请
   - 借用理由（必填）
   - 借用期限（必填）
   - 预期归还时间（必填）
   - 是否长期借用（选填）
3. 系统发送申请给设备归属者
4. 归属者审批（同意/拒绝/修改期限）
5. 审批通过后设备状态变更为"借出中"
6. 系统通知借用人和归属者
```

#### 4.3.2 应急借用流程（临时借出）
```
1. 归属者直接发起临时借出
   - 选择借用人（必填）
   - 借用事由（必填）
   - 预期归还时间（必填，最长7天）
   - 特殊说明（选填）
2. 系统发送确认通知给借用人
3. 借用人确认接收后，设备状态变更为"借出中"
4. 超时处理：24小时未确认则临时借出申请自动取消
5. 到期前1天/当天自动提醒归属者和借用人
6. 到期后系统自动发送确认归还消息给归属者
```

#### 4.3.3 批量借用操作
- **批量借出确认机制**：
  - 归属者选择多台设备进行批量借出
  - 系统生成"批量借出单"，包含设备清单
  - 借用人必须逐一确认收到每台设备
  - 只有全部设备确认收到，批量操作才完成
  - 如有设备未确认，系统标记异常并通知归属者

#### 4.3.4 归还流程
```
1. 借用人申请归还（或系统自动提醒到期）
2. 系统通知归属者准备接收设备
3. 归属者确认收到设备并检查状态
   - 设备完好：恢复为可借用/闲置状态
   - 设备损坏：填写损坏说明，设为维修中
4. 完成归还，记录归还时间
```

#### 4.3.5 批量归还确认机制
- **逐一确认原则**：
  - 归属者必须逐台确认收到设备
  - 系统显示"已收到X台，待收Y台"进度
  - 每台设备需要归属者点击"确认收到"
  - 可标记设备状态：完好/损坏/丢失
  - 只有全部确认后，批量归还才完成

#### 4.3.6 异常情况处理

**网络异常处理：**
- 批量操作过程中网络中断，整个批量申请自动拒绝
- 用户需要重新发起批量申请
- 已确认的部分操作不保留，避免数据不一致

**借用冲突处理：**
- 同一设备收到多个借用申请时，按时间顺序排队
- 归属者审批通过第一个申请后，其余申请自动拒绝
- 系统自动通知被拒绝的申请人，建议选择其他设备

**临时借出超时：**
- 临时借出发起后，借用人24小时内必须确认接收
- 超时未确认，临时借出申请自动取消
- 归属者需要通过线下沟通确认后重新发起

**常规借用超期：**
- 系统每日提醒，超期7天自动标记为异常
- 借用人离职：自动将其借用设备转为"待处理"状态
- 紧急收回：归属者可强制要求归还，借用人必须配合
- 设备损坏：借用期间损坏需要填写损坏报告

### 4.4 设备维护系统

#### 4.4.1 维护类型定义
- **系统维护**：系统升级、密码重置、软件安装
- **硬件维护**：电池更换、屏幕维修、清洁保养
- **安全维护**：病毒查杀、安全补丁、数据清理
- **定期保养**：按计划进行的预防性维护

#### 4.4.2 维护申请流程
```
1. 归属者发起维护申请
   - 选择维护类型（必填）
   - 维护原因（必填）
   - 预期维护时长（必填）
   - 维护详情描述（必填）
   - 是否外送维修（选填）
2. 系统自动将设备状态设为"维修中"
3. 如需外送维修，需要管理员审批
4. 维护完成后，归属者确认并恢复设备状态
```

#### 4.4.3 维护记录管理
- 记录每次维护的详细信息
- 维护前后状态对比
- 维护费用记录（如有）
- 维护效果评价

#### 4.4.4 定期维护提醒
- 系统根据设备类型设置维护周期
- 自动提醒归属者设备需要保养
- 支持维护计划的制定和执行

### 4.5 消息通知系统

#### 4.5.1 通知类型
- **借用申请**：通知归属者有新的借用申请
- **审批结果**：通知申请人审批通过/拒绝
- **临时借出**：通知借用人确认接收设备
- **归还提醒**：借用到期前3天、1天、当天提醒
- **临时借出到期**：到期前1天/当天提醒归属者和借用人
- **批量操作确认**：提醒相关人员确认设备收发
- **超期通知**：超期后每日通知借用人和归属者
- **维护提醒**：设备维护计划和维护完成通知
- **状态变更**：设备状态变更通知相关人员
- **归属者转移**：设备归属权变更通知

#### 4.5.2 通知渠道
- 站内消息（必须）
- 邮件通知（可配置）
- 企业微信/钉钉（可配置）

#### 4.5.3 消息优先级
- **紧急**：应急借用、设备丢失、强制归还
- **重要**：借用申请、归还确认、超期通知
- **普通**：状态变更、维护提醒、定期统计

### 4.5 报表统计

#### 4.5.1 设备统计
- 设备状态分布（饼图）
- 设备利用率统计（按时间、部门、归属者）
- 设备借用频次排行
- 设备故障率统计

#### 4.5.2 用户统计  
- 用户借用次数排行
- 超期用户统计
- 归属者工作量统计

#### 4.5.3 财务统计
- 设备资产统计
- 采购成本分析
- 维修费用统计

## 5. 页面设计要求

### 5.1 设备列表页面
显示字段：
- 序号
- 设备名称
- 型号
- 系统版本
- CPU/GPU/内存
- 分辨率/尺寸
- 特殊屏幕
- 当前状态（带颜色标识）
- 归属者
- 当前使用人
- 预期归还时间
- 特殊说明
- 操作按钮（根据状态和权限显示）

### 5.2 操作按钮逻辑
| 设备状态 | 普通用户 | 设备归属者 | 管理员 |
|----------|----------|------------|--------|
| 可借用 | [申请借用] | [修改状态][临时借出][申请维护][查看详情] | [修改状态][分配归属者] |
| 闲置 | - | [改为可借用][临时借出][申请维护][查看详情] | [修改状态][分配归属者] |
| 锁定 | - | [修改状态][申请维护][查看详情] | [修改状态][分配归属者] |
| 借出中 | [查看详情] | [强制归还][查看详情] | [强制归还][修改状态] |
| 维修中 | - | [维护完成][查看详情] | [修改状态][分配归属者] |

### 5.3 批量操作界面
- **批量选择**：支持多选设备进行批量操作
- **批量借出界面**：
  - 显示选中设备列表
  - 填写统一的借用信息
  - 逐一确认每台设备的接收状态
  - 实时显示确认进度："已确认 X/Y 台设备"
- **批量归还界面**：
  - 显示待归还设备列表
  - 逐一确认每台设备的收回状态
  - 可标记设备状态（完好/损坏/丢失）
  - 支持部分确认和异常处理

### 5.4 应急借用快捷界面
- **快速借出按钮**：在设备详情页添加快速借出功能
- **临时借出表单**：
  - 借用人搜索选择
  - 借用时长快速选择（1小时、半天、1天、3天、7天）
  - 借用事由快填模板
  - 一键发起借出

## 6. 技术实现方案

### 6.1 数据库设计要点

#### 6.1.1 核心表结构
```sql
-- 设备表（含软删除字段）
devices (
    id, name, model, serial_number, brand,
    cpu, gpu, memory, storage, resolution, screen_size,
    os, os_version, purchase_price, purchase_date,
    status, owner_id, current_user_id, special_notes,
    is_deleted, deleted_at, -- 软删除字段
    created_at, updated_at
)

-- 强制状态修改申请表
status_change_requests (
    id, device_id, requester_id, current_status, 
    target_status, reason, admin_notes,
    status, -- 'pending'/'approved'/'rejected'
    requested_at, processed_at, processed_by
)

-- 回收站清理日志
deletion_logs (
    id, device_id, device_info, -- JSON格式存储设备快照
    deleted_by, deletion_reason, 
    auto_cleanup_date, actual_cleanup_date
)

-- 借用记录表
device_loans (
    id, device_id, borrower_id, owner_id,
    loan_type, -- 'normal'正常借用/'temporary'临时借出
    loan_reason, loan_start, loan_end, expected_return,
    actual_return, status, approval_notes,
    created_at, updated_at
)

-- 批量操作记录表
batch_operations (
    id, operation_type, -- 'batch_loan'/'batch_return'
    operator_id, device_count, confirmed_count,
    status, -- 'pending'/'completed'/'failed'
    created_at, updated_at
)

-- 批量操作明细表
batch_operation_details (
    id, batch_id, device_id, status, -- 'pending'/'confirmed'/'failed'
    confirmed_at, notes
)

-- 设备维护记录表
device_maintenances (
    id, device_id, owner_id, maintenance_type,
    reason, description, start_date, end_date,
    cost, status, notes,
    created_at, updated_at
)

-- 归属者转移记录表
owner_transfers (
    id, device_id, old_owner_id, new_owner_id,
    transfer_type, -- 'transfer'/'return_to_stock'
    reason, status, -- 'pending'/'confirmed'/'rejected'
    requested_at, confirmed_at
)

-- 状态变更日志
device_status_logs (
    id, device_id, old_status, new_status,
    operator_id, reason, created_at
)
```

### 6.2 关键业务逻辑实现

#### 6.2.1 设备状态机
```python
class DeviceStatusMachine:
    VALID_TRANSITIONS = {
        'in_stock': ['available', 'idle', 'locked'],
        'available': ['borrowed', 'idle', 'locked', 'maintenance'],
        'idle': ['available', 'locked', 'maintenance'],
        'locked': ['available', 'idle', 'maintenance'],
        'borrowed': ['available', 'idle', 'locked'],
        'maintenance': ['available', 'idle', 'locked'],
        # 终态
        'lost': [],
        'scrapped': []
    }
```

#### 6.2.2 批量操作控制器
```python
class BatchOperationController:
    def create_batch_loan(self, device_ids, borrower_id, loan_info):
        """创建批量借出操作"""
        batch = BatchOperation.create(
            operation_type='batch_loan',
            device_count=len(device_ids)
        )
        
        for device_id in device_ids:
            BatchOperationDetail.create(
                batch_id=batch.id,
                device_id=device_id,
                status='pending'
            )
        
        # 发送确认通知
        self.send_batch_confirmation_notification(batch.id)
        return batch
    
    def confirm_device_received(self, batch_id, device_id, user_id):
        """确认单台设备接收"""
        detail = BatchOperationDetail.get(batch_id, device_id)
        detail.status = 'confirmed'
        detail.confirmed_at = datetime.now()
        
        # 检查是否全部确认完成
        if self.check_batch_completed(batch_id):
            self.complete_batch_operation(batch_id)

#### 6.2.3 异常处理控制器
```python
class ExceptionHandlingController:
    def handle_batch_operation_failure(self, batch_id, error_type):
        """处理批量操作失败"""
        batch = BatchOperation.get(batch_id)
        batch.status = 'failed'
        
        # 回滚所有已变更的设备状态
        details = BatchOperationDetail.filter(batch_id=batch_id)
        for detail in details:
            if detail.status == 'confirmed':
                self.rollback_device_status(detail.device_id)
        
        # 通知相关用户操作失败，需要重新申请
        self.notify_batch_operation_failed(batch_id)

    def handle_loan_conflict(self, device_id, conflicting_requests):
        """处理借用冲突"""
        # 按申请时间排序，只保留最早的申请
        earliest_request = min(conflicting_requests, key=lambda x: x.created_at)
        
        # 拒绝其他申请
        for request in conflicting_requests:
            if request.id != earliest_request.id:
                request.status = 'auto_rejected'
                self.notify_loan_rejected(request.id, reason='设备已被他人申请')

class SoftDeleteController:
    def soft_delete_device(self, device_id, reason):
        """软删除设备"""
        device = Device.get(device_id)
        device.is_deleted = True
        device.deleted_at = datetime.now()
        
        # 记录删除日志
        DeletionLog.create(
            device_id=device_id,
            device_info=device.to_json(),
            deleted_by=current_user.id,
            deletion_reason=reason,
            auto_cleanup_date=datetime.now() + timedelta(days=30)
        )
    
    def restore_device(self, device_id):
        """恢复已删除设备"""
        device = Device.get_deleted(device_id)
        device.is_deleted = False
        device.deleted_at = None
        device.status = 'in_stock'  # 恢复后重置为库存中
        device.owner_id = None      # 清除原归属者
```
```

## 7. 阶段性实施计划

### Phase 1：基础框架（2周）
**Week 1:**
- 项目架构搭建（Flask + SQLAlchemy）
- 数据库设计和创建
- 用户认证系统（Flask-Login）
- 基础API框架

**Week 2:**
- 用户管理模块
- 权限控制系统
- 基础前端框架（Vue.js + Element Plus）

### Phase 2：设备管理核心（3周）
**Week 3:**
- 设备CRUD功能
- 设备状态管理
- 归属者分配功能

**Week 4:**  
- 设备列表页面
- 设备详情页面
- 批量导入功能

**Week 5:**
- 设备状态变更日志
- 设备搜索和筛选
- 图片上传功能

### Phase 3：借用流程（3周）
**Week 6:**
- 借用申请功能
- 审批流程实现
- 状态流转逻辑

**Week 7:**
- 归还流程
- 消息通知系统
- 到期提醒功能

**Week 8:**
- 超期处理
- 强制归还功能
- 异常情况处理

### Phase 4：报表统计（2周）
**Week 9:**
- 基础统计报表
- 设备利用率分析
- 用户行为统计

**Week 10:**
- 数据可视化
- 报表导出功能
- 移动端适配

### Phase 5：部署上线（1周）
**Week 11:**
- 生产环境部署
- 性能测试和优化
- 用户培训和文档

## 8. 关键技术难点

### 8.1 并发控制
- 借用申请的并发处理
- 设备状态的原子性更新
- 分布式锁的使用

### 8.2 消息可靠性
- 消息队列的使用（Celery + Redis）
- 消息失败重试机制
- 推送服务的集成

### 8.3 数据一致性
- 设备状态与借用记录的一致性
- 分布式事务处理
- 数据库事务管理

## 9. 验收标准

### 9.1 功能验收
- 设备状态流转正确率 100%
- 借用流程完整性测试通过
- 权限控制测试无漏洞
- 并发测试无数据不一致

### 9.2 性能验收
- 设备列表页面加载时间 < 2秒
- 支持1000+设备同时管理
- 支持100+用户并发使用

### 9.3 用户验收
- 核心流程操作简便性评分 > 4.0/5.0
- 用户培训通过率 > 90%
- 系统稳定运行1个月无重大bug