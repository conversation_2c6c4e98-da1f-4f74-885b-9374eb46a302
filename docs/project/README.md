# MDM 设备管理系统 - 项目概览

## 📋 项目基本信息

### 项目简介
MDM (Mobile Device Management) 设备管理系统是一个企业级的设备借用和管理平台，支持设备的全生命周期管理，包括设备注册、借用申请、审批流程、归还管理等功能。

### 项目状态
- **当前版本**: v2.3+
- **开发状态**: 活跃开发中
- **部署环境**: dev, prod, prod-lite
- **最后更新**: 2025-07-30

## 🎯 核心功能

### 用户管理
- ✅ 用户注册和登录
- ✅ 角色权限管理 (超级管理员、设备管理员、设备负责人、普通用户)
- ✅ 个人资料管理
- ✅ 账户设置和安全管理

### 设备管理
- ✅ 设备信息录入和管理
- ✅ 设备分类和标签
- ✅ 设备状态跟踪
- ✅ 二维码生成和扫描

### 借用管理
- ✅ 借用申请流程
- ✅ 审批工作流
- ✅ 借用记录管理
- ✅ 归还流程

### 系统管理
- ✅ 统计报表
- ✅ 日志管理
- ✅ 系统监控
- ✅ 数据备份

## 💻 技术架构

### 前端技术栈
- **框架**: Vue.js 3.3.8 + TypeScript 5.2.2
- **UI库**: Element Plus 2.4.2
- **构建工具**: Vite 6.0.0
- **状态管理**: Pinia 2.1.7

### 后端技术栈
- **框架**: Django 4.2.7 + DRF 3.14.0
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **任务队列**: Celery 5.3.4

### 基础设施
- **容器化**: Docker & Docker Compose
- **Web服务器**: Nginx
- **监控**: Prometheus + Grafana

## 🌍 部署环境

| 环境 | 配置文件 | 用途 | 特性 |
|------|----------|------|------|
| **dev** | `docker-compose.dev.yml` | 本地开发 | 热重载、调试工具 |
| **prod** | `docker-compose.prod.yml` | 生产环境 | 性能优化、安全加固 |
| **prod-lite** | `docker-compose.prod-lite.yml` | 预发布环境 | 简化配置、快速部署 |

## 📚 项目文档

### 核心文档
- [需求文档](requirements.md) - 详细功能需求和业务流程
- [变更日志](changelog.md) - 版本更新记录

### 技术文档
- [技术架构](../architecture/technical_architecture.md) - 系统技术架构
- [API设计](../architecture/api-design.md) - RESTful API设计
- [数据库设计](../architecture/database-design.md) - 数据库结构

### 开发文档
- [开发指南](../development/DEV-GUIDE.md) - 开发规范和流程
- [编码规范](../development/coding-standards.md) - 代码规范
- [版本管理](../development/VERSION_MANAGEMENT.md) - 版本控制

### 部署文档
- [部署指南](../deployment/README.md) - 生产环境部署
- [快速开始](../QUICK_START_GUIDE.md) - 快速部署指南

### 用户文档
- [用户手册](../user/user-guide.md) - 系统使用说明
- [个人设置指南](../user/profile_settings_guide.md) - 个人资料管理

## 🔧 开发团队

### 角色分工
- **项目经理**: 项目规划和进度管理
- **后端开发**: Django/Python 开发
- **前端开发**: Vue.js/TypeScript 开发
- **运维工程师**: 部署和监控
- **测试工程师**: 质量保证

### 协作流程
- **版本控制**: Git + GitFlow
- **代码审查**: Pull Request
- **问题跟踪**: [问题管理](../maintenance/ISSUE_MANAGEMENT.md)
- **文档维护**: Markdown + 版本控制

## 📊 项目指标

### 开发进度
- **核心功能**: 100% 完成
- **用户管理**: 100% 完成
- **设备管理**: 100% 完成
- **借用流程**: 100% 完成
- **系统管理**: 90% 完成

### 质量指标
- **代码覆盖率**: 目标 80%+
- **API响应时间**: < 200ms
- **系统可用性**: 99.9%
- **安全等级**: 企业级

## 🚀 未来规划

### 短期目标 (1-3个月)
- [ ] 移动端适配优化
- [ ] 高级报表功能
- [ ] 批量操作功能
- [ ] 性能优化

### 中期目标 (3-6个月)
- [ ] 微服务架构迁移
- [ ] 多租户支持
- [ ] 国际化支持
- [ ] 高可用部署

### 长期目标 (6-12个月)
- [ ] AI智能推荐
- [ ] 物联网设备接入
- [ ] 大数据分析
- [ ] 云原生架构

## 📞 联系方式

- **开发团队**: 内部开发团队
- **技术支持**: 开发团队
- **问题反馈**: [问题管理系统](../maintenance/ISSUE_MANAGEMENT.md)

---

*最后更新: 2025-07-30*
*文档版本: v2.4*
*项目版本: v2.3+*
