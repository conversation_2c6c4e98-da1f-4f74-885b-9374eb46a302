# 📸 文档图片资源目录

> **用途**: 存放MDM系统文档中使用的所有图片资源  
> **更新**: 2025-07-31 | **版本**: v1.0

## 📋 目录结构

```
docs/images/
├── README.md              # 本说明文档
├── architecture/          # 架构图
│   ├── system-overview.png
│   ├── database-schema.png
│   └── api-flow.png
├── screenshots/           # 系统截图
│   ├── login-page.png
│   ├── dashboard.png
│   └── device-management.png
├── diagrams/              # 流程图和示意图
│   ├── deployment-flow.png
│   ├── user-workflow.png
│   └── data-flow.png
└── icons/                 # 图标和小图片
    ├── logo.png
    ├── status-icons/
    └── tech-stack-icons/
```

## 🎯 使用规范

### 图片命名规则
- **小写字母**: 全部使用小写字母
- **连字符分隔**: 使用 `-` 连接单词，如 `user-login-flow.png`
- **描述性命名**: 文件名应清楚描述图片内容
- **版本标识**: 如有版本需求，使用 `v1`, `v2` 后缀

### 支持的图片格式
- **PNG**: 推荐用于截图、图标、透明背景图片
- **JPG/JPEG**: 推荐用于照片、复杂图像
- **SVG**: 推荐用于矢量图、图标、简单图形
- **WebP**: 推荐用于网页优化图片

### 图片规格建议
- **截图**: 1920x1080 或实际屏幕分辨率
- **架构图**: 最小 1200px 宽度，确保清晰度
- **图标**: 64x64, 128x128, 256x256 (提供多种尺寸)
- **文件大小**: 单个文件不超过 2MB

## 📝 在文档中引用图片

### Markdown语法
```markdown
# 相对路径引用
![系统架构图](images/architecture/system-overview.png)

# 带标题的图片
![登录页面](images/screenshots/login-page.png "MDM系统登录页面")

# 指定图片大小
<img src="images/diagrams/deployment-flow.png" alt="部署流程" width="600">
```

### 图片说明规范
- 每个图片都应该有清晰的 `alt` 文本
- 复杂图片应该在文档中提供详细说明
- 重要的架构图应该配有文字解释

## 🔧 图片优化建议

### 压缩工具
- **在线工具**: TinyPNG, Squoosh
- **本地工具**: ImageOptim (Mac), PNGGauntlet (Windows)
- **命令行**: `imagemin`, `pngquant`

### 优化原则
- 保持图片清晰度的同时尽量减小文件大小
- 截图前确保界面整洁，隐藏敏感信息
- 使用统一的截图风格和尺寸

## 📚 常用图片类型

### 系统架构图
- 整体系统架构
- 数据库设计图
- API接口关系图
- 部署架构图

### 用户界面截图
- 登录页面
- 主要功能页面
- 管理后台界面
- 移动端界面

### 流程图
- 用户操作流程
- 数据处理流程
- 部署流程
- 问题解决流程

### 技术图表
- 性能监控图表
- 系统状态图
- 错误统计图
- 使用量统计图

## 🎨 设计规范

### 颜色方案
- 主色调: #409EFF (Element Plus 蓝色)
- 辅助色: #67C23A (成功绿), #E6A23C (警告橙), #F56C6C (错误红)
- 背景色: #F5F7FA (浅灰)

### 字体规范
- 中文: 微软雅黑, PingFang SC
- 英文: Arial, Helvetica
- 代码: Consolas, Monaco, monospace

### 统一风格
- 保持截图的浏览器、操作系统界面一致
- 使用相同的图标风格
- 保持配色方案统一

## 📞 维护说明

### 更新频率
- 系统界面变更时及时更新截图
- 架构调整时更新相关图表
- 定期检查图片链接有效性

### 版本管理
- 重要图片保留历史版本
- 使用Git跟踪图片变更
- 大文件考虑使用Git LFS

### 质量检查
- 定期检查图片清晰度
- 确保图片内容与文档同步
- 移除不再使用的图片文件

---

*最后更新: 2025-07-31*  
*维护者: 文档团队*  
*当前图片数量: 0*
