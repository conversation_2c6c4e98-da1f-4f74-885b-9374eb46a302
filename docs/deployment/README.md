# 部署运维指南

## 📋 文档概览

本目录包含 MDM 设备管理系统的部署和运维相关文档。

## 🚀 部署方案

### 快速部署
- [快速启动指南](../QUICK_START_GUIDE.md) - 5分钟快速部署
- [快速参考指南](../QUICK_REFERENCE_GUIDE.md) - 常用命令参考

### 生产部署
- [生产环境部署指南](PRODUCTION_DEPLOYMENT.md) - 完整的生产部署流程
- [Docker最佳实践](DOCKER_BEST_PRACTICES.md) - 容器化部署方案
- [监控系统](../monitoring/MONITORING_SYSTEM.md) - 系统监控和运维

## 🌍 部署环境

### 开发环境 (dev)
```bash
# 使用开发环境配置
docker compose -f docker-compose.dev.yml up -d
```
- **配置文件**: `docker-compose.dev.yml`
- **用途**: 本地开发和调试
- **特性**: 热重载、调试工具、详细日志

### 生产环境 (prod)
```bash
# 使用生产环境配置
docker compose -f docker-compose.prod.yml up -d
```
- **配置文件**: `docker-compose.prod.yml`
- **用途**: 正式生产环境
- **特性**: 性能优化、安全加固、监控告警

### 精简生产环境 (prod-lite)
```bash
# 使用精简生产环境配置
docker compose -f docker-compose.prod-lite.yml up -d
```
- **配置文件**: `docker-compose.prod-lite.yml`
- **用途**: 预发布环境和快速验收测试
- **特性**: 核心服务、简化配置、快速部署

## 📋 环境要求

### 硬件要求
- **CPU**: 4 cores (推荐 8 cores)
- **内存**: 8GB RAM (推荐 16GB)
- **存储**: 100GB SSD (推荐 500GB)

### 软件要求
- **操作系统**: Ubuntu 22.04+ / CentOS 8+ / RHEL 8+
- **Docker**: >= 20.10.0
- **Docker Compose**: >= 2.0.0

## 🔧 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd mdm
```

2. **选择部署环境**
```bash
# 开发环境
docker compose -f docker-compose.dev.yml up -d

# 生产环境
docker compose -f docker-compose.prod.yml up -d

# 精简生产环境
docker compose -f docker-compose.prod-lite.yml up -d
```

3. **访问系统**
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/api/docs/

## 📚 相关文档

- [技术架构](../architecture/technical_architecture.md) - 系统技术架构
- [安全标准](../security/SECURITY_STANDARDS.md) - 安全规范
- [问题管理](../maintenance/ISSUE_MANAGEMENT.md) - 问题跟踪

---

*最后更新: 2025-07-30*
*文档版本: v2.4*
