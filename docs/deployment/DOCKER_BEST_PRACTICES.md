# 🐳 Docker 最佳实践文档

**版本**: v2.4
**更新时间**: 2025年7月30日
**维护者**: 运维负责人 & 技术负责人
**适用范围**: MDM 设备管理系统 v2.3+

---

## 📋 目录

1. [Docker Compose 现代化配置](#docker-compose-现代化配置)
2. [Dockerfile 优化实践](#dockerfile-优化实践)
3. [安全最佳实践](#安全最佳实践)
4. [性能优化](#性能优化)
5. [监控和日志](#监控和日志)
6. [故障排查](#故障排查)

---

## 🚀 Docker Compose 现代化配置

### 版本规范更新

#### ❌ 过时写法
```yaml
version: '3.8'  # 不再需要version字段

services:
  app:
    image: myapp
```

#### ✅ 现代写法
```yaml
# 现代化 Docker Compose 配置
# 使用最新的 Docker Compose 规范（无需version字段）

services:
  app:
    image: myapp
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

### 命令更新

#### ❌ 过时命令
```bash
docker-compose up -d
docker-compose logs -f
docker-compose down
```

#### ✅ 现代命令
```bash
docker compose up -d
docker compose logs -f
docker compose down
```

### 健康检查配置

```yaml
services:
  backend:
    build: .
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
```

### 环境分离

#### 开发环境配置
```yaml
# docker-compose.dev.yml
services:
  backend:
    build:
      target: development
    environment:
      - DEBUG=True
      - LOG_LEVEL=DEBUG
    volumes:
      - ./backend:/app  # 代码热重载
    stdin_open: true
    tty: true
```

#### 生产环境配置
```yaml
# docker-compose.prod.yml
services:
  backend:
    build:
      target: production
    environment:
      - DEBUG=False
      - LOG_LEVEL=INFO
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

---

## 🏗️ Dockerfile 优化实践

### 多阶段构建

#### ❌ 单阶段构建（冗余）
```dockerfile
FROM python:3.11-slim

# 安装所有依赖（包括构建工具）
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "manage.py", "runserver"]
```

#### ✅ 多阶段构建（优化）
```dockerfile
# 基础镜像
FROM python:3.11-slim as base
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# 开发阶段
FROM base as development
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
USER django
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

# 生产阶段
FROM base as production
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements-prod.txt .
RUN pip install -r requirements-prod.txt

COPY . .
RUN python manage.py collectstatic --noinput
USER django
CMD ["gunicorn", "config.wsgi:application", "--bind", "0.0.0.0:8000"]
```

### 层缓存优化

#### ❌ 缓存效率低
```dockerfile
FROM python:3.11-slim

COPY . .  # 代码变更会使后续层失效
RUN pip install -r requirements.txt
```

#### ✅ 缓存效率高
```dockerfile
FROM python:3.11-slim

# 先复制依赖文件
COPY requirements.txt .
RUN pip install -r requirements.txt  # 依赖不变时可复用缓存

# 再复制代码
COPY . .
```

### 镜像大小优化

```dockerfile
FROM python:3.11-slim

# 合并RUN命令，减少层数
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    && pip install --no-cache-dir -r requirements.txt \
    && apt-get purge -y --auto-remove build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /root/.cache

# 使用.dockerignore排除不需要的文件
# .dockerignore内容:
# .git
# .gitignore
# README.md
# Dockerfile
# .dockerignore
# node_modules
# __pycache__
# *.pyc
```

---

## 🔒 安全最佳实践

### 非root用户

#### ❌ 使用root用户
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
CMD ["python", "manage.py", "runserver"]  # 以root运行
```

#### ✅ 使用非root用户
```dockerfile
FROM python:3.11-slim

# 创建非root用户
RUN groupadd -r django && useradd -r -g django django

WORKDIR /app
COPY . .

# 设置文件权限
RUN chown -R django:django /app

# 切换到非root用户
USER django

CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

### 敏感信息管理

#### ❌ 硬编码敏感信息
```dockerfile
ENV SECRET_KEY=my-secret-key
ENV DATABASE_PASSWORD=password123
```

#### ✅ 使用环境变量和secrets
```yaml
# docker-compose.yml
services:
  backend:
    environment:
      - SECRET_KEY_FILE=/run/secrets/secret_key
      - DATABASE_PASSWORD_FILE=/run/secrets/db_password
    secrets:
      - secret_key
      - db_password

secrets:
  secret_key:
    file: ./secrets/secret_key.txt
  db_password:
    file: ./secrets/db_password.txt
```

### 镜像扫描

```bash
# 使用Docker Scout扫描镜像
docker scout cves mdm-backend:latest

# 使用Trivy扫描
trivy image mdm-backend:latest

# 使用Snyk扫描
snyk container test mdm-backend:latest
```

---

## ⚡ 性能优化

### 资源限制

```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
```

### 网络优化

```yaml
networks:
  mdm_network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: mdm_bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
```

### 存储优化

```yaml
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/mdm/data/postgres
  
  # 使用tmpfs提高性能
  temp_data:
    driver: tmpfs
    driver_opts:
      tmpfs-size: 100M
```

---

## 📊 监控和日志

### 日志配置

```yaml
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=backend,environment=production"
```

### 指标收集

```yaml
services:
  backend:
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=8000"
      - "prometheus.io/path=/metrics"
```

### 健康检查

```dockerfile
# Dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/api/health/ || exit 1
```

---

## 🔧 故障排查

### 常用调试命令

```bash
# 查看容器状态
docker compose ps
docker compose top

# 查看日志
docker compose logs -f backend
docker compose logs --tail=100 backend

# 进入容器调试
docker compose exec backend bash
docker compose exec backend python manage.py shell

# 查看容器资源使用
docker stats
docker compose exec backend top

# 查看网络连接
docker network ls
docker network inspect mdm_network

# 查看卷使用情况
docker volume ls
docker volume inspect mdm_postgres_data
```

### 性能分析

```bash
# 查看容器性能指标
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# 分析镜像层
docker history mdm-backend:latest

# 查看镜像大小
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### 常见问题解决

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker compose logs backend

# 检查健康检查状态
docker compose ps
docker inspect --format='{{.State.Health.Status}}' mdm_backend
```

#### 2. 网络连接问题
```bash
# 测试容器间连接
docker compose exec backend ping db
docker compose exec backend telnet redis 6379

# 检查端口映射
docker compose port backend 8000
```

#### 3. 存储问题
```bash
# 检查卷挂载
docker compose exec backend df -h
docker volume inspect postgres_data

# 清理未使用的资源
docker system prune -a
docker volume prune
```

---

## 📝 最佳实践清单

### 开发阶段
- [ ] 使用多阶段构建
- [ ] 优化Dockerfile层缓存
- [ ] 配置健康检查
- [ ] 使用非root用户
- [ ] 设置资源限制
- [ ] 配置日志轮转

### 生产部署
- [ ] 扫描镜像安全漏洞
- [ ] 使用secrets管理敏感信息
- [ ] 配置监控和告警
- [ ] 设置备份策略
- [ ] 准备回滚方案
- [ ] 文档化部署流程

### 维护阶段
- [ ] 定期更新基础镜像
- [ ] 监控资源使用情况
- [ ] 清理未使用的镜像和卷
- [ ] 备份重要数据
- [ ] 更新安全补丁
- [ ] 优化性能瓶颈

---

## 🔗 相关文档

- [部署指南](README.md)
- [监控系统](../monitoring/MONITORING_SYSTEM.md)
- [安全规范](../security/SECURITY_STANDARDS.md)
- [开发指南](../development/DEV-GUIDE.md)

---

**文档维护**: 本文档由运维负责人负责维护，每月更新一次，Docker版本更新时及时更新。
