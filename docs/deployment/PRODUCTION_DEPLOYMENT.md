# 🚀 生产环境部署指南

**版本**: v1.0.0  
**更新时间**: 2025年7月30日  
**维护者**: 运维团队  
**适用环境**: 生产环境、预发布环境

---

## 📋 目录

1. [部署前准备](#部署前准备)
2. [环境配置](#环境配置)
3. [部署步骤](#部署步骤)
4. [超级管理员配置](#超级管理员配置)
5. [安全配置](#安全配置)
6. [验证测试](#验证测试)
7. [故障排查](#故障排查)

---

## 🛠️ 部署前准备

### 系统要求

#### 硬件要求
- **CPU**: 4 cores (推荐 8 cores)
- **内存**: 8GB RAM (推荐 16GB)
- **存储**: 100GB SSD (推荐 500GB)
- **网络**: 100Mbps (推荐 1Gbps)

#### 软件要求
- **操作系统**: Ubuntu 22.04+ / CentOS 8+ / RHEL 8+
- **Docker**: >= 20.10.0
- **Docker Compose**: >= 2.0.0
- **Git**: >= 2.30.0

### 环境检查

```bash
# 检查Docker版本
docker --version
docker-compose --version

# 检查系统资源
free -h
df -h
lscpu
```

---

## ⚙️ 环境配置

### 1. 克隆项目代码

```bash
# 克隆项目
git clone <repository-url>
cd mdm

# 检查项目结构
ls -la
```

### 2. 配置环境变量

#### 生产环境配置
```bash
# 复制生产环境配置模板
cp .env.production .env

# 编辑生产环境配置（重要！）
vim .env
```

#### 必须修改的配置项
```bash
# 安全密钥（必须修改）
SECRET_KEY=your-super-secret-production-key-here

# 数据库密码（必须修改）
POSTGRES_PASSWORD=your-strong-database-password

# Redis密码（必须修改）
REDIS_PASSWORD=your-strong-redis-password

# 允许的主机（根据实际域名修改）
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# CORS配置（根据前端域名修改）
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

---

## 🚀 部署步骤

### 方法1: 使用部署脚本（推荐）

```bash
# 部署完整生产环境
./scripts/deploy.sh prod

# 部署精简生产环境
./scripts/deploy.sh prod-lite
```

### 方法2: 手动部署

#### 步骤1: 构建镜像
```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build --no-cache
```

#### 步骤2: 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml up -d
```

#### 步骤3: 等待服务就绪
```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看启动日志
docker-compose -f docker-compose.prod.yml logs -f
```

#### 步骤4: 执行数据库迁移
```bash
# 数据库迁移
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate

# Celery Beat迁移 (新增)
docker-compose -f docker-compose.prod.yml exec backend python manage.py migrate django_celery_beat

# 收集静态文件
docker-compose -f docker-compose.prod.yml exec backend python manage.py collectstatic --noinput
```

---

## 👤 超级管理员配置

### ⚠️ 重要说明

**生产环境不会自动创建任何管理员账户**，这是出于安全考虑的设计。部署完成后，**必须手动创建**超级管理员。

### 创建超级管理员

#### 方法1: 交互式创建（推荐）

```bash
# 进入后端容器创建超级管理员
docker-compose -f docker-compose.prod.yml exec backend python manage.py createsuperuser

# 按提示输入以下信息：
# 用户名: [输入安全的用户名，避免使用admin]
# 邮箱地址: [输入公司邮箱]
# 密码: [输入强密码]
# 员工编号: [输入实际员工编号]
```

#### 方法2: 脚本创建

```bash
# 使用脚本创建管理员
docker-compose -f docker-compose.prod.yml exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()

# 创建超级管理员
admin_user = User.objects.create_superuser(
    username='your_admin_username',      # 替换为安全的用户名
    email='<EMAIL>',       # 替换为公司邮箱
    password='your_secure_password',     # 替换为强密码
    employee_id='ADMIN001',              # 替换为实际员工编号
    first_name='系统管理员',
    role='super_admin'
)
print(f'✅ 超级管理员创建成功: {admin_user.username}')
"
```

### 安全配置建议

#### 强密码要求
- **长度**: 至少12个字符
- **复杂性**: 包含大小写字母、数字、特殊字符
- **避免**: 常见密码、个人信息、公司信息

#### 安全用户名建议
- **避免使用**: `admin`, `administrator`, `root`, `superuser`
- **推荐格式**: `公司前缀_admin`, `部门_管理员姓名`
- **示例**: `mdm_admin`, `it_zhangsan`

### 验证管理员账户

```bash
# 检查管理员账户
docker-compose -f docker-compose.prod.yml exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
superusers = User.objects.filter(is_superuser=True)
print(f'超级管理员数量: {superusers.count()}')
for user in superusers:
    print(f'用户名: {user.username}, 邮箱: {user.email}, 激活状态: {user.is_active}')
"
```

---

## 🔒 安全配置

### 1. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 22/tcp    # SSH

# 关闭不必要的端口
sudo ufw deny 5432/tcp   # PostgreSQL (仅内部访问)
sudo ufw deny 6379/tcp   # Redis (仅内部访问)
```

### 2. SSL证书配置

```bash
# 将SSL证书放置到ssl目录
mkdir -p ssl
cp your-domain.crt ssl/
cp your-domain.key ssl/

# 更新Nginx配置以启用HTTPS
# 编辑 nginx/nginx.prod.conf
```

### 3. 定期安全维护

```bash
# 定期更新系统
sudo apt update && sudo apt upgrade -y

# 定期更新Docker镜像
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

---

## ✅ 验证测试

### 1. 服务健康检查

```bash
# 检查所有服务状态
docker-compose -f docker-compose.prod.yml ps

# 检查服务健康状态
curl -f http://localhost/api/health/
```

### 2. 功能验证

#### 前端访问测试
```bash
# 访问前端应用
curl -I http://localhost/

# 检查静态资源
curl -I http://localhost/static/
```

#### 后端API测试
```bash
# 测试API接口
curl -f http://localhost/api/

# 测试API文档
curl -I http://localhost/api/docs/
```

#### 管理后台测试
```bash
# 访问管理后台
curl -I http://localhost/admin/

# 使用创建的管理员账户登录测试
```

### 3. 性能测试

```bash
# 简单压力测试
ab -n 100 -c 10 http://localhost/

# 数据库连接测试
docker-compose -f docker-compose.prod.yml exec backend python manage.py dbshell
```

---

## 🚨 故障排查

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.prod.yml logs backend

# 检查配置文件
docker-compose -f docker-compose.prod.yml config
```

#### 2. 数据库连接失败
```bash
# 检查数据库服务
docker-compose -f docker-compose.prod.yml exec db pg_isready

# 检查数据库连接
docker-compose -f docker-compose.prod.yml exec backend python manage.py dbshell
```

#### 3. 静态文件无法访问
```bash
# 重新收集静态文件
docker-compose -f docker-compose.prod.yml exec backend python manage.py collectstatic --noinput

# 检查Nginx配置
docker-compose -f docker-compose.prod.yml exec nginx nginx -t
```

### 紧急恢复

#### 管理员密码重置
```bash
# 重置管理员密码
docker-compose -f docker-compose.prod.yml exec backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
admin_user = User.objects.get(username='your_admin_username')
admin_user.set_password('new_secure_password')
admin_user.save()
print('✅ 密码重置成功')
"
```

#### 数据库备份恢复
```bash
# 创建数据库备份
docker-compose -f docker-compose.prod.yml exec db pg_dump -U mdm_user mdm_prod > backup.sql

# 恢复数据库备份
docker-compose -f docker-compose.prod.yml exec -T db psql -U mdm_user mdm_prod < backup.sql
```

---

## 📞 技术支持

### 联系方式
- **技术支持**: <EMAIL>
- **运维团队**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

### 相关文档
- [环境配置指南](../../ENVIRONMENT_GUIDE.md)
- [监控系统](../monitoring/MONITORING_SYSTEM.md)
- [安全标准](../security/SECURITY_STANDARDS.md)

---

*最后更新: 2025-07-30*  
*文档版本: v1.0.0*
