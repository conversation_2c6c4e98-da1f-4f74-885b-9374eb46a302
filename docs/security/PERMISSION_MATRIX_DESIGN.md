# 权限管理矩阵设计文档

> **状态**: 🔄 讨论中 | **创建**: 2025-08-01 | **版本**: v1.0-draft

## 📋 目录
- [当前权限体系分析](#当前权限体系分析)
- [问题识别](#问题识别)
- [权限矩阵设计](#权限矩阵设计)
- [实施方案](#实施方案)
- [风险评估](#风险评估)

---

## 🔍 当前权限体系分析

### 1. 现有角色定义
```python
ROLE_CHOICES = [
    ('super_admin', '超级管理员'),      # 系统最高权限
    ('device_admin', '设备管理员'),     # 设备管理权限
    ('device_owner', '设备归属者'),     # 设备拥有权限
    ('normal_user', '普通用户'),        # 基础使用权限
]
```

### 2. 现有权限属性
```python
@property
def is_super_admin(self):
    return self.role == 'super_admin' or self.is_superuser

@property  
def is_device_admin(self):
    return self.role in ['super_admin', 'device_admin'] or self.is_superuser

@property
def is_device_owner(self):
    return self.role in ['super_admin', 'device_admin', 'device_owner'] or self.is_superuser
```

### 3. 现有权限类
- `IsAdminUser`: 只允许管理员访问
- `IsOwnerOrAdmin`: 允许拥有者或管理员访问
- `IsDeviceOwnerOrAdmin`: 允许设备拥有者或管理员访问
- `CanManageDevice`: 可以管理设备的权限
- `CanBorrowDevice`: 可以借用设备的权限

---

## ⚠️ 问题识别

### 1. 权限继承混乱
**问题**: 当前权限设计中，高级角色自动继承低级角色权限，导致权限边界不清晰。

**示例**:
```python
# 设备归属者权限包含了所有低级角色
def is_device_owner(self):
    return self.role in ['super_admin', 'device_admin', 'device_owner']
```

### 2. 权限检查不一致
**问题**: 不同模块使用不同的权限检查方式，缺乏统一标准。

**示例**:
- 用户管理: 使用 `IsAdminUser`
- 设备管理: 使用 `CanManageDevice` + 对象级权限
- 借用管理: 混合使用角色检查和对象权限

### 3. 缺乏细粒度权限
**问题**: 权限粒度过粗，无法满足复杂业务场景。

**示例**:
- 设备管理员可以管理所有设备，但可能只需要管理特定类别
- 普通用户无法区分只读和操作权限

### 4. 权限扩展性差
**问题**: 当前设计难以支持新的角色和权限需求。

---

## 🎯 权限矩阵设计

### 1. 核心权限模块

#### A. 用户管理权限
| 权限代码 | 权限名称 | 描述 |
|---------|---------|------|
| `user.view` | 查看用户 | 查看用户基本信息 |
| `user.view_detail` | 查看用户详情 | 查看用户详细信息 |
| `user.create` | 创建用户 | 创建新用户账户 |
| `user.update` | 更新用户 | 修改用户信息 |
| `user.delete` | 删除用户 | 禁用/删除用户 |
| `user.manage_role` | 管理角色 | 分配/撤销用户角色 |
| `user.reset_password` | 重置密码 | 重置用户密码 |

#### B. 设备管理权限
| 权限代码 | 权限名称 | 描述 |
|---------|---------|------|
| `device.view` | 查看设备 | 查看设备基本信息 |
| `device.view_detail` | 查看设备详情 | 查看设备详细信息 |
| `device.create` | 创建设备 | 添加新设备 |
| `device.update` | 更新设备 | 修改设备信息 |
| `device.delete` | 删除设备 | 删除设备记录 |
| `device.assign_owner` | 分配归属者 | 设置设备归属者 |
| `device.change_status` | 变更状态 | 修改设备状态 |
| `device.view_logs` | 查看日志 | 查看设备操作日志 |

#### C. 借用管理权限
| 权限代码 | 权限名称 | 描述 |
|---------|---------|------|
| `loan.view` | 查看借用 | 查看借用申请 |
| `loan.create` | 创建借用 | 提交借用申请 |
| `loan.approve` | 审批借用 | 审批借用申请 |
| `loan.reject` | 拒绝借用 | 拒绝借用申请 |
| `loan.extend` | 延期借用 | 申请/审批延期 |
| `loan.return` | 归还设备 | 处理设备归还 |
| `loan.view_history` | 查看历史 | 查看借用历史 |

#### D. 系统管理权限
| 权限代码 | 权限名称 | 描述 |
|---------|---------|------|
| `system.view_logs` | 查看系统日志 | 查看系统操作日志 |
| `system.backup` | 系统备份 | 执行系统备份 |
| `system.config` | 系统配置 | 修改系统配置 |
| `system.monitor` | 系统监控 | 查看系统监控信息 |

### 2. 角色权限矩阵

| 角色 | 用户管理 | 设备管理 | 借用管理 | 系统管理 | 备注 |
|------|---------|---------|---------|---------|------|
| **超级管理员** | 全部权限 | 全部权限 | 全部权限 | 全部权限 | 系统最高权限 |
| **设备管理员** | view, view_detail | 全部权限 | approve, reject, view_history | view_logs | 设备全权管理 |
| **设备归属者** | view | view, view_detail, update*, change_status*, view_logs* | approve*, reject*, view* | - | 仅限自己的设备 |
| **普通用户** | view(自己) | view, view_detail | create, extend, view(自己) | - | 基础使用权限 |

**注**: `*` 表示仅限于自己拥有/相关的资源

### 3. 对象级权限规则

#### 设备权限规则
```python
class DevicePermissionRules:
    def can_view(self, user, device):
        # 所有认证用户可以查看基本信息
        return user.is_authenticated
    
    def can_update(self, user, device):
        # 设备归属者或管理员可以更新
        return (user.has_perm('device.update') and 
                (device.owner == user or user.is_device_admin))
    
    def can_delete(self, user, device):
        # 只有管理员可以删除
        return user.has_perm('device.delete') and user.is_device_admin
```

#### 借用权限规则
```python
class LoanPermissionRules:
    def can_approve(self, user, loan):
        # 设备归属者或管理员可以审批
        return (user.has_perm('loan.approve') and 
                (loan.device.owner == user or user.is_device_admin))
    
    def can_view(self, user, loan):
        # 申请人、设备归属者或管理员可以查看
        return (loan.borrower == user or 
                loan.device.owner == user or 
                user.is_device_admin)
```

---

## 🚀 实施方案

### 阶段1: 权限模型重构 (1-2天)
1. **创建权限模型**
   - 定义 `Permission` 模型
   - 定义 `Role` 模型  
   - 定义 `UserRole` 关联模型

2. **数据迁移**
   - 创建基础权限数据
   - 创建角色数据
   - 迁移现有用户角色

### 阶段2: 权限检查重构 (2-3天)
1. **统一权限检查**
   - 重构现有权限类
   - 实现对象级权限检查
   - 更新视图权限配置

2. **权限服务**
   - 实现 `PermissionService`
   - 提供统一的权限检查接口
   - 支持权限缓存

### 阶段3: 前端权限适配 (1-2天)
1. **权限状态管理**
   - 更新用户权限获取逻辑
   - 实现权限状态缓存
   - 更新权限检查方法

2. **UI权限控制**
   - 更新按钮/菜单权限控制
   - 实现页面级权限控制

---

## ⚠️ 风险评估

### 高风险项
1. **数据迁移风险** 🔴
   - **风险**: 现有用户角色数据丢失
   - **缓解**: 完整备份 + 分步迁移 + 回滚方案

2. **权限检查遗漏** 🔴  
   - **风险**: 某些接口权限检查缺失
   - **缓解**: 全面审计 + 自动化测试 + 权限矩阵验证

### 中风险项
3. **性能影响** 🟡
   - **风险**: 权限检查增加响应时间
   - **缓解**: 权限缓存 + 数据库优化 + 性能测试

4. **兼容性问题** 🟡
   - **风险**: 前端权限逻辑不兼容
   - **缓解**: 渐进式迁移 + 兼容层 + 充分测试

### 低风险项  
5. **学习成本** 🟢
   - **风险**: 开发团队需要适应新权限体系
   - **缓解**: 文档完善 + 培训 + 示例代码

---

## 📝 讨论要点

### 1. 角色设计是否合理？
- 是否需要增加新角色（如部门管理员）？
- 角色权限分配是否符合业务需求？

### 2. 权限粒度是否合适？
- 当前权限划分是否过细或过粗？
- 是否需要支持自定义权限组合？

### 3. 实施优先级如何安排？
- 哪些模块需要优先重构？
- 是否可以分模块逐步实施？

### 4. 向后兼容性要求？
- 是否需要保持API兼容性？
- 前端改动范围是否可接受？

---

## 💻 技术实现方案

### 1. 数据库模型设计

```python
# 权限模型
class Permission(models.Model):
    codename = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=255)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    description = models.TextField(blank=True)

    class Meta:
        db_table = 'auth_permissions'

# 角色模型
class Role(models.Model):
    name = models.CharField(max_length=80, unique=True)
    description = models.TextField(blank=True)
    permissions = models.ManyToManyField(Permission, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'auth_roles'

# 用户角色关联
class UserRole(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='granted_roles')
    granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'auth_user_roles'
        unique_together = ['user', 'role']

# 对象级权限
class ObjectPermission(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        db_table = 'auth_object_permissions'
        unique_together = ['user', 'permission', 'content_type', 'object_id']
```

### 2. 权限服务实现

```python
class PermissionService:
    """统一权限管理服务"""

    def __init__(self):
        self.cache = {}

    def has_permission(self, user, permission_code, obj=None):
        """检查用户是否有指定权限"""
        if user.is_superuser:
            return True

        # 检查角色权限
        if self.has_role_permission(user, permission_code):
            # 如果有对象，检查对象级权限
            if obj:
                return self.has_object_permission(user, permission_code, obj)
            return True

        # 检查直接分配的对象权限
        if obj:
            return self.has_direct_object_permission(user, permission_code, obj)

        return False

    def get_user_permissions(self, user):
        """获取用户所有权限"""
        cache_key = f"user_permissions_{user.id}"
        if cache_key in self.cache:
            return self.cache[cache_key]

        permissions = set()

        # 获取角色权限
        user_roles = UserRole.objects.filter(
            user=user,
            role__is_active=True
        ).select_related('role').prefetch_related('role__permissions')

        for user_role in user_roles:
            if self.is_role_valid(user_role):
                permissions.update(user_role.role.permissions.all())

        self.cache[cache_key] = permissions
        return permissions

    def assign_role(self, user, role_name, granted_by=None):
        """分配角色给用户"""
        try:
            role = Role.objects.get(name=role_name, is_active=True)
            user_role, created = UserRole.objects.get_or_create(
                user=user,
                role=role,
                defaults={'granted_by': granted_by}
            )

            # 清除缓存
            self.clear_user_cache(user)

            return user_role, created
        except Role.DoesNotExist:
            raise ValueError(f"Role '{role_name}' does not exist")
```

### 3. 权限装饰器和中间件

```python
# 权限装饰器
def require_permission(permission_code, obj_param=None):
    """权限检查装饰器"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            obj = None
            if obj_param:
                # 从视图参数中获取对象
                obj = get_object_from_params(obj_param, args, kwargs)

            if not permission_service.has_permission(request.user, permission_code, obj):
                raise PermissionDenied(f"Permission '{permission_code}' required")

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

# DRF权限类
class DynamicPermission(BasePermission):
    """动态权限检查类"""

    permission_map = {
        'GET': 'view',
        'POST': 'create',
        'PUT': 'update',
        'PATCH': 'update',
        'DELETE': 'delete'
    }

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        permission_code = self.get_permission_code(request, view)
        return permission_service.has_permission(request.user, permission_code)

    def has_object_permission(self, request, view, obj):
        permission_code = self.get_permission_code(request, view)
        return permission_service.has_permission(request.user, permission_code, obj)

    def get_permission_code(self, request, view):
        """获取权限代码"""
        action = self.permission_map.get(request.method, 'view')
        model_name = getattr(view, 'permission_model', None)
        if not model_name:
            model_name = view.queryset.model._meta.model_name
        return f"{model_name}.{action}"
```

---

## 📊 迁移计划详细步骤

### Step 1: 数据库迁移 (预计2小时)

```bash
# 1. 创建新权限表
python manage.py makemigrations --name create_permission_models

# 2. 执行迁移
python manage.py migrate

# 3. 初始化权限数据
python manage.py loaddata initial_permissions.json

# 4. 迁移现有用户角色
python manage.py migrate_user_roles
```

### Step 2: 代码重构 (预计1天)

1. **更新User模型**
   ```python
   # 移除硬编码的权限属性
   # 添加权限检查方法
   def has_perm(self, permission_code, obj=None):
       return permission_service.has_permission(self, permission_code, obj)
   ```

2. **重构权限类**
   ```python
   # 统一使用DynamicPermission
   # 移除旧的权限类
   # 更新视图权限配置
   ```

3. **更新视图**
   ```python
   # 使用统一的权限检查
   # 移除硬编码的角色检查
   # 添加权限模型配置
   ```

### Step 3: 测试验证 (预计4小时)

```python
# 权限测试用例
class PermissionTestCase(TestCase):
    def test_user_permissions(self):
        # 测试用户权限获取
        # 测试角色权限继承
        # 测试对象级权限

    def test_permission_inheritance(self):
        # 测试权限继承逻辑

    def test_object_permissions(self):
        # 测试对象级权限检查
```

---

## 🔄 回滚方案

### 紧急回滚步骤
1. **代码回滚**: 使用Git回滚到迁移前版本
2. **数据库回滚**: 执行反向迁移脚本
3. **缓存清理**: 清除所有权限相关缓存
4. **服务重启**: 重启应用服务

### 数据保护措施
- 迁移前完整数据库备份
- 分步迁移，每步验证
- 保留原有权限检查逻辑作为备用

---

**下一步**: 请您审查以上设计，特别关注：
1. 权限矩阵是否符合业务需求？
2. 技术实现方案是否可行？
3. 迁移风险是否可接受？
4. 是否需要调整角色和权限定义？

确认后我们将开始实施。

---

## 📈 权限体系对比分析

### 当前体系 vs 新体系对比

| 维度 | 当前体系 | 新体系 | 改进效果 |
|------|---------|--------|----------|
| **权限模型** | 硬编码角色权限 | 基于RBAC的动态权限 | ✅ 灵活性大幅提升 |
| **权限检查** | 分散在各个视图中 | 统一的权限服务 | ✅ 一致性和可维护性 |
| **权限粒度** | 粗粒度角色权限 | 细粒度功能权限 | ✅ 精确控制 |
| **对象权限** | 部分支持，不统一 | 完整的对象级权限 | ✅ 支持复杂业务场景 |
| **扩展性** | 难以扩展新角色 | 易于添加角色和权限 | ✅ 支持业务发展 |
| **性能** | 多次数据库查询 | 权限缓存机制 | ✅ 性能优化 |

### 具体场景对比

#### 场景1: 设备归属者管理设备
**当前实现**:
```python
# 分散在多个地方的权限检查
if not request.user.is_device_admin:
    if request.user.role == 'device_owner':
        if device.owner != request.user:
            return False
```

**新实现**:
```python
# 统一的权限检查
@require_permission('device.update')
def update_device(request, device_id):
    # 权限检查自动处理，包括对象级权限
    pass
```

#### 场景2: 借用申请审批
**当前实现**:
```python
# 复杂的权限逻辑
if not self.request.user.is_device_admin:
    if self.request.user.role == 'device_owner':
        if obj.device.owner != self.request.user and obj.borrower != self.request.user:
            self.permission_denied(self.request)
```

**新实现**:
```python
# 清晰的权限规则
class LoanPermissionRules:
    def can_approve(self, user, loan):
        return (user.has_perm('loan.approve') and
                (loan.device.owner == user or user.is_device_admin))
```

### 权限配置示例

#### 新体系权限配置
```json
{
  "roles": {
    "super_admin": {
      "permissions": ["*"]
    },
    "device_admin": {
      "permissions": [
        "user.view", "user.view_detail",
        "device.*",
        "loan.approve", "loan.reject", "loan.view_history",
        "system.view_logs"
      ]
    },
    "device_owner": {
      "permissions": [
        "user.view",
        "device.view", "device.view_detail", "device.update", "device.change_status", "device.view_logs",
        "loan.approve", "loan.reject", "loan.view"
      ],
      "object_restrictions": {
        "device.*": "owner_only",
        "loan.*": "device_owner_or_borrower"
      }
    },
    "normal_user": {
      "permissions": [
        "user.view",
        "device.view", "device.view_detail",
        "loan.create", "loan.extend", "loan.view"
      ],
      "object_restrictions": {
        "user.*": "self_only",
        "loan.*": "borrower_only"
      }
    }
  }
}
```

---

## 🎯 实施建议

### 推荐实施策略: 渐进式迁移

#### 阶段1: 基础设施 (优先级: 🔴 高)
- [ ] 创建权限模型和数据库表
- [ ] 实现权限服务核心功能
- [ ] 创建权限初始化脚本
- [ ] 建立测试框架

#### 阶段2: 核心模块迁移 (优先级: 🟡 中)
- [ ] 用户管理模块权限重构
- [ ] 设备管理模块权限重构
- [ ] 统一权限检查中间件

#### 阶段3: 业务模块迁移 (优先级: 🟢 低)
- [ ] 借用管理模块权限重构
- [ ] 前端权限状态管理更新
- [ ] 权限缓存优化

#### 阶段4: 优化完善 (优先级: 🟢 低)
- [ ] 性能优化和监控
- [ ] 权限审计功能
- [ ] 文档和培训

### 关键决策点

1. **是否保持向后兼容？**
   - 建议: 保持API兼容，内部实现重构
   - 影响: 前端无需大幅修改

2. **权限缓存策略？**
   - 建议: Redis缓存 + 本地缓存
   - 影响: 提升性能，需要缓存失效机制

3. **迁移时间窗口？**
   - 建议: 分模块迁移，每个模块1-2天
   - 影响: 降低风险，便于回滚

---

## 🔍 关键业务逻辑澄清

### 问题：normal_user 与设备归属权的关系

**当前系统设计分析**：

1. **设备归属者限制**：
   ```python
   # 在 DeviceAssignOwnerSerializer 中
   def validate_owner_id(self, value):
       user = User.objects.get(id=value)
       if not user.is_device_owner:  # 检查是否具备设备归属者权限
           raise serializers.ValidationError("指定用户不具备设备归属者权限")
   ```

2. **设备归属者选项过滤**：
   ```python
   # 在 DeviceOwnerOptionsView 中
   def get_queryset(self):
       return User.objects.filter(
           role__in=['super_admin', 'device_admin', 'device_owner'],  # 不包含 normal_user
           is_active=True
       )
   ```

3. **权限检查逻辑**：
   ```python
   @property
   def is_device_owner(self):
       return self.role in ['super_admin', 'device_admin', 'device_owner']  # normal_user 返回 False
   ```

**结论**：**当前系统设计中，`normal_user` 不能被分配为设备归属者，只能借用设备。**

### 业务场景分析

#### 场景A：严格角色分离（当前实现）
```
normal_user（普通用户）
├── 只能借用设备
├── 不能被分配为设备归属者
├── 不能管理任何设备
└── 不能审批借用申请

device_owner（设备归属者）
├── 可以被分配为设备归属者
├── 可以管理自己的设备
├── 可以审批自己设备的借用申请
└── 可以借用其他设备
```

#### 场景B：动态权限分配（可能的需求）
```
normal_user 被分配设备后
├── 自动获得该设备的管理权限？
├── 可以审批该设备的借用申请？
├── 角色是否需要自动升级为 device_owner？
└── 还是保持 normal_user 但获得特定设备权限？
```

### 三种可能的设计方案

#### 方案1：维持当前设计（推荐）
- **规则**：只有 `device_owner` 及以上角色可以被分配为设备归属者
- **优点**：角色权限清晰，易于管理
- **缺点**：需要先将用户角色升级为 `device_owner` 才能分配设备

#### 方案2：允许 normal_user 被分配设备，自动升级角色
- **规则**：分配设备时自动将 `normal_user` 升级为 `device_owner`
- **优点**：操作简便，一步到位
- **缺点**：角色变更可能带来意外权限

#### 方案3：基于对象的权限分配（新权限体系）
- **规则**：`normal_user` 可以被分配设备，但只获得该特定设备的管理权限
- **优点**：最灵活，精确控制
- **缺点**：实现复杂度高

## ❓ 待确认问题

### 🔴 核心业务逻辑问题
1. **设备归属权分配策略**：
   - 是否允许 `normal_user` 被分配为设备归属者？
   - 如果允许，是否需要自动升级用户角色？
   - 还是采用对象级权限，让 `normal_user` 只管理特定设备？

2. **权限继承逻辑**：
   - `normal_user` 被分配设备后，是否应该获得设备管理权限？
   - 是否应该能够审批该设备的借用申请？
   - 权限范围是仅限该设备，还是获得完整的 `device_owner` 权限？

### 🟡 技术实现问题
3. **业务角色需求**：
   - 是否需要增加"部门管理员"角色？
   - 是否需要支持临时权限（如代理审批）？

4. **权限粒度**：
   - 当前权限划分是否满足需求？
   - 是否需要更细粒度的权限（如只读设备信息）？

### 🟢 实施计划问题
5. **实施时间**：
   - 期望的完成时间？
   - 是否可以接受分阶段实施？

6. **风险承受度**：
   - 对于数据迁移风险的接受程度？
   - 是否需要更保守的迁移策略？

---

**请您重点关注以下几点并给出反馈：**
1. 权限矩阵设计是否符合实际业务需求？
2. 技术实现方案是否可行？
3. 迁移风险和时间安排是否可接受？
4. 是否有其他特殊的权限需求？
