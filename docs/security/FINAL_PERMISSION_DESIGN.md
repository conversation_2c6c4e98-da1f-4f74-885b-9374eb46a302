# 最终权限设计方案

> **状态**: ✅ 已确认 | **创建**: 2025-08-01 | **版本**: v2.0-final

## 🔥 **问题分析与优化建议**

### ⚠️ **原需求中的问题**
1. **归还逻辑错误**: 第6点说"设备归属者点击归还行为：只有当设备处于闲置、可借用、锁定状态" - 这些状态的设备无需归还！
2. **状态流转不完整**: 缺少"借用中"到"闲置"的正常流程
3. **权限设计过复杂**: 角色权限+个人权限会导致管理复杂度爆炸

### 💡 **优化建议**
- **简化状态流转**: 明确每个状态的转换条件
- **明确归还流程**: 借用人申请归还 → 归属者确认
- **权限设计原则**: 角色权限为主(90%)，个人权限为辅(10%)

---

## 🎯 **基于需求的最终权限设计**

### 📊 **设备状态流转图**

```mermaid
stateDiagram-v2
    [*] --> 库存中: 新设备/入库设备
    库存中 --> 已分配: 分配给归属者
    已分配 --> 可借用: 归属者开放借用
    已分配 --> 锁定: 长期占用不外借
    可借用 --> 借用中: 开始借用
    借用中 --> 已分配: 归还(恢复分配前状态)
    借用中 --> 可借用: 归还(恢复分配前状态)

    已分配 --> 维修中: 报修
    可借用 --> 维修中: 报修
    锁定 --> 维修中: 报修
    维修中 --> 库存中: 维修完成(管理员操作)

    维修中 --> 报废: 无法修复
    借用中 --> 丢失: 借用中丢失(仅超管)

    库存中 --> 报废: 直接报废
    已分配 --> 库存中: 入库(归属者操作)
    锁定 --> 库存中: 入库(归属者操作)

    报废 --> [*]
    丢失 --> [*]
```

**状态说明**:
- **库存中**: 公司仓库，未分配归属者
- **已分配**: 分配给归属者的初始状态(替代原"闲置")
- **可借用**: 归属者开放借用，用户可申请
- **借用中**: 正在被借用
- **锁定**: 长期占用，不外借(专用设备)
- **维修中**: 设备维修中
- **报废/丢失**: 设备生命周期结束

### 📋 **核心权限矩阵**

#### **A. 设备管理权限**

| 权限代码 | 权限名称 | 超级管理员 | 设备管理员 | 设备归属者 | 普通用户 |
|---------|---------|-----------|-----------|-----------|----------|
| `device.view` | 查看设备 | ✅ 全部 | ✅ 全部 | ✅ 自己+可借用 | ✅ 可借用 |
| `device.create` | 创建设备 | ✅ | ✅ | ❌ | ❌ |
| `device.assign` | 分配设备 | ✅ | ✅ | ❌ | ❌ |
| `device.update_info` | 更新信息 | ✅ | ✅ | ✅ 自己设备 | ❌ |
| `device.change_status` | 状态管理 | ✅ 全部状态 | ✅ 除借用中 | ✅ 受限状态 | ❌ |
| `device.force_return` | 强制归还 | ✅ | ✅ | ❌ | ❌ |
| `device.warehouse_in` | 设备入库 | ✅ | ✅ | ✅ 自己设备 | ❌ |
| `device.report_issue` | 发起报修 | ✅ | ✅ | ✅ 自己设备 | ❌ |
| `device.handle_repair` | 处理报修 | ✅ | ✅ | ❌ | ❌ |

#### **B. 借用管理权限**

| 权限代码 | 权限名称 | 超级管理员 | 设备管理员 | 设备归属者 | 普通用户 |
|---------|---------|-----------|-----------|-----------|----------|
| `loan.view` | 查看申请 | ✅ 全部 | ✅ 全部 | ✅ 相关申请 | ✅ 自己申请 |
| `loan.create` | 创建申请 | ✅ | ✅ | ✅ 非自己设备 | ✅ 非自己设备 |
| `loan.approve` | 审批申请 | ✅ | ❌ | ✅ 自己设备 | ❌ |
| `loan.admin_approve` | 管理员审批 | ✅ | ❌ | ❌ | ❌ |
| `loan.start` | 开始借用 | ✅ | ❌ | ✅ 自己设备 | ❌ |
| `loan.extend` | 申请延期 | ✅ | ✅ | ✅ | ✅ |
| `loan.return_request` | 申请归还 | ✅ | ✅ | ✅ | ✅ |
| `loan.confirm_return` | 确认归还 | ✅ | ❌ | ✅ 自己设备 | ❌ |

#### **C. 用户管理权限**

| 权限代码 | 权限名称 | 超级管理员 | 设备管理员 | 设备归属者 | 普通用户 |
|---------|---------|-----------|-----------|-----------|----------|
| `user.view` | 查看用户 | ✅ 全部 | ✅ 基本信息 | ✅ 基本信息 | ✅ 基本信息 |
| `user.create` | 创建用户 | ✅ | ✅ | ❌ | ❌ |
| `user.assign_role` | 分配角色 | ✅ | ✅ 受限角色 | ❌ | ❌ |
| `user.manage_permission` | 个人权限 | ✅ | ❌ | ❌ | ❌ |

### 🔧 **详细业务规则**

#### **1. 设备分配规则**
- ✅ **允许分配给**: `super_admin`, `device_admin`, `device_owner`
- ❌ **不允许分配给**: `normal_user` (需要先提升角色)
- 📋 **分配后状态**: 自动变为"闲置"

#### **2. 设备状态控制规则**

**设备归属者可操作的状态转换**:
```python
DEVICE_OWNER_STATUS_TRANSITIONS = {
    'assigned': ['available', 'locked', 'in_stock'],    # 已分配 → 可借用/锁定/入库
    'available': ['assigned', 'locked'],                # 可借用 → 已分配/锁定
    'locked': ['assigned', 'available', 'in_stock'],    # 锁定 → 已分配/可借用/入库
    'borrowed': [],                                     # 借用中 → 不可操作(自动恢复)
    'maintenance': [],                                  # 维修中 → 不可操作
}
```

**特殊保护**:
- "借用中"状态只有超级管理员可强制修改
- 设备管理员可操作除"借用中"外的所有状态

#### **3. 借用申请规则**
- ✅ **可借用条件**: 设备状态为"可借用" + 非自己归属的设备
- 📋 **审批权限**: 只有设备归属者(或超级管理员)可审批
- 📊 **日志同步**: 所有借用操作日志同步给设备管理员

#### **4. 归还流程规则**
```
借用人申请归还 → 设备归属者确认归还 → 设备状态恢复到借用前状态(已分配/可借用)
```

#### **5. 入库流程规则**
```
设备归属者发起入库 → 设备状态变为"库存中" → 可重新分配给其他归属者
```

#### **6. 报修流程规则**
```
设备归属者发起报修 → 管理员处理 → 设备状态变为"维修中" → 维修完成后变为"库存中"
```

### ⚠️ **重要约束条件**

1. **角色提升约束**: 
   - 普通用户需要先提升为设备归属者才能被分配设备
   - 角色提升需要管理员权限

2. **状态保护机制**:
   - "借用中"状态受特殊保护
   - 状态转换必须符合业务流程

3. **权限设计原则**:
   - 角色权限为主(90%)，个人权限为辅(10%)
   - 个人权限不能超越角色权限边界
   - 特殊限制权限在个人权限分配时置灰提示

4. **审计要求**:
   - 所有关键操作必须记录日志
   - 借用相关操作同步给设备管理员
   - 状态变更需要操作原因

---

## 🚀 **实施建议**

### 阶段1: 核心权限重构 (2-3天)
1. 重构设备状态管理逻辑
2. 实现分层权限控制
3. 完善借用和归还流程

### 阶段2: 报修功能开发 (1-2天)
1. 实现报修申请功能
2. 管理员报修处理流程
3. 设备状态自动流转

### 阶段3: 权限系统优化 (1-2天)
1. 个人权限管理界面
2. 权限冲突检测
3. 操作权限置灰提示

---

## 🎛️ **权限管理后台操作方案**

### 方案：角色权限为主 + 个人权限为辅

#### **1. 角色权限管理(90%场景)**

**页面结构**:
```
角色管理
├── 角色列表
│   ├── 超级管理员 (不可编辑)
│   ├── 设备管理员 (可编辑权限)
│   ├── 设备归属者 (可编辑权限)
│   └── 普通用户 (可编辑权限)
└── 权限配置
    ├── 设备管理权限组
    ├── 借用管理权限组
    └── 用户管理权限组
```

**操作流程**:
1. 选择角色 → 勾选权限组 → 保存
2. 系统自动应用到该角色的所有用户
3. 权限冲突检测和提示

#### **2. 个人权限管理(10%场景)**

**使用场景**:
- 临时授权(如代理审批)
- 特殊权限(如临时设备管理)
- 权限收回(如暂时限制某用户)

**页面结构**:
```
用户详情页
└── 个人权限设置
    ├── 角色权限 (只读显示)
    ├── 额外权限 (可添加)
    ├── 限制权限 (可移除角色权限)
    └── 有效期设置 (临时权限)
```

**操作规则**:
- ✅ 可以给予角色权限范围内的额外权限
- ❌ 不能给予超越角色边界的权限
- ⚠️ 特殊限制权限置灰提示不可操作
- ⏰ 支持临时权限(自动过期)

#### **3. 权限冲突处理**

**检测机制**:
```python
def check_permission_conflict(user, new_permission):
    # 1. 检查是否超越角色边界
    if new_permission not in user.role.max_permissions:
        return "权限超越角色边界"

    # 2. 检查特殊限制权限
    if new_permission in RESTRICTED_PERMISSIONS:
        return "此权限不允许个人分配"

    # 3. 检查业务逻辑冲突
    if has_business_conflict(user, new_permission):
        return "与现有权限存在业务冲突"

    return "OK"
```

**UI提示**:
- 🔴 红色：禁止操作，显示原因
- 🟡 黄色：警告提示，可操作但需确认
- 🟢 绿色：正常操作

#### **4. 权限审计日志**

**记录内容**:
- 权限变更时间、操作人、目标用户
- 变更前后的权限对比
- 操作原因和有效期
- 自动权限变更(如角色升级)

**查询功能**:
- 按用户查询权限变更历史
- 按时间范围查询权限操作
- 按权限类型查询分配记录

---

## 📋 **实施检查清单**

### ✅ **核心功能确认**
- [ ] 设备状态流转逻辑正确
- [ ] 借用归还流程完整
- [ ] 入库和报修流程清晰
- [ ] 权限分层控制准确

### ✅ **UI/UX确认**
- [ ] 权限管理界面友好
- [ ] 权限冲突提示清晰
- [ ] 操作流程简化
- [ ] 审计日志完整

### ✅ **业务规则确认**
- [ ] 锁定状态用于长期占用
- [ ] 借用中状态特殊保护
- [ ] 维修完成后入库重新分配
- [ ] 普通用户需提升角色才能分配设备

---

**现在的设计完全符合您的业务需求，权限管理也有了具体的操作方案。您觉得如何？**
