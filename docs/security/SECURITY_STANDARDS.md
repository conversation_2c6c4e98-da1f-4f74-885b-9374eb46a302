# 🔒 安全规范文档

**版本**: v1.0.0
**更新时间**: 2025年7月29日
**维护者**: 安全负责人 & 技术负责人
**适用范围**: 设备管理平台及所有企业级项目

---

## 📋 目录

1. [安全策略概述](#安全策略概述)
2. [身份认证与授权](#身份认证与授权)
3. [数据安全](#数据安全)
4. [网络安全](#网络安全)
5. [应用安全](#应用安全)
6. [安全监控与响应](#安全监控与响应)

---

## 🎯 安全策略概述

### 安全目标

#### 1. 机密性 (Confidentiality)
- **数据保护**: 确保敏感数据不被未授权访问
- **访问控制**: 实施最小权限原则
- **数据分类**: 按敏感程度分类管理数据
- **加密保护**: 对敏感数据进行加密存储和传输

#### 2. 完整性 (Integrity)
- **数据完整性**: 防止数据被篡改或损坏
- **系统完整性**: 确保系统配置和代码不被恶意修改
- **审计日志**: 记录所有关键操作的审计轨迹
- **数字签名**: 对关键数据进行数字签名验证

#### 3. 可用性 (Availability)
- **服务可用性**: 确保系统7×24小时稳定运行
- **灾难恢复**: 建立完善的备份和恢复机制
- **DDoS防护**: 防范分布式拒绝服务攻击
- **容量规划**: 确保系统有足够的处理能力

### 安全原则

#### 1. 纵深防御
```
┌─────────────────────────────────────┐
│           用户层防护                 │
├─────────────────────────────────────┤
│           应用层防护                 │
├─────────────────────────────────────┤
│           网络层防护                 │
├─────────────────────────────────────┤
│           主机层防护                 │
├─────────────────────────────────────┤
│           数据层防护                 │
└─────────────────────────────────────┘
```

#### 2. 最小权限原则
- 用户只能访问完成工作所需的最小资源
- 定期审查和调整权限设置
- 实施角色基础的访问控制(RBAC)

#### 3. 零信任架构
- 不信任网络内部的任何实体
- 对所有访问请求进行验证和授权
- 持续监控和验证用户行为

#### 4. 安全左移
- 在开发阶段就考虑安全问题
- 实施安全编码规范
- 进行安全代码审查和测试

---

## 🔐 身份认证与授权

### 身份认证

#### 1. 多因素认证 (MFA)
```python
# backend/authentication/mfa.py
from django.contrib.auth import get_user_model
from django.core.cache import cache
import pyotp
import qrcode
from io import BytesIO
import base64

User = get_user_model()

class MFAService:
    """多因素认证服务"""

    def generate_secret_key(self, user: User) -> str:
        """生成密钥"""
        secret = pyotp.random_base32()
        cache.set(f'mfa_secret_{user.id}', secret, timeout=300)  # 5分钟有效
        return secret

    def generate_qr_code(self, user: User, secret: str) -> str:
        """生成二维码"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user.email,
            issuer_name="MDM Platform"
        )

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)

        return base64.b64encode(buffer.getvalue()).decode()

    def verify_totp(self, user: User, token: str) -> bool:
        """验证TOTP令牌"""
        secret = user.profile.mfa_secret
        if not secret:
            return False

        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)

    def enable_mfa(self, user: User, token: str) -> bool:
        """启用MFA"""
        secret = cache.get(f'mfa_secret_{user.id}')
        if not secret:
            return False

        if self.verify_totp_with_secret(secret, token):
            user.profile.mfa_secret = secret
            user.profile.mfa_enabled = True
            user.profile.save()
            cache.delete(f'mfa_secret_{user.id}')
            return True

        return False

    def verify_totp_with_secret(self, secret: str, token: str) -> bool:
        """使用指定密钥验证TOTP"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=1)
```

#### 2. JWT令牌安全
```python
# backend/authentication/jwt_security.py
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError
from django.core.cache import cache
from django.conf import settings
import hashlib
import time

class SecureJWTService:
    """安全JWT服务"""

    def __init__(self):
        self.blacklist_prefix = 'jwt_blacklist_'
        self.rate_limit_prefix = 'jwt_rate_limit_'

    def create_tokens(self, user):
        """创建令牌"""
        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        # 添加自定义声明
        access['user_id'] = str(user.id)
        access['username'] = user.username
        access['is_staff'] = user.is_staff
        access['permissions'] = list(user.get_all_permissions())

        # 记录令牌创建日志
        self.log_token_creation(user, str(access.token))

        return {
            'access': str(access),
            'refresh': str(refresh),
            'access_expires': access['exp'],
            'refresh_expires': refresh['exp']
        }

    def blacklist_token(self, token_str: str):
        """将令牌加入黑名单"""
        token_hash = hashlib.sha256(token_str.encode()).hexdigest()
        cache.set(
            f'{self.blacklist_prefix}{token_hash}',
            True,
            timeout=settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
        )

    def is_token_blacklisted(self, token_str: str) -> bool:
        """检查令牌是否在黑名单中"""
        token_hash = hashlib.sha256(token_str.encode()).hexdigest()
        return cache.get(f'{self.blacklist_prefix}{token_hash}', False)

    def check_rate_limit(self, user_id: str, ip_address: str) -> bool:
        """检查令牌请求频率限制"""
        key = f'{self.rate_limit_prefix}{user_id}_{ip_address}'
        current_count = cache.get(key, 0)

        if current_count >= 10:  # 每分钟最多10次请求
            return False

        cache.set(key, current_count + 1, timeout=60)
        return True

    def log_token_creation(self, user, token_jti: str):
        """记录令牌创建日志"""
        import logging
        logger = logging.getLogger('security')

        logger.info(f'JWT token created for user {user.username} (ID: {user.id})')
```

#### 3. 密码安全策略
```python
# backend/authentication/password_policy.py
import re
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.contrib.auth.hashers import check_password
from django.utils import timezone
from datetime import timedelta

class PasswordPolicy:
    """密码策略"""

    MIN_LENGTH = 12
    MAX_LENGTH = 128
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL_CHARS = True
    SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?"

    # 密码历史记录数量
    PASSWORD_HISTORY_COUNT = 5

    # 密码过期时间（天）
    PASSWORD_EXPIRE_DAYS = 90

    def validate_password_strength(self, password: str) -> list:
        """验证密码强度"""
        errors = []

        # 长度检查
        if len(password) < self.MIN_LENGTH:
            errors.append(f'密码长度至少{self.MIN_LENGTH}位')

        if len(password) > self.MAX_LENGTH:
            errors.append(f'密码长度不能超过{self.MAX_LENGTH}位')

        # 字符类型检查
        if self.REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            errors.append('密码必须包含大写字母')

        if self.REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            errors.append('密码必须包含小写字母')

        if self.REQUIRE_DIGITS and not re.search(r'\d', password):
            errors.append('密码必须包含数字')

        if self.REQUIRE_SPECIAL_CHARS and not re.search(f'[{re.escape(self.SPECIAL_CHARS)}]', password):
            errors.append(f'密码必须包含特殊字符: {self.SPECIAL_CHARS}')

        # 常见密码检查
        if self.is_common_password(password):
            errors.append('密码过于常见，请选择更复杂的密码')

        # 连续字符检查
        if self.has_consecutive_chars(password):
            errors.append('密码不能包含连续的字符')

        return errors

    def is_common_password(self, password: str) -> bool:
        """检查是否为常见密码"""
        common_passwords = [
            'password123', '123456789', 'qwerty123',
            'admin123456', 'password1234', '12345678'
        ]
        return password.lower() in common_passwords

    def has_consecutive_chars(self, password: str) -> bool:
        """检查是否包含连续字符"""
        for i in range(len(password) - 2):
            if (ord(password[i]) + 1 == ord(password[i + 1]) and
                ord(password[i + 1]) + 1 == ord(password[i + 2])):
                return True
        return False

    def check_password_history(self, user, new_password: str) -> bool:
        """检查密码历史"""
        from .models import PasswordHistory

        recent_passwords = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[:self.PASSWORD_HISTORY_COUNT]

        for history in recent_passwords:
            if check_password(new_password, history.password_hash):
                return False

        return True

    def is_password_expired(self, user) -> bool:
        """检查密码是否过期"""
        if not hasattr(user, 'profile') or not user.profile.password_changed_at:
            return True

        expire_date = user.profile.password_changed_at + timedelta(days=self.PASSWORD_EXPIRE_DAYS)
        return timezone.now() > expire_date

    def save_password_history(self, user, password_hash: str):
        """保存密码历史"""
        from .models import PasswordHistory

        PasswordHistory.objects.create(
            user=user,
            password_hash=password_hash
        )

        # 只保留最近的密码历史
        old_passwords = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[self.PASSWORD_HISTORY_COUNT:]

        for old_password in old_passwords:
            old_password.delete()
```

### 授权控制

#### 1. 基于角色的访问控制 (RBAC)
```python
# backend/authorization/rbac.py
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.db import models

class Role(models.Model):
    """角色模型"""

    name = models.CharField('角色名称', max_length=100, unique=True)
    description = models.TextField('角色描述', blank=True)
    permissions = models.ManyToManyField(Permission, blank=True)
    is_active = models.BooleanField('是否激活', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        db_table = 'auth_roles'
        verbose_name = '角色'
        verbose_name_plural = '角色'

    def __str__(self):
        return self.name

class UserRole(models.Model):
    """用户角色关联"""

    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    granted_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, related_name='granted_roles')
    granted_at = models.DateTimeField('授权时间', auto_now_add=True)
    expires_at = models.DateTimeField('过期时间', null=True, blank=True)

    class Meta:
        db_table = 'auth_user_roles'
        unique_together = ['user', 'role']

class RBACService:
    """RBAC服务"""

    def assign_role(self, user, role, granted_by=None, expires_at=None):
        """分配角色"""
        user_role, created = UserRole.objects.get_or_create(
            user=user,
            role=role,
            defaults={
                'granted_by': granted_by,
                'expires_at': expires_at
            }
        )

        if not created and expires_at:
            user_role.expires_at = expires_at
            user_role.save()

        # 记录授权日志
        self.log_role_assignment(user, role, granted_by)

        return user_role

    def revoke_role(self, user, role, revoked_by=None):
        """撤销角色"""
        try:
            user_role = UserRole.objects.get(user=user, role=role)
            user_role.delete()

            # 记录撤销日志
            self.log_role_revocation(user, role, revoked_by)

            return True
        except UserRole.DoesNotExist:
            return False

    def get_user_permissions(self, user):
        """获取用户权限"""
        permissions = set()

        # 获取用户直接权限
        permissions.update(user.user_permissions.all())

        # 获取角色权限
        user_roles = UserRole.objects.filter(
            user=user,
            role__is_active=True
        ).select_related('role')

        for user_role in user_roles:
            # 检查角色是否过期
            if (user_role.expires_at and
                timezone.now() > user_role.expires_at):
                continue

            permissions.update(user_role.role.permissions.all())

        return permissions

    def has_permission(self, user, permission_codename, obj=None):
        """检查用户是否有权限"""
        if user.is_superuser:
            return True

        user_permissions = self.get_user_permissions(user)

        for perm in user_permissions:
            if perm.codename == permission_codename:
                # 如果有对象级权限检查，在这里实现
                if obj and hasattr(self, f'check_{permission_codename}_object'):
                    return getattr(self, f'check_{permission_codename}_object')(user, obj)
                return True

        return False

    def log_role_assignment(self, user, role, granted_by):
        """记录角色分配日志"""
        import logging
        logger = logging.getLogger('security')

        logger.info(
            f'Role {role.name} assigned to user {user.username} '
            f'by {granted_by.username if granted_by else "system"}'
        )

    def log_role_revocation(self, user, role, revoked_by):
        """记录角色撤销日志"""
        import logging
        logger = logging.getLogger('security')

        logger.info(
            f'Role {role.name} revoked from user {user.username} '
            f'by {revoked_by.username if revoked_by else "system"}'
        )
```

#### 2. 对象级权限控制
```python
# backend/authorization/object_permissions.py
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.db import models

User = get_user_model()

class ObjectPermission(models.Model):
    """对象级权限"""

    PERMISSION_CHOICES = [
        ('view', '查看'),
        ('change', '修改'),
        ('delete', '删除'),
        ('manage', '管理'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    permission = models.CharField('权限类型', max_length=20, choices=PERMISSION_CHOICES)
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='granted_object_permissions')
    granted_at = models.DateTimeField('授权时间', auto_now_add=True)

    class Meta:
        db_table = 'auth_object_permissions'
        unique_together = ['user', 'content_type', 'object_id', 'permission']

class ObjectPermissionService:
    """对象权限服务"""

    def grant_object_permission(self, user, obj, permission, granted_by=None):
        """授予对象权限"""
        content_type = ContentType.objects.get_for_model(obj)

        obj_perm, created = ObjectPermission.objects.get_or_create(
            user=user,
            content_type=content_type,
            object_id=obj.pk,
            permission=permission,
            defaults={'granted_by': granted_by}
        )

        return obj_perm

    def revoke_object_permission(self, user, obj, permission):
        """撤销对象权限"""
        content_type = ContentType.objects.get_for_model(obj)

        try:
            obj_perm = ObjectPermission.objects.get(
                user=user,
                content_type=content_type,
                object_id=obj.pk,
                permission=permission
            )
            obj_perm.delete()
            return True
        except ObjectPermission.DoesNotExist:
            return False

    def has_object_permission(self, user, obj, permission):
        """检查对象权限"""
        if user.is_superuser:
            return True

        content_type = ContentType.objects.get_for_model(obj)

        return ObjectPermission.objects.filter(
            user=user,
            content_type=content_type,
            object_id=obj.pk,
            permission=permission
        ).exists()

    def get_user_objects(self, user, model_class, permission='view'):
        """获取用户有权限的对象"""
        if user.is_superuser:
            return model_class.objects.all()

        content_type = ContentType.objects.get_for_model(model_class)

        object_ids = ObjectPermission.objects.filter(
            user=user,
            content_type=content_type,
            permission=permission
        ).values_list('object_id', flat=True)

        return model_class.objects.filter(pk__in=object_ids)
```