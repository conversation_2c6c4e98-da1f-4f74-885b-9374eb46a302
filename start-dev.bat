@echo off
REM Device Management Platform - Development Environment Startup Script
REM For daily development and initial acceptance testing

echo ========================================
echo Device Management Platform - Dev Environment
echo ========================================
echo.

echo [0/5] Environment Check...
call scripts\check-environment.bat
if %errorlevel% neq 0 (
    echo X Environment check failed, please resolve issues and retry
    pause
    exit /b 1
)

echo [1/5] Stopping existing services...
docker compose -f docker-compose.dev.yml --env-file .env.dev down

echo [2/5] Starting development environment...
docker compose -f docker-compose.dev.yml --env-file .env.dev up -d --build

echo [3/5] Waiting for services to be ready...
timeout /t 15 /nobreak > nul

echo [4/5] Executing database migrations...
docker exec mdm_backend_dev python manage.py migrate
docker exec mdm_backend_dev python manage.py migrate django_celery_beat

echo [5/5] Creating default superuser...
docker exec mdm_backend_dev python scripts/create_default_superuser.py

echo.
echo ========================================
echo Development Environment Started
echo ========================================
echo.

echo Service Status:
docker compose -f docker-compose.dev.yml --env-file .env.dev ps

echo.
echo Access URLs:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:8000/api/
echo - Admin Panel: http://localhost:8000/admin/
echo - API Docs: http://localhost:8000/api/docs/
echo.

echo Database Connection:
echo - Host: localhost
echo - Port: 5432
echo - Database: mdm_dev
echo - User: mdm_user
echo.

echo Default Admin Account:
echo - Username: admin
echo - Password: admin123456
echo - Email: <EMAIL>
echo - WARNING: Please change password in production!
echo.

echo View Logs:
echo - All services: docker compose -f docker-compose.dev.yml --env-file .env.dev logs -f
echo - Backend: docker compose -f docker-compose.dev.yml --env-file .env.dev logs -f backend
echo - Frontend: docker compose -f docker-compose.dev.yml --env-file .env.dev logs -f frontend
echo.

echo ========================================
echo Development environment is ready for testing
echo ========================================

pause
