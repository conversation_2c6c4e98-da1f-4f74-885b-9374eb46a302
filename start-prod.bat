@echo off
REM 设备管理平台生产环境启动脚本
REM 完整的生产级环境，与线上部署完全一致

echo ========================================
echo 设备管理平台 - 生产环境启动
echo ========================================
echo.

echo [1/5] 检查环境配置...
if not exist .env (
    echo 错误: 生产环境配置文件 .env 不存在
    echo 请先创建生产环境配置文件
    pause
    exit /b 1
)

echo [2/5] 停止现有服务...
docker compose -f docker-compose.prod.yml down --remove-orphans

echo [3/5] 清理Docker资源...
docker system prune -f

echo [4/5] 构建并启动生产环境...
echo 正在构建生产镜像，请耐心等待...
docker compose -f docker-compose.prod.yml up -d --build

echo [5/5] 等待服务就绪...
timeout /t 30 /nobreak > nul

echo.
echo ========================================
echo 生产环境启动完成
echo ========================================
echo.

echo 服务状态:
docker compose -f docker-compose.prod.yml ps

echo.
echo 访问地址:
echo - 系统入口: http://localhost (通过Nginx)
echo - 前端应用: http://localhost:3000 (直接访问)
echo - 后端API: http://localhost:8000/api/ (直接访问)
echo - 管理后台: http://localhost/admin/ (通过Nginx)
echo - API文档: http://localhost/api/docs/ (通过Nginx)
echo.

echo 监控地址:
echo - Prometheus: http://localhost:9090
echo - Grafana: http://localhost:3001 (admin/admin123)
echo.

echo 数据库连接:
echo - 主机: localhost
echo - 端口: 5432
echo - 数据库: mdm_prod
echo - 用户: mdm_prod_user
echo.

echo 日志查看:
echo - 所有服务: docker compose -f docker-compose.prod.yml logs -f
echo - 后端服务: docker compose -f docker-compose.prod.yml logs -f backend
echo - 前端服务: docker compose -f docker-compose.prod.yml logs -f frontend
echo - Nginx日志: docker compose -f docker-compose.prod.yml logs -f nginx
echo.

echo ========================================
echo 生产环境已就绪，可以开始最终验收测试
echo ========================================

pause
