# 开发环境 Docker Compose 配置
# 使用最新的 Docker Compose 规范

services:
  # PostgreSQL 数据库
  db:
    image: postgres:15-alpine
    container_name: mdm_postgres_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mdm_dev}
      POSTGRES_USER: ${POSTGRES_USER:-mdm_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-mdm_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mdm_user} -d ${POSTGRES_DB:-mdm_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mdm_dev_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: mdm_redis_dev
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_dev_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - mdm_dev_network

  # Django 后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mdm_backend_dev
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"
    environment:
      - DEBUG=True
      - SECRET_KEY=dev-secret-key-change-in-production
      - DB_NAME=${POSTGRES_DB:-mdm_dev}
      - DB_USER=${POSTGRES_USER:-mdm_user}
      - DB_PASSWORD=${POSTGRES_PASSWORD:-mdm_password}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend,0.0.0.0
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://frontend:3000
      - DJANGO_SETTINGS_MODULE=config.settings
    volumes:
      - ./backend:/app
      - static_dev_volume:/app/staticfiles
      - media_dev_volume:/app/media
      - ./logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - mdm_dev_network
    # 开发环境特殊配置
    stdin_open: true
    tty: true

  # Vue.js 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mdm_frontend_dev
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8000/api
      - VITE_APP_TITLE=设备管理平台 (开发环境)
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - VITE_DEV_SERVER_PORT=3000
    volumes:
      - ./frontend:/app
      - /app/node_modules  # 匿名卷，避免本地node_modules覆盖
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "sh", "-c", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 90s
    restart: unless-stopped
    networks:
      - mdm_dev_network
    # 开发环境特殊配置
    stdin_open: true
    tty: true

  # Celery 异步任务处理
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mdm_celery_dev
    command: celery -A config worker -l debug --concurrency=2
    environment:
      - DEBUG=True
      - SECRET_KEY=dev-secret-key-change-in-production
      - DB_NAME=${POSTGRES_DB:-mdm_dev}
      - DB_USER=${POSTGRES_USER:-mdm_user}
      - DB_PASSWORD=${POSTGRES_PASSWORD:-mdm_password}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=config.settings
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "celery", "-A", "config", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - mdm_dev_network

  # Celery Beat 定时任务
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mdm_celery_beat_dev
    command: celery -A config beat -l debug --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DEBUG=True
      - SECRET_KEY=dev-secret-key-change-in-production
      - DB_NAME=${POSTGRES_DB:-mdm_dev}
      - DB_USER=${POSTGRES_USER:-mdm_user}
      - DB_PASSWORD=${POSTGRES_PASSWORD:-mdm_password}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=config.settings
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "/app/scripts/celery_beat_healthcheck.py"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - mdm_dev_network

  # 开发工具 - MailHog (邮件测试)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: mdm_mailhog_dev
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    restart: unless-stopped
    networks:
      - mdm_dev_network
    profiles:
      - tools

  # 开发工具 - Redis Commander (Redis管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: mdm_redis_commander_dev
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - mdm_dev_network
    profiles:
      - tools

  # 开发工具 - pgAdmin (PostgreSQL管理)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: mdm_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - mdm_dev_network
    profiles:
      - tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  static_dev_volume:
    driver: local
  media_dev_volume:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  mdm_dev_network:
    driver: bridge
    name: mdm_dev_network
