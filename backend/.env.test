# Django测试环境配置
SECRET_KEY=django-insecure-test-environment-only
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# 测试数据库配置
DB_NAME=mdm_test
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432

# Redis配置
REDIS_URL=redis://:redis123@localhost:6379/1

# 邮件配置（测试环境使用控制台输出）
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# 文件存储配置
USE_S3=False

# 第三方服务配置（测试环境禁用）
WECHAT_CORP_ID=
WECHAT_CORP_SECRET=
DINGTALK_APP_KEY=
DINGTALK_APP_SECRET=

# 安全配置
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False
