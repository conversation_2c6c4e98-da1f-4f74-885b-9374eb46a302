"""
URL configuration for device management platform project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from apps.common.health import health_check, readiness_check, liveness_check, simple_health_check, system_info

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Health checks
    path('api/health/', health_check, name='health_check'),
    path('api/health/ready/', readiness_check, name='readiness_check'),
    path('api/health/live/', liveness_check, name='liveness_check'),
    path('health/', simple_health_check, name='simple_health_check'),
    path('api/system/info/', system_info, name='system_info'),

    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # API endpoints
    path('api/auth/', include('apps.users.urls')),
    path('api/devices/', include('apps.devices.urls')),
    path('api/loans/', include('apps.loans.urls')),
    path('api/notifications/', include('apps.notifications.urls')),
    path('api/reports/', include('apps.reports.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
