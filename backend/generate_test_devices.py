#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成200条符合规则的设备测试数据
"""
import os
import sys
import django
import random
from datetime import datetime, timedelta
from decimal import Decimal

# 设置Django环境
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.devices.models import Device
from apps.devices.constants import (
    BRAND_CHOICES, CATEGORY_CHOICES, SPECIAL_SCREEN_CHOICES, 
    OS_CHOICES, STATUS_CHOICES
)
from django.contrib.auth import get_user_model

User = get_user_model()

def generate_test_devices(count=200):
    """生成测试设备数据"""
    
    # 获取管理员用户作为归属者
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        print("❌ 未找到admin用户，请先创建管理员账户")
        return
    
    # 设备名称模板
    device_names = [
        "iPhone", "iPad", "MacBook", "iMac", "Galaxy", "Note", "Pixel", 
        "Mate", "P系列", "Nova", "荣耀", "小米", "红米", "OPPO", "VIVO",
        "OnePlus", "realme", "魅族", "中兴", "努比亚", "摩托罗拉",
        "ThinkPad", "Surface", "Dell", "HP", "联想", "华硕", "宏碁"
    ]
    
    # 型号后缀
    model_suffixes = [
        "Pro", "Max", "Plus", "Ultra", "SE", "Air", "Mini", "Lite",
        "标准版", "高配版", "旗舰版", "青春版", "经典版"
    ]
    
    # CPU选项
    cpu_options = [
        "A17 Pro", "A16 Bionic", "A15 Bionic", "Snapdragon 8 Gen 3", 
        "Snapdragon 8 Gen 2", "Snapdragon 888", "麒麟9000", "麒麟985",
        "天玑9200", "天玑8100", "Intel i7-13700H", "Intel i5-12500H",
        "AMD Ryzen 7 7735HS", "AMD Ryzen 5 6600H", "M3 Pro", "M2", "M1"
    ]
    
    # GPU选项
    gpu_options = [
        "A17 Pro GPU", "A16 GPU", "Adreno 750", "Adreno 740", "Adreno 660",
        "Mali-G78 MP24", "Mali-G77 MP11", "RTX 4060", "RTX 3060", "GTX 1650",
        "Radeon RX 6600M", "Intel Iris Xe", "M3 Pro GPU", "M2 GPU", "M1 GPU"
    ]
    
    # 内存选项
    memory_options = ["4GB", "6GB", "8GB", "12GB", "16GB", "18GB", "24GB", "32GB"]
    
    # 存储选项
    storage_options = ["64GB", "128GB", "256GB", "512GB", "1TB", "2TB"]
    
    # 分辨率选项
    resolution_options = [
        "1920x1080", "2560x1440", "3840x2160", "2556x1179", "2796x1290",
        "3200x1440", "2400x1080", "1600x720", "2340x1080", "3120x1440"
    ]
    
    # 屏幕尺寸选项
    screen_size_options = [
        "5.4英寸", "6.1英寸", "6.7英寸", "6.8英寸", "10.9英寸", "11英寸",
        "12.9英寸", "13.3英寸", "14英寸", "15.6英寸", "16英寸", "24英寸", "27英寸"
    ]
    
    # 系统版本选项
    os_versions = {
        'Android': ['14', '13', '12', '11', '10'],
        'iOS': ['17.2', '17.1', '17.0', '16.7', '16.6'],
        'HarmonyOS': ['4.0', '3.1', '3.0', '2.0'],
        'HarmonyOSNext': ['5.0', '4.2', '4.1']
    }
    
    # 保修期选项
    warranty_options = ["1年", "2年", "3年", "延保1年", "延保2年"]
    
    devices_created = 0
    
    print(f"🚀 开始生成 {count} 条设备数据...")
    
    for i in range(1, count + 1):
        try:
            # 随机选择配置
            brand = random.choice([choice[0] for choice in BRAND_CHOICES])
            category = random.choice([choice[0] for choice in CATEGORY_CHOICES])
            special_screen = random.choice([choice[0] for choice in SPECIAL_SCREEN_CHOICES])
            os = random.choice([choice[0] for choice in OS_CHOICES])
            status = random.choice([choice[0] for choice in STATUS_CHOICES])
            
            # 生成设备名称和型号
            base_name = random.choice(device_names)
            model_suffix = random.choice(model_suffixes)
            name = f"{base_name} {i}"
            model = f"{base_name} {model_suffix}"
            
            # 生成序列号（确保唯一）
            serial_number = f"{brand[:2].upper()}{i:04d}{random.randint(100000, 999999)}"
            
            # 生成技术规格
            cpu = random.choice(cpu_options)
            gpu = random.choice(gpu_options)
            memory = random.choice(memory_options)
            storage = random.choice(storage_options)
            resolution = random.choice(resolution_options)
            screen_size = random.choice(screen_size_options)
            
            # 生成系统版本
            os_version = random.choice(os_versions.get(os, ['1.0']))
            
            # 生成采购信息
            purchase_price = Decimal(str(random.randint(1000, 15000)))
            purchase_date = datetime.now().date() - timedelta(days=random.randint(0, 365))
            warranty_period = random.choice(warranty_options)
            
            # 生成特殊说明
            special_notes_options = [
                f"高性能{category}设备", f"企业级{brand}设备", f"测试专用{category}",
                f"开发团队使用", f"市场部专用", f"设计师工作站", f"会议室设备",
                f"备用{category}", f"新采购设备", f"升级换代设备"
            ]
            special_notes = random.choice(special_notes_options)
            
            # 创建设备
            device = Device.objects.create(
                name=name,
                model=model,
                serial_number=serial_number,
                brand=brand,
                device_number=str(i),  # 自然数递增编号
                category=category,
                owner=admin_user,
                status=status,
                cpu=cpu,
                gpu=gpu,
                memory=memory,
                storage=storage,
                resolution=resolution,
                screen_size=screen_size,
                special_screen=special_screen,
                os=os,
                os_version=os_version,
                purchase_price=purchase_price,
                purchase_date=purchase_date,
                warranty_period=warranty_period,
                special_notes=special_notes
            )
            
            devices_created += 1
            
            if devices_created % 20 == 0:
                print(f"✅ 已生成 {devices_created} 条设备数据...")
                
        except Exception as e:
            print(f"❌ 生成第 {i} 条设备数据失败: {str(e)}")
            continue
    
    print(f"🎉 设备数据生成完成！")
    print(f"📊 成功生成: {devices_created} 条")
    print(f"📊 失败数量: {count - devices_created} 条")
    
    # 统计信息
    print("\n📈 数据统计:")
    for brand_code, brand_name in BRAND_CHOICES:
        count = Device.objects.filter(brand=brand_code).count()
        if count > 0:
            print(f"  {brand_name}: {count} 台")
    
    print(f"\n📱 分类统计:")
    for category_code, category_name in CATEGORY_CHOICES:
        count = Device.objects.filter(category=category_code).count()
        if count > 0:
            print(f"  {category_name}: {count} 台")

if __name__ == "__main__":
    generate_test_devices(200)
