# 生产环境Python依赖包
# 基于requirements.txt，移除开发工具，添加生产优化包

# Django核心
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
django-fsm==2.8.1

# 数据库
psycopg2-binary==2.9.7
redis==5.0.1

# 认证和安全
djangorestframework-simplejwt==5.3.0
cryptography==41.0.7

# API文档
drf-spectacular==0.26.5

# 异步任务
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1

# 生产服务器
gunicorn==21.2.0
gevent==23.9.1

# 监控和日志
django-prometheus==2.3.1
sentry-sdk[django]==1.38.0

# 文件处理
Pillow==10.1.0
python-magic==0.4.27

# 工具库
python-decouple==3.8
python-dotenv==1.0.0
pytz==2023.3
requests==2.31.0
psutil==5.9.6

# 数据处理
openpyxl==3.1.2
pandas==2.1.3

# 缓存
django-redis==5.4.0

# 安全
django-ratelimit==4.1.0
django-security==0.17.0

# 性能优化
django-debug-toolbar==4.2.0  # 仅在DEBUG=True时加载
django-extensions==3.2.3

# 健康检查
django-health-check==3.17.0

# 邮件
django-anymail==10.2

# 存储 (可选)
django-storages==1.14.2
boto3==1.34.0  # AWS S3支持

# 国际化
django-modeltranslation==0.18.11

# 版本信息
# Python: 3.11+
# Django: 4.2 LTS
# PostgreSQL: 14+
# Redis: 6+
