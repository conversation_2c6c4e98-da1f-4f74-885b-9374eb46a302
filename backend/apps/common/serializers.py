"""
通用序列化工具
"""
import json
import uuid
from decimal import Decimal
from datetime import datetime, date, time
from django.core.serializers.json import DjangoJSONEncoder


class UUIDSafeJSONEncoder(DjangoJSONEncoder):
    """
    UUID安全的JSON编码器
    处理UUID、Decimal等特殊类型的序列化
    """
    
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date, time)):
            return obj.isoformat()
        return super().default(obj)


def safe_json_dumps(data, **kwargs):
    """
    安全的JSON序列化函数
    自动处理UUID等特殊类型
    """
    return json.dumps(data, cls=UUIDSafeJSONEncoder, **kwargs)


def convert_uuids_to_strings(data):
    """
    递归地将数据中的UUID对象转换为字符串
    """
    if isinstance(data, dict):
        return {key: convert_uuids_to_strings(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [convert_uuids_to_strings(item) for item in data]
    elif isinstance(data, uuid.UUID):
        return str(data)
    elif isinstance(data, Decimal):
        return float(data)
    else:
        return data


def safe_serialize_queryset(queryset):
    """
    安全地序列化QuerySet，处理UUID字段
    """
    result = []
    for item in queryset:
        if isinstance(item, dict):
            # 如果是values()查询的结果
            safe_item = convert_uuids_to_strings(item)
            result.append(safe_item)
        else:
            # 如果是模型实例
            result.append(item)
    return result


class SafeStatisticsSerializer:
    """
    安全的统计数据序列化器
    专门处理报表统计中的UUID问题
    """
    
    @staticmethod
    def serialize_device_stats(queryset):
        """序列化设备统计数据"""
        result = []
        for item in queryset:
            safe_item = {}
            for key, value in item.items():
                if isinstance(value, uuid.UUID):
                    safe_item[key] = str(value)
                else:
                    safe_item[key] = value
            result.append(safe_item)
        return result
    
    @staticmethod
    def serialize_user_stats(queryset):
        """序列化用户统计数据"""
        result = []
        for item in queryset:
            safe_item = {}
            for key, value in item.items():
                if isinstance(value, uuid.UUID):
                    safe_item[key] = str(value)
                elif key.endswith('_id') and value:
                    # 处理外键ID字段
                    safe_item[key] = str(value)
                else:
                    safe_item[key] = value
            result.append(safe_item)
        return result
    
    @staticmethod
    def serialize_loan_stats(queryset):
        """序列化借用统计数据"""
        result = []
        for item in queryset:
            safe_item = {}
            for key, value in item.items():
                if isinstance(value, uuid.UUID):
                    safe_item[key] = str(value)
                elif key in ['borrower_id', 'device_id', 'approver_id'] and value:
                    # 处理特定的UUID外键字段
                    safe_item[key] = str(value)
                else:
                    safe_item[key] = value
            result.append(safe_item)
        return result


# 装饰器：自动处理UUID序列化
def uuid_safe_response(func):
    """
    装饰器：自动处理视图返回数据中的UUID序列化问题
    """
    from functools import wraps
    from rest_framework.response import Response
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        response = func(*args, **kwargs)
        
        if isinstance(response, Response):
            # 处理Response对象的数据
            safe_data = convert_uuids_to_strings(response.data)
            response.data = safe_data
        
        return response
    
    return wrapper
