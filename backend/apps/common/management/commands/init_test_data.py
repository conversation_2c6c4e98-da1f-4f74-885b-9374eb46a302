"""
初始化测试数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.devices.models import Device, DeviceCategory

User = get_user_model()


class Command(BaseCommand):
    help = '初始化测试数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化测试数据...')
        
        # 创建测试用户
        self.create_test_users()
        
        # 创建设备分类
        self.create_device_categories()
        
        # 创建测试设备
        self.create_test_devices()
        
        self.stdout.write(
            self.style.SUCCESS('测试数据初始化完成！')
        )

    def create_test_users(self):
        """创建测试用户"""
        self.stdout.write('创建测试用户...')
        
        # 管理员用户
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                role='super_admin',
                first_name='管理员',
                department='IT部',
                employee_id='EMP001'
            )
            self.stdout.write(f'✓ 创建管理员用户: {admin_user.username}')

        # 设备归属者
        if not User.objects.filter(username='owner1').exists():
            device_owner = User.objects.create_user(
                username='owner1',
                email='<EMAIL>',
                password='owner123',
                role='device_owner',
                first_name='设备归属者',
                department='研发部',
                employee_id='EMP002'
            )
            self.stdout.write(f'✓ 创建设备归属者: {device_owner.username}')

        # 普通用户
        if not User.objects.filter(username='user1').exists():
            normal_user = User.objects.create_user(
                username='user1',
                email='<EMAIL>',
                password='user123',
                role='normal_user',
                first_name='普通用户',
                department='产品部',
                employee_id='EMP003'
            )
            self.stdout.write(f'✓ 创建普通用户: {normal_user.username}')

    def create_device_categories(self):
        """创建设备分类"""
        self.stdout.write('创建设备分类...')
        
        categories = [
            {'name': '移动设备', 'description': '手机、平板等移动设备'},
            {'name': '计算机设备', 'description': '笔记本电脑、台式机等'},
            {'name': '网络设备', 'description': '路由器、交换机等网络设备'},
            {'name': '办公设备', 'description': '打印机、投影仪等办公设备'},
        ]
        
        for cat_data in categories:
            category, created = DeviceCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'✓ 创建设备分类: {category.name}')

    def create_test_devices(self):
        """创建测试设备"""
        self.stdout.write('创建测试设备...')
        
        # 获取用户和分类
        owner = User.objects.get(username='owner1')
        mobile_category = DeviceCategory.objects.get(name='移动设备')
        computer_category = DeviceCategory.objects.get(name='计算机设备')
        
        devices = [
            {
                'name': 'iPhone 14 Pro',
                'model': 'A2894',
                'serial_number': 'F2LMQXYZ123456',
                'brand': 'Apple',
                'category': mobile_category,
                'owner': owner,
                'status': 'available',
                'cpu': 'A16 Bionic',
                'memory': '6GB',
                'storage': '256GB',
                'screen_size': '6.1英寸',
                'os': 'iOS',
                'os_version': '16.0',
                'purchase_price': 7999.00,
                'special_notes': '公司配发的测试设备'
            },
            {
                'name': 'MacBook Pro',
                'model': 'M2 13寸',
                'serial_number': 'C02YX1234567',
                'brand': 'Apple',
                'category': computer_category,
                'owner': owner,
                'status': 'available',
                'cpu': 'Apple M2',
                'memory': '16GB',
                'storage': '512GB SSD',
                'screen_size': '13.3英寸',
                'resolution': '2560x1600',
                'os': 'macOS',
                'os_version': 'Ventura 13.0',
                'purchase_price': 12999.00,
                'special_notes': '开发专用笔记本'
            },
            {
                'name': 'iPad Air',
                'model': 'M1 第5代',
                'serial_number': 'DMQXYZ789012',
                'brand': 'Apple',
                'category': mobile_category,
                'owner': owner,
                'status': 'idle',
                'cpu': 'Apple M1',
                'memory': '8GB',
                'storage': '256GB',
                'screen_size': '10.9英寸',
                'resolution': '2360x1640',
                'os': 'iPadOS',
                'os_version': '16.0',
                'purchase_price': 4399.00,
                'special_notes': '设计团队使用'
            },
            {
                'name': 'ThinkPad X1 Carbon',
                'model': 'Gen 10',
                'serial_number': 'PC123ABC789',
                'brand': 'Lenovo',
                'category': computer_category,
                'owner': owner,
                'status': 'available',
                'cpu': 'Intel i7-1260P',
                'memory': '16GB',
                'storage': '512GB SSD',
                'screen_size': '14英寸',
                'resolution': '1920x1200',
                'os': 'Windows',
                'os_version': '11 Pro',
                'purchase_price': 9999.00,
                'special_notes': '轻薄商务笔记本'
            }
        ]
        
        for device_data in devices:
            device, created = Device.objects.get_or_create(
                serial_number=device_data['serial_number'],
                defaults=device_data
            )
            if created:
                self.stdout.write(f'✓ 创建测试设备: {device.name}')
        
        self.stdout.write(f'总共创建了 {Device.objects.count()} 台设备')
