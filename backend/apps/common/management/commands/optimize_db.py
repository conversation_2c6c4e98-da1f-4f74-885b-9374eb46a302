"""
数据库优化管理命令
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.common.db_optimization import DatabaseOptimizer, CacheWarmer
import json


class Command(BaseCommand):
    help = '执行数据库优化操作'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze',
            action='store_true',
            help='分析数据库性能',
        )
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='清理数据库',
        )
        parser.add_argument(
            '--warm-cache',
            action='store_true',
            help='预热缓存',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='执行所有优化操作',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('开始数据库优化...')
        )
        
        if options['analyze'] or options['all']:
            self.analyze_database()
        
        if options['vacuum'] or options['all']:
            self.vacuum_database()
        
        if options['warm_cache'] or options['all']:
            self.warm_cache()
        
        self.stdout.write(
            self.style.SUCCESS('数据库优化完成！')
        )
    
    def analyze_database(self):
        """分析数据库"""
        self.stdout.write('正在分析数据库...')
        
        try:
            report = DatabaseOptimizer.get_optimization_report()
            
            # 显示表统计信息
            self.stdout.write('\n=== 表统计信息 ===')
            for table, stats in report['table_stats'].items():
                self.stdout.write(
                    f"{table}: {stats['row_count']} 行, "
                    f"{stats['column_count']} 列"
                )
            
            # 显示索引建议
            self.stdout.write('\n=== 索引优化建议 ===')
            if report['index_recommendations']:
                for rec in report['index_recommendations']:
                    self.stdout.write(f"建议: {rec['reason']}")
                    self.stdout.write(f"SQL: {rec['sql']}")
                    self.stdout.write('')
            else:
                self.stdout.write('暂无索引优化建议')
            
            # 保存报告
            report_file = f"db_optimization_report_{timezone.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            self.stdout.write(
                self.style.SUCCESS(f'分析报告已保存到: {report_file}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'数据库分析失败: {e}')
            )
    
    def vacuum_database(self):
        """清理数据库"""
        self.stdout.write('正在清理数据库...')
        
        try:
            DatabaseOptimizer.vacuum_database()
            self.stdout.write(
                self.style.SUCCESS('数据库清理完成')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'数据库清理失败: {e}')
            )
    
    def warm_cache(self):
        """预热缓存"""
        self.stdout.write('正在预热缓存...')
        
        try:
            CacheWarmer.warm_all_caches()
            self.stdout.write(
                self.style.SUCCESS('缓存预热完成')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'缓存预热失败: {e}')
            )
