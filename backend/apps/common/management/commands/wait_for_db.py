"""
等待数据库就绪的管理命令
"""
import time
from django.core.management.base import BaseCommand
from django.db import connections
from django.db.utils import OperationalError


class Command(BaseCommand):
    """等待数据库连接就绪"""
    
    help = '等待数据库连接就绪'
    
    def handle(self, *args, **options):
        """命令处理逻辑"""
        self.stdout.write('等待数据库连接...')
        db_conn = None
        while not db_conn:
            try:
                db_conn = connections['default']
                db_conn.ensure_connection()
            except OperationalError:
                self.stdout.write('数据库不可用，等待1秒...')
                time.sleep(1)
        
        self.stdout.write(
            self.style.SUCCESS('数据库连接成功！')
        )
