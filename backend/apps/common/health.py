"""
系统健康检查模块
"""
import time
import psutil
from django.conf import settings
from django.core.cache import cache
from django.db import connection
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    系统健康检查端点
    返回系统各组件的健康状态
    """
    health_status = {
        'status': 'healthy',
        'timestamp': time.time(),
        'version': getattr(settings, 'VERSION', '1.0.0'),
        'environment': getattr(settings, 'ENVIRONMENT', 'development'),
        'checks': {}
    }
    
    overall_healthy = True
    
    # 数据库健康检查
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        health_status['checks']['database'] = {
            'status': 'healthy',
            'message': 'Database connection successful'
        }
    except Exception as e:
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        overall_healthy = False
    
    # Redis缓存健康检查
    try:
        cache.set('health_check', 'ok', 30)
        cache_value = cache.get('health_check')
        if cache_value == 'ok':
            health_status['checks']['cache'] = {
                'status': 'healthy',
                'message': 'Cache connection successful'
            }
        else:
            raise Exception('Cache value mismatch')
    except Exception as e:
        health_status['checks']['cache'] = {
            'status': 'unhealthy',
            'message': f'Cache connection failed: {str(e)}'
        }
        overall_healthy = False
    
    # 系统资源检查
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_status['checks']['system'] = {
            'status': 'healthy',
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'disk_percent': (disk.used / disk.total) * 100,
            'message': 'System resources within normal range'
        }
        
        # 检查资源使用率是否过高
        if cpu_percent > 90 or memory.percent > 90:
            health_status['checks']['system']['status'] = 'warning'
            health_status['checks']['system']['message'] = 'High resource usage detected'
            
    except Exception as e:
        health_status['checks']['system'] = {
            'status': 'unhealthy',
            'message': f'System check failed: {str(e)}'
        }
        overall_healthy = False
    
    # 设置总体状态
    if not overall_healthy:
        health_status['status'] = 'unhealthy'
        return Response(health_status, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    return Response(health_status, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def readiness_check(request):
    """
    就绪检查端点
    检查应用是否准备好接收流量
    """
    readiness_status = {
        'status': 'ready',
        'timestamp': time.time(),
        'checks': {}
    }
    
    ready = True
    
    # 检查数据库迁移状态
    try:
        from django.core.management import execute_from_command_line
        from django.core.management.commands.migrate import Command
        
        # 这里可以添加更复杂的迁移检查逻辑
        readiness_status['checks']['migrations'] = {
            'status': 'ready',
            'message': 'Database migrations are up to date'
        }
    except Exception as e:
        readiness_status['checks']['migrations'] = {
            'status': 'not_ready',
            'message': f'Migration check failed: {str(e)}'
        }
        ready = False
    
    # 检查关键服务依赖
    try:
        # 检查数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_migrations")
            cursor.fetchone()
            
        readiness_status['checks']['dependencies'] = {
            'status': 'ready',
            'message': 'All dependencies are available'
        }
    except Exception as e:
        readiness_status['checks']['dependencies'] = {
            'status': 'not_ready',
            'message': f'Dependency check failed: {str(e)}'
        }
        ready = False
    
    if not ready:
        readiness_status['status'] = 'not_ready'
        return Response(readiness_status, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    
    return Response(readiness_status, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([AllowAny])
def liveness_check(request):
    """
    存活检查端点
    简单检查应用是否还在运行
    """
    return Response({
        'status': 'alive',
        'timestamp': time.time(),
        'message': 'Application is running'
    }, status=status.HTTP_200_OK)


@csrf_exempt
@require_http_methods(["GET"])
def simple_health_check(request):
    """
    简单的健康检查端点 (不依赖DRF)
    用于负载均衡器等外部系统的快速检查
    """
    try:
        # 快速数据库检查
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': time.time()
        })
    except Exception:
        return JsonResponse({
            'status': 'unhealthy',
            'timestamp': time.time()
        }, status=503)


def get_system_info():
    """
    获取系统信息
    """
    try:
        return {
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total,
            'disk_total': psutil.disk_usage('/').total,
            'boot_time': psutil.boot_time(),
            'python_version': f"{psutil.sys.version_info.major}.{psutil.sys.version_info.minor}.{psutil.sys.version_info.micro}",
        }
    except Exception as e:
        return {'error': str(e)}


@api_view(['GET'])
@permission_classes([AllowAny])
def system_info(request):
    """
    系统信息端点
    """
    info = {
        'application': {
            'name': 'Device Management System',
            'version': getattr(settings, 'VERSION', '1.0.0'),
            'environment': getattr(settings, 'ENVIRONMENT', 'development'),
            'debug': settings.DEBUG,
        },
        'system': get_system_info(),
        'timestamp': time.time()
    }
    
    return Response(info, status=status.HTTP_200_OK)
