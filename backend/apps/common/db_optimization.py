"""
数据库优化工具
"""
from django.core.management.base import BaseCommand
from django.db import connection
from django.apps import apps
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """数据库优化器"""
    
    @staticmethod
    def analyze_slow_queries():
        """分析慢查询"""
        with connection.cursor() as cursor:
            # SQLite的查询分析
            if 'sqlite' in connection.vendor:
                cursor.execute("PRAGMA compile_options;")
                options = cursor.fetchall()
                logger.info(f"SQLite编译选项: {options}")
                
                # 检查是否启用了查询计划器
                cursor.execute("PRAGMA query_planner_limit;")
                limit = cursor.fetchone()
                logger.info(f"查询计划器限制: {limit}")
    
    @staticmethod
    def optimize_indexes():
        """优化索引"""
        recommendations = []
        
        # 检查常用查询的索引
        index_recommendations = [
            {
                'table': 'devices',
                'columns': ['status', 'is_deleted'],
                'reason': '设备状态查询优化'
            },
            {
                'table': 'devices', 
                'columns': ['owner_id', 'status'],
                'reason': '按归属者查询设备优化'
            },
            {
                'table': 'loans_loanapplication',
                'columns': ['status', 'created_at'],
                'reason': '借用申请状态和时间查询优化'
            },
            {
                'table': 'loans_loanapplication',
                'columns': ['borrower_id', 'status'],
                'reason': '用户借用记录查询优化'
            },
            {
                'table': 'loans_loanapplication',
                'columns': ['device_id', 'status'],
                'reason': '设备借用记录查询优化'
            },
            {
                'table': 'users_user',
                'columns': ['role', 'is_active'],
                'reason': '用户角色查询优化'
            }
        ]
        
        with connection.cursor() as cursor:
            for rec in index_recommendations:
                # 检查索引是否已存在
                if 'sqlite' in connection.vendor:
                    cursor.execute(
                        "SELECT name FROM sqlite_master WHERE type='index' AND tbl_name=?",
                        [rec['table']]
                    )
                    existing_indexes = [row[0] for row in cursor.fetchall()]
                    
                    # 生成索引名
                    index_name = f"idx_{rec['table']}_{'_'.join(rec['columns'])}"
                    
                    if index_name not in existing_indexes:
                        recommendations.append({
                            'sql': f"CREATE INDEX {index_name} ON {rec['table']} ({', '.join(rec['columns'])});",
                            'reason': rec['reason']
                        })
        
        return recommendations
    
    @staticmethod
    def analyze_table_stats():
        """分析表统计信息"""
        stats = {}
        
        with connection.cursor() as cursor:
            if 'sqlite' in connection.vendor:
                # 获取所有表的统计信息
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                tables = [row[0] for row in cursor.fetchall()]
                
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                        count = cursor.fetchone()[0]

                        # 获取表大小信息
                        cursor.execute(f"PRAGMA table_info(`{table}`)")
                        columns = cursor.fetchall()
                    except Exception as e:
                        logger.warning(f"无法分析表 {table}: {e}")
                        continue
                    
                    stats[table] = {
                        'row_count': count,
                        'column_count': len(columns),
                        'columns': [col[1] for col in columns]
                    }
        
        return stats
    
    @staticmethod
    def vacuum_database():
        """清理数据库"""
        with connection.cursor() as cursor:
            if 'sqlite' in connection.vendor:
                cursor.execute("VACUUM;")
                logger.info("数据库VACUUM完成")
                
                cursor.execute("ANALYZE;")
                logger.info("数据库ANALYZE完成")
    
    @staticmethod
    def get_optimization_report():
        """获取优化报告"""
        try:
            report = {
                'timestamp': timezone.now().isoformat(),
                'database_vendor': connection.vendor,
                'table_stats': DatabaseOptimizer.analyze_table_stats(),
                'index_recommendations': DatabaseOptimizer.optimize_indexes(),
            }
            return report
        except Exception as e:
            logger.error(f"生成优化报告失败: {e}")
            return {
                'timestamp': timezone.now().isoformat(),
                'database_vendor': connection.vendor,
                'error': str(e),
                'table_stats': {},
                'index_recommendations': []
            }


class QueryProfiler:
    """查询性能分析器"""
    
    def __init__(self):
        self.queries = []
    
    def __enter__(self):
        from django.db import reset_queries
        from django.conf import settings
        
        # 确保DEBUG模式开启以记录查询
        self.original_debug = settings.DEBUG
        settings.DEBUG = True
        
        reset_queries()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        from django.db import connection
        from django.conf import settings
        
        self.queries = connection.queries
        settings.DEBUG = self.original_debug
    
    def get_slow_queries(self, threshold=0.1):
        """获取慢查询（超过阈值的查询）"""
        slow_queries = []
        
        for query in self.queries:
            time = float(query['time'])
            if time > threshold:
                slow_queries.append({
                    'sql': query['sql'],
                    'time': time,
                    'formatted_time': f"{time:.3f}s"
                })
        
        return sorted(slow_queries, key=lambda x: x['time'], reverse=True)
    
    def get_duplicate_queries(self):
        """获取重复查询"""
        query_counts = {}
        
        for query in self.queries:
            sql = query['sql']
            if sql in query_counts:
                query_counts[sql] += 1
            else:
                query_counts[sql] = 1
        
        duplicates = {sql: count for sql, count in query_counts.items() if count > 1}
        return sorted(duplicates.items(), key=lambda x: x[1], reverse=True)
    
    def get_summary(self):
        """获取查询摘要"""
        if not self.queries:
            return {
                'total_queries': 0,
                'total_time': 0,
                'avg_time': 0,
                'slow_queries': [],
                'duplicate_queries': []
            }
        
        total_time = sum(float(q['time']) for q in self.queries)
        avg_time = total_time / len(self.queries)
        
        return {
            'total_queries': len(self.queries),
            'total_time': f"{total_time:.3f}s",
            'avg_time': f"{avg_time:.3f}s",
            'slow_queries': self.get_slow_queries(),
            'duplicate_queries': self.get_duplicate_queries()
        }


# 性能监控中间件
class PerformanceMonitoringMiddleware:
    """性能监控中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        import time
        start_time = time.time()
        
        # 记录请求开始
        logger.info(f"请求开始: {request.method} {request.path}")
        
        with QueryProfiler() as profiler:
            response = self.get_response(request)
        
        # 计算响应时间
        end_time = time.time()
        duration = end_time - start_time
        
        # 记录性能信息
        query_summary = profiler.get_summary()
        
        logger.info(
            f"请求完成: {request.method} {request.path} "
            f"响应时间: {duration:.3f}s "
            f"查询数量: {query_summary['total_queries']} "
            f"查询时间: {query_summary['total_time']}"
        )
        
        # 慢请求警告
        if duration > 2.0:
            logger.warning(
                f"慢请求警告: {request.method} {request.path} "
                f"耗时: {duration:.3f}s"
            )
            
            # 记录慢查询
            slow_queries = profiler.get_slow_queries(0.1)
            if slow_queries:
                logger.warning(f"慢查询数量: {len(slow_queries)}")
                for query in slow_queries[:3]:  # 只记录前3个最慢的
                    logger.warning(f"慢查询: {query['formatted_time']} - {query['sql'][:100]}...")
        
        return response


# 缓存预热工具
class CacheWarmer:
    """缓存预热工具"""
    
    @staticmethod
    def warm_dashboard_cache():
        """预热仪表盘缓存"""
        from apps.reports.views import DashboardStatisticsView
        from django.test import RequestFactory
        from apps.users.models import User
        
        try:
            # 创建模拟请求
            factory = RequestFactory()
            request = factory.get('/api/reports/dashboard/')
            
            # 获取管理员用户
            admin_user = User.objects.filter(is_superuser=True).first()
            if admin_user:
                request.user = admin_user
                
                # 调用视图预热缓存
                view = DashboardStatisticsView()
                view.get(request)
                
                logger.info("仪表盘缓存预热完成")
            else:
                logger.warning("未找到管理员用户，跳过缓存预热")
                
        except Exception as e:
            logger.error(f"缓存预热失败: {e}")
    
    @staticmethod
    def warm_all_caches():
        """预热所有缓存"""
        CacheWarmer.warm_dashboard_cache()
        # 可以添加更多缓存预热方法
