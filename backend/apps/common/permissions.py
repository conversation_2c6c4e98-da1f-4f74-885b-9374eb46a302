"""
通用权限类
"""
from rest_framework import permissions


class IsAdminUser(permissions.BasePermission):
    """
    只允许管理员用户访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_admin
        )


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    只允许对象所有者或管理员访问
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有对象
        if request.user.is_device_admin:
            return True
        
        # 检查是否为对象所有者
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'owner'):
            return obj.owner == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        # 如果对象就是用户本身
        if obj == request.user:
            return True
        
        return False


class IsDeviceOwnerOrAdmin(permissions.BasePermission):
    """
    只允许设备归属者或管理员访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_owner
        )
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有设备
        if request.user.is_device_admin:
            return True
        
        # 设备归属者可以访问自己管理的设备
        if hasattr(obj, 'owner'):
            return obj.owner == request.user
        
        return False


class IsReadOnlyOrAdmin(permissions.BasePermission):
    """
    只读权限或管理员权限
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 管理员有完全权限
        if request.user.is_device_admin:
            return True
        
        # 其他用户只有只读权限
        return request.method in permissions.SAFE_METHODS


class CanBorrowDevice(permissions.BasePermission):
    """
    可以借用设备的权限
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 设备必须是可借用状态
        if hasattr(obj, 'status') and obj.status != 'available':
            return False
        
        # 不能借用自己管理的设备
        if hasattr(obj, 'owner') and obj.owner == request.user:
            return False
        
        return True


class CanManageDevice(permissions.BasePermission):
    """
    可以管理设备的权限
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_owner
        )
    
    def has_object_permission(self, request, view, obj):
        return request.user.can_manage_device(obj)
