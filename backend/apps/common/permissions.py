"""
通用权限类 - 基于最终权限设计v2.0
"""
from rest_framework import permissions
from django.core.cache import cache
from apps.devices.constants import STATUS_TRANSITION_PERMISSIONS


class IsAdminUser(permissions.BasePermission):
    """
    只允许管理员用户访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_admin
        )


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    只允许对象所有者或管理员访问
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有对象
        if request.user.is_device_admin:
            return True
        
        # 检查是否为对象所有者
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'owner'):
            return obj.owner == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        # 如果对象就是用户本身
        if obj == request.user:
            return True
        
        return False


class IsDeviceOwnerOrAdmin(permissions.BasePermission):
    """
    只允许设备归属者或管理员访问
    """
    
    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_owner
        )
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有设备
        if request.user.is_device_admin:
            return True
        
        # 设备归属者可以访问自己管理的设备
        if hasattr(obj, 'owner'):
            return obj.owner == request.user
        
        return False


class IsReadOnlyOrAdmin(permissions.BasePermission):
    """
    只读权限或管理员权限
    """
    
    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False
        
        # 管理员有完全权限
        if request.user.is_device_admin:
            return True
        
        # 其他用户只有只读权限
        return request.method in permissions.SAFE_METHODS


class CanBorrowDevice(permissions.BasePermission):
    """
    可以借用设备的权限
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 设备必须是可借用状态
        if hasattr(obj, 'status') and obj.status != 'available':
            return False
        
        # 不能借用自己管理的设备
        if hasattr(obj, 'owner') and obj.owner == request.user:
            return False
        
        return True


class CanManageDevice(permissions.BasePermission):
    """
    可以管理设备的权限
    """

    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_owner
        )

    def has_object_permission(self, request, view, obj):
        return request.user.can_manage_device(obj)


class DevicePermissionService:
    """设备权限管理服务"""

    @staticmethod
    def can_change_device_status(user, device, target_status):
        """检查用户是否可以修改设备状态"""
        if not user.is_authenticated:
            return False, "用户未认证"

        current_status = device.status
        user_role = user.role

        # 获取用户角色的状态转换权限
        role_permissions = STATUS_TRANSITION_PERMISSIONS.get(user_role, {})
        allowed_transitions = role_permissions.get(current_status, [])

        if target_status not in allowed_transitions:
            return False, f"{user.get_role_display()}不能将设备从{device.get_status_display()}状态修改为{dict(device.STATUS_CHOICES).get(target_status, target_status)}"

        # 对象级权限检查
        if user_role == 'device_owner':
            if device.owner != user:
                return False, "只能管理自己的设备"

        return True, "权限检查通过"

    @staticmethod
    def can_assign_device(user, device, target_user):
        """检查用户是否可以分配设备"""
        if not user.is_authenticated:
            return False, "用户未认证"

        # 只有超级管理员和设备管理员可以分配设备
        if not user.is_device_admin:
            return False, "只有管理员可以分配设备"

        # 设备必须在库存中
        if device.status != 'in_stock':
            return False, "只能分配库存中的设备"

        # 目标用户必须是设备归属者及以上角色
        if not target_user.is_device_owner:
            return False, "只能分配给设备归属者及以上角色的用户"

        return True, "权限检查通过"

    @staticmethod
    def can_borrow_device(user, device):
        """检查用户是否可以借用设备"""
        if not user.is_authenticated:
            return False, "用户未认证"

        # 设备必须是可借用状态
        if device.status != 'available':
            return False, "设备不在可借用状态"

        # 不能借用自己管理的设备
        if device.owner == user:
            return False, "不能借用自己管理的设备"

        return True, "权限检查通过"

    @staticmethod
    def can_approve_loan(user, loan_application):
        """检查用户是否可以审批借用申请"""
        if not user.is_authenticated:
            return False, "用户未认证"

        # 超级管理员可以审批所有申请
        if user.is_super_admin:
            return True, "超级管理员权限"

        # 设备归属者可以审批自己设备的申请
        if user.role == 'device_owner' and loan_application.device.owner == user:
            return True, "设备归属者权限"

        return False, "只有设备归属者或超级管理员可以审批借用申请"

    @staticmethod
    def can_warehouse_in_device(user, device):
        """检查用户是否可以将设备入库"""
        if not user.is_authenticated:
            return False, "用户未认证"

        # 超级管理员和设备管理员可以操作所有设备
        if user.is_device_admin:
            return True, "管理员权限"

        # 设备归属者可以将自己的设备入库
        if user.role == 'device_owner' and device.owner == user:
            # 只能入库特定状态的设备
            if device.status in ['assigned', 'locked']:
                return True, "设备归属者权限"
            else:
                return False, f"不能入库{device.get_status_display()}状态的设备"

        return False, "只有设备归属者或管理员可以操作设备入库"


class DeviceStatusPermission(permissions.BasePermission):
    """设备状态管理权限"""

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        if request.method == 'GET':
            return True

        # 获取目标状态
        target_status = request.data.get('status')
        if not target_status:
            return False

        can_change, message = DevicePermissionService.can_change_device_status(
            request.user, obj, target_status
        )

        if not can_change:
            self.message = message

        return can_change


class DeviceAssignPermission(permissions.BasePermission):
    """设备分配权限"""

    def has_permission(self, request, view):
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_device_admin
        )

    def has_object_permission(self, request, view, obj):
        # 获取目标用户
        owner_id = request.data.get('owner_id')
        if not owner_id:
            return False

        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            target_user = User.objects.get(id=owner_id)
        except User.DoesNotExist:
            self.message = "目标用户不存在"
            return False

        can_assign, message = DevicePermissionService.can_assign_device(
            request.user, obj, target_user
        )

        if not can_assign:
            self.message = message

        return can_assign
