"""
分页配置模块
提供统一的分页配置和自定义分页类
"""

from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from collections import OrderedDict


class StandardResultsSetPagination(PageNumberPagination):
    """
    标准分页配置
    - 默认每页20条记录
    - 支持客户端自定义每页数量
    - 最大每页100条记录
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    page_query_param = 'page'

    def get_paginated_response(self, data):
        """
        自定义分页响应格式
        返回统一的分页信息结构
        """
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('page_size', self.page_size),
            ('current_page', self.page.number),
            ('total_pages', self.page.paginator.num_pages),
            ('results', data)
        ]))


class LargeResultsSetPagination(PageNumberPagination):
    """
    大数据集分页配置
    - 默认每页50条记录
    - 支持客户端自定义每页数量
    - 最大每页200条记录
    """
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200
    page_query_param = 'page'

    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('page_size', self.page_size),
            ('current_page', self.page.number),
            ('total_pages', self.page.paginator.num_pages),
            ('results', data)
        ]))


class SmallResultsSetPagination(PageNumberPagination):
    """
    小数据集分页配置
    - 默认每页10条记录
    - 支持客户端自定义每页数量
    - 最大每页50条记录
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 50
    page_query_param = 'page'

    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('page_size', self.page_size),
            ('current_page', self.page.number),
            ('total_pages', self.page.paginator.num_pages),
            ('results', data)
        ]))


class CustomPagination(PageNumberPagination):
    """
    自定义分页配置
    可以根据需要动态调整分页参数
    """
    def __init__(self, page_size=20, max_page_size=100):
        super().__init__()
        self.page_size = page_size
        self.max_page_size = max_page_size
        self.page_size_query_param = 'page_size'
        self.page_query_param = 'page'

    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('page_size', self.page_size),
            ('current_page', self.page.number),
            ('total_pages', self.page.paginator.num_pages),
            ('results', data)
        ]))


def get_pagination_info(paginator, page_obj):
    """
    获取分页信息的工具函数
    
    Args:
        paginator: Django分页器对象
        page_obj: 当前页面对象
        
    Returns:
        dict: 包含分页信息的字典
    """
    return {
        'count': paginator.count,
        'current_page': page_obj.number,
        'total_pages': paginator.num_pages,
        'page_size': page_obj.paginator.per_page,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
        'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
        'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
    }


def paginate_queryset(queryset, page_size=20, page_number=1):
    """
    对查询集进行分页的工具函数
    
    Args:
        queryset: Django查询集
        page_size: 每页记录数
        page_number: 页码
        
    Returns:
        tuple: (分页后的数据, 分页信息)
    """
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    
    paginator = Paginator(queryset, page_size)
    
    try:
        page_obj = paginator.page(page_number)
    except PageNotAnInteger:
        # 如果页码不是整数，返回第一页
        page_obj = paginator.page(1)
    except EmptyPage:
        # 如果页码超出范围，返回最后一页
        page_obj = paginator.page(paginator.num_pages)
    
    pagination_info = get_pagination_info(paginator, page_obj)
    
    return page_obj.object_list, pagination_info
