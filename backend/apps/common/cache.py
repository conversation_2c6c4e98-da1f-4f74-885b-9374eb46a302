"""
缓存工具类
提供统一的缓存管理功能
"""
from django.core.cache import cache
from django.conf import settings
from functools import wraps
import hashlib
import json
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """缓存管理器"""
    
    # 缓存键前缀
    CACHE_PREFIX = 'device_mgmt'
    
    # 缓存时间配置（秒）
    CACHE_TIMEOUTS = {
        'dashboard_stats': 300,      # 5分钟
        'device_stats': 600,         # 10分钟
        'loan_stats': 300,           # 5分钟
        'user_stats': 1800,          # 30分钟
        'device_list': 180,          # 3分钟
        'user_list': 600,            # 10分钟
        'loan_list': 120,            # 2分钟
    }
    
    @classmethod
    def get_cache_key(cls, key_type: str, **kwargs) -> str:
        """生成缓存键"""
        # 基础键
        base_key = f"{cls.CACHE_PREFIX}:{key_type}"
        
        # 如果有参数，添加参数哈希
        if kwargs:
            params_str = json.dumps(kwargs, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            base_key += f":{params_hash}"
        
        return base_key
    
    @classmethod
    def get(cls, key_type: str, **kwargs):
        """获取缓存数据"""
        cache_key = cls.get_cache_key(key_type, **kwargs)
        return cache.get(cache_key)
    
    @classmethod
    def set(cls, key_type: str, data, **kwargs):
        """设置缓存数据"""
        cache_key = cls.get_cache_key(key_type, **kwargs)
        timeout = cls.CACHE_TIMEOUTS.get(key_type, 300)
        
        try:
            cache.set(cache_key, data, timeout)
            logger.debug(f"缓存设置成功: {cache_key}, 超时: {timeout}秒")
        except Exception as e:
            logger.error(f"缓存设置失败: {cache_key}, 错误: {e}")
    
    @classmethod
    def delete(cls, key_type: str, **kwargs):
        """删除缓存数据"""
        cache_key = cls.get_cache_key(key_type, **kwargs)
        cache.delete(cache_key)
        logger.debug(f"缓存删除: {cache_key}")
    
    @classmethod
    def clear_pattern(cls, pattern: str):
        """清除匹配模式的缓存"""
        # 注意：这个功能需要Redis支持，本地内存缓存不支持
        try:
            if hasattr(cache, 'delete_pattern'):
                cache.delete_pattern(f"{cls.CACHE_PREFIX}:{pattern}*")
                logger.debug(f"批量清除缓存: {pattern}")
        except Exception as e:
            logger.warning(f"批量清除缓存失败: {e}")


def cache_result(key_type: str, timeout: int = None, **cache_kwargs):
    """
    缓存装饰器
    
    Args:
        key_type: 缓存类型
        timeout: 缓存超时时间（秒）
        **cache_kwargs: 缓存键参数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键参数
            cache_params = cache_kwargs.copy()
            
            # 如果是类方法，添加用户ID等上下文信息
            if args and hasattr(args[0], 'request'):
                request = args[0].request
                if hasattr(request, 'user') and request.user.is_authenticated:
                    cache_params['user_id'] = request.user.id
                    cache_params['user_role'] = request.user.role
            
            # 尝试从缓存获取
            cached_data = CacheManager.get(key_type, **cache_params)
            if cached_data is not None:
                logger.debug(f"缓存命中: {key_type}")
                return cached_data
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 设置缓存
            if timeout:
                original_timeout = CacheManager.CACHE_TIMEOUTS.get(key_type)
                CacheManager.CACHE_TIMEOUTS[key_type] = timeout
                CacheManager.set(key_type, result, **cache_params)
                if original_timeout:
                    CacheManager.CACHE_TIMEOUTS[key_type] = original_timeout
            else:
                CacheManager.set(key_type, result, **cache_params)
            
            logger.debug(f"缓存设置: {key_type}")
            return result
        
        return wrapper
    return decorator


class CacheInvalidator:
    """缓存失效管理器"""
    
    # 缓存依赖关系映射
    CACHE_DEPENDENCIES = {
        'device': ['dashboard_stats', 'device_stats', 'device_list'],
        'loan': ['dashboard_stats', 'loan_stats', 'loan_list'],
        'user': ['dashboard_stats', 'user_stats', 'user_list'],
    }
    
    @classmethod
    def invalidate_related_caches(cls, model_type: str):
        """使相关缓存失效"""
        related_caches = cls.CACHE_DEPENDENCIES.get(model_type, [])
        
        for cache_type in related_caches:
            CacheManager.clear_pattern(cache_type)
            logger.info(f"缓存失效: {cache_type} (由于 {model_type} 变更)")
    
    @classmethod
    def invalidate_user_cache(cls, user_id: int):
        """使特定用户的缓存失效"""
        patterns = ['device_list', 'loan_list', 'dashboard_stats']
        for pattern in patterns:
            CacheManager.clear_pattern(f"{pattern}:*user_id*{user_id}*")


# 性能监控装饰器
def monitor_performance(func_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 记录性能日志
                logger.info(f"性能监控 - {func_name or func.__name__}: {execution_time:.3f}秒")
                
                # 如果执行时间过长，记录警告
                if execution_time > 2.0:
                    logger.warning(f"慢查询警告 - {func_name or func.__name__}: {execution_time:.3f}秒")
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"执行错误 - {func_name or func.__name__}: {execution_time:.3f}秒, 错误: {e}")
                raise
        
        return wrapper
    return decorator


# 数据库查询优化工具
class QueryOptimizer:
    """数据库查询优化工具"""
    
    @staticmethod
    def optimize_device_queries():
        """优化设备相关查询"""
        from apps.devices.models import Device
        
        # 预加载相关数据，减少N+1查询
        return Device.objects.select_related(
            'category', 'owner', 'current_user'
        ).prefetch_related(
            'devicestatuslog_set'
        )
    
    @staticmethod
    def optimize_loan_queries():
        """优化借用相关查询"""
        from apps.loans.models import LoanApplication
        
        return LoanApplication.objects.select_related(
            'device', 'borrower', 'approver'
        ).prefetch_related(
            'device__category'
        )
    
    @staticmethod
    def optimize_user_queries():
        """优化用户相关查询"""
        from apps.users.models import User
        
        return User.objects.prefetch_related(
            'owned_devices', 'loan_applications'
        )


# 批量操作优化
class BatchOperationOptimizer:
    """批量操作优化器"""
    
    @staticmethod
    def bulk_update_devices(devices_data: list):
        """批量更新设备"""
        from apps.devices.models import Device
        
        devices_to_update = []
        for device_data in devices_data:
            device = Device.objects.get(id=device_data['id'])
            for field, value in device_data.items():
                if field != 'id':
                    setattr(device, field, value)
            devices_to_update.append(device)
        
        # 批量更新
        Device.objects.bulk_update(
            devices_to_update, 
            fields=[field for field in devices_data[0].keys() if field != 'id']
        )
        
        # 清除相关缓存
        CacheInvalidator.invalidate_related_caches('device')
    
    @staticmethod
    def bulk_create_logs(logs_data: list):
        """批量创建日志"""
        from apps.devices.models import DeviceStatusLog
        
        logs = [DeviceStatusLog(**log_data) for log_data in logs_data]
        DeviceStatusLog.objects.bulk_create(logs)
