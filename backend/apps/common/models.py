"""
通用模型基类
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
import uuid


class BaseModel(models.Model):
    """基础模型类"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        abstract = True


class SoftDeleteModel(BaseModel):
    """软删除模型基类"""
    
    is_deleted = models.BooleanField(_('是否删除'), default=False)
    deleted_at = models.DateTimeField(_('删除时间'), blank=True, null=True)
    
    class Meta:
        abstract = True


class AuditModel(BaseModel):
    """审计模型基类"""
    
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_created',
        verbose_name=_('创建者')
    )
    updated_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)s_updated',
        verbose_name=_('更新者')
    )
    
    class Meta:
        abstract = True


class StatusChoices(models.TextChoices):
    """通用状态选择"""
    ACTIVE = 'active', _('激活')
    INACTIVE = 'inactive', _('未激活')
    PENDING = 'pending', _('待处理')
    APPROVED = 'approved', _('已批准')
    REJECTED = 'rejected', _('已拒绝')
    CANCELLED = 'cancelled', _('已取消')


class PriorityChoices(models.TextChoices):
    """优先级选择"""
    LOW = 'low', _('低')
    NORMAL = 'normal', _('普通')
    HIGH = 'high', _('高')
    URGENT = 'urgent', _('紧急')
