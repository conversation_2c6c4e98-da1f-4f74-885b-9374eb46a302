"""
设备相关视图
"""
from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter
from apps.common.permissions import (
    IsAdminUser, IsDeviceOwnerOrAdmin, DeviceStatusPermission,
    DeviceAssignPermission, DevicePermissionService
), CanManageDevice
from .models import Device, DeviceCategory, DeviceStatusLog
from .serializers import (
    DeviceListSerializer, DeviceDetailSerializer, DeviceCreateUpdateSerializer,
    DeviceStatusChangeSerializer, DeviceStatusLogSerializer, DeviceCategorySerializer,
    DeviceAssignOwnerSerializer, DeviceBatchOperationSerializer
)
from .constants import (
    get_brand_options, get_category_options, get_special_screen_options,
    get_os_options, get_status_options
)


class DeviceListCreateView(generics.ListCreateAPIView):
    """设备列表和创建视图"""

    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['status', 'owner', 'category']
    search_fields = ['name', 'model', 'serial_number', 'brand', 'device_number']
    ordering_fields = ['created_at', 'name', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Device.objects.filter(is_deleted=False)

        # 根据用户权限过滤
        if not self.request.user.is_device_admin:
            # 普通用户只能看到可借用的设备和自己借用的设备
            if self.request.user.role == 'normal_user':
                queryset = queryset.filter(
                    Q(status='available') | Q(current_user=self.request.user)
                )
            # 设备归属者可以看到自己管理的设备
            elif self.request.user.role == 'device_owner':
                queryset = queryset.filter(
                    Q(owner=self.request.user) | Q(status='available') | Q(current_user=self.request.user)
                )

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DeviceCreateUpdateSerializer
        return DeviceListSerializer

    @extend_schema(
        summary="获取设备列表",
        description="获取设备列表，支持搜索和筛选",
        tags=["设备管理"],
        parameters=[
            OpenApiParameter(name='status', description='设备状态筛选'),
            OpenApiParameter(name='owner', description='归属者筛选'),
            OpenApiParameter(name='category', description='分类筛选'),
            OpenApiParameter(name='search', description='搜索关键词'),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="创建设备",
        description="创建新设备（需要管理员权限）",
        tags=["设备管理"]
    )
    def post(self, request, *args, **kwargs):
        if not request.user.is_device_admin:
            return Response(
                {"error": "只有管理员可以创建设备"},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().post(request, *args, **kwargs)


class DeviceDetailView(generics.RetrieveUpdateDestroyAPIView):
    """设备详情视图"""

    queryset = Device.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return DeviceCreateUpdateSerializer
        return DeviceDetailSerializer

    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            return [CanManageDevice()]
        return [permissions.IsAuthenticated()]

    @extend_schema(
        summary="获取设备详情",
        description="获取指定设备的详细信息",
        tags=["设备管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="更新设备信息",
        description="更新设备信息（需要管理权限）",
        tags=["设备管理"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    @extend_schema(
        summary="删除设备",
        description="软删除设备（需要管理权限）",
        tags=["设备管理"]
    )
    def delete(self, request, *args, **kwargs):
        device = self.get_object()
        device.is_deleted = True
        device.deleted_at = timezone.now()
        device.save()
        return Response({"message": "设备已删除"})


class DeviceStatusChangeView(APIView):
    """设备状态变更视图"""

    permission_classes = [CanManageDevice]

    @extend_schema(
        summary="变更设备状态",
        description="变更设备状态（需要管理权限）",
        tags=["设备管理"],
        request=DeviceStatusChangeSerializer
    )
    def post(self, request, pk):
        try:
            device = Device.objects.get(pk=pk, is_deleted=False)
        except Device.DoesNotExist:
            return Response(
                {"error": "设备不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 检查权限
        if not request.user.can_manage_device(device):
            return Response(
                {"error": "没有权限管理此设备"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = DeviceStatusChangeSerializer(
            data=request.data,
            context={'device': device}
        )

        if serializer.is_valid():
            old_status = device.status
            new_status = serializer.validated_data['new_status']
            reason = serializer.validated_data.get('reason', '')

            # 更新设备状态
            device.status = new_status
            if new_status != 'borrowed':
                device.current_user = None
            device.save()

            # 记录状态变更日志
            DeviceStatusLog.objects.create(
                device=device,
                old_status=old_status,
                new_status=new_status,
                operator=request.user,
                reason=reason
            )

            return Response({
                "message": "设备状态已更新",
                "old_status": old_status,
                "new_status": new_status
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeviceAssignOwnerView(APIView):
    """设备分配归属者视图"""

    permission_classes = [DeviceAssignPermission]

    @extend_schema(
        summary="分配设备归属者",
        description="为设备分配归属者（需要管理员权限）",
        tags=["设备管理"],
        request=DeviceAssignOwnerSerializer
    )
    def post(self, request, pk):
        try:
            device = Device.objects.get(pk=pk, is_deleted=False)
        except Device.DoesNotExist:
            return Response(
                {"error": "设备不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = DeviceAssignOwnerSerializer(data=request.data)

        if serializer.is_valid():
            from django.contrib.auth import get_user_model
            User = get_user_model()

            owner_id = serializer.validated_data['owner_id']
            reason = serializer.validated_data.get('reason', '')

            try:
                new_owner = User.objects.get(id=owner_id)
            except User.DoesNotExist:
                return Response(
                    {"error": "指定用户不存在"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 权限检查
            can_assign, message = DevicePermissionService.can_assign_device(
                request.user, device, new_owner
            )

            if not can_assign:
                return Response(
                    {"error": message},
                    status=status.HTTP_403_FORBIDDEN
                )

            old_owner = device.owner

            # 使用状态机进行分配
            try:
                device.assign_to_owner(new_owner)
                device.save()
            except Exception as e:
                return Response(
                    {"error": f"设备分配失败: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 记录状态变更日志
            DeviceStatusLog.objects.create(
                device=device,
                old_status=device.status,
                new_status=device.status,
                operator=request.user,
                reason=f"分配归属者: {old_owner.username if old_owner else '无'} -> {new_owner.username}. {reason}"
            )

            return Response({
                "message": "设备归属者已更新",
                "old_owner": old_owner.username if old_owner else None,
                "new_owner": new_owner.username
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeviceBatchOperationView(APIView):
    """设备批量操作视图"""

    permission_classes = [IsAdminUser]

    @extend_schema(
        summary="设备批量操作",
        description="批量操作设备（分配归属者、变更状态、删除等）",
        tags=["设备管理"],
        request=DeviceBatchOperationSerializer
    )
    def post(self, request):
        serializer = DeviceBatchOperationSerializer(data=request.data)

        if serializer.is_valid():
            device_ids = serializer.validated_data['device_ids']
            operation = serializer.validated_data['operation']
            reason = serializer.validated_data.get('reason', '')

            devices = Device.objects.filter(id__in=device_ids, is_deleted=False)
            results = []

            if operation == 'assign_owner':
                from django.contrib.auth import get_user_model
                User = get_user_model()

                owner_id = serializer.validated_data['owner_id']
                new_owner = User.objects.get(id=owner_id)

                for device in devices:
                    old_owner = device.owner
                    device.owner = new_owner
                    device.save()

                    DeviceStatusLog.objects.create(
                        device=device,
                        old_status=device.status,
                        new_status=device.status,
                        operator=request.user,
                        reason=f"批量分配归属者: {old_owner.username if old_owner else '无'} -> {new_owner.username}. {reason}"
                    )

                    results.append({
                        "device_id": device.id,
                        "device_name": device.name,
                        "status": "success"
                    })

            elif operation == 'change_status':
                new_status = serializer.validated_data['new_status']

                for device in devices:
                    old_status = device.status
                    device.status = new_status
                    if new_status != 'borrowed':
                        device.current_user = None
                    device.save()

                    DeviceStatusLog.objects.create(
                        device=device,
                        old_status=old_status,
                        new_status=new_status,
                        operator=request.user,
                        reason=f"批量状态变更. {reason}"
                    )

                    results.append({
                        "device_id": device.id,
                        "device_name": device.name,
                        "status": "success"
                    })

            elif operation == 'delete':
                for device in devices:
                    device.is_deleted = True
                    device.deleted_at = timezone.now()
                    device.save()

                    results.append({
                        "device_id": device.id,
                        "device_name": device.name,
                        "status": "success"
                    })

            return Response({
                "message": f"批量操作完成，共处理 {len(results)} 台设备",
                "results": results
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeviceStatusLogView(generics.ListAPIView):
    """设备状态日志视图"""

    serializer_class = DeviceStatusLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['device', 'operator']
    ordering = ['-created_at']

    def get_queryset(self):
        device_id = self.kwargs.get('device_id')
        if device_id:
            return DeviceStatusLog.objects.filter(device_id=device_id)

        # 根据用户权限过滤
        if self.request.user.is_device_admin:
            return DeviceStatusLog.objects.all()
        else:
            # 普通用户只能看到自己管理设备的日志
            managed_devices = self.request.user.get_managed_devices()
            return DeviceStatusLog.objects.filter(device__in=managed_devices)

    @extend_schema(
        summary="获取设备状态日志",
        description="获取设备状态变更日志",
        tags=["设备管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class DeviceCategoryListCreateView(generics.ListCreateAPIView):
    """设备分类列表和创建视图"""

    queryset = DeviceCategory.objects.all()
    serializer_class = DeviceCategorySerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['name']

    def get_permissions(self):
        if self.request.method == 'POST':
            return [IsAdminUser()]
        return [permissions.IsAuthenticated()]

    @extend_schema(
        summary="获取设备分类列表",
        description="获取所有设备分类",
        tags=["设备管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="创建设备分类",
        description="创建新的设备分类（需要管理员权限）",
        tags=["设备管理"]
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class DeviceCategoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """设备分类详情视图"""

    queryset = DeviceCategory.objects.all()
    serializer_class = DeviceCategorySerializer
    permission_classes = [IsAdminUser]

    @extend_schema(
        summary="获取设备分类详情",
        description="获取指定设备分类的详细信息",
        tags=["设备管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="更新设备分类",
        description="更新设备分类信息",
        tags=["设备管理"]
    )
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    @extend_schema(
        summary="删除设备分类",
        description="删除设备分类",
        tags=["设备管理"]
    )
    def delete(self, request, *args, **kwargs):
        return super().delete(request, *args, **kwargs)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_import_devices(request):
    """批量导入设备"""
    from django.contrib.auth import get_user_model

    User = get_user_model()

    try:
        devices_data = request.data.get('devices', [])

        if not devices_data:
            return Response(
                {"error": "没有提供设备数据"},
                status=status.HTTP_400_BAD_REQUEST
            )

        imported_devices = []
        errors = []

        with transaction.atomic():
            for index, device_data in enumerate(devices_data):
                try:
                    # 处理归属者
                    owner = None
                    if device_data.get('owner'):
                        try:
                            owner = User.objects.get(username=device_data['owner'])
                        except User.DoesNotExist:
                            errors.append(f"第{index+1}行: 用户 '{device_data['owner']}' 不存在")
                            continue

                    # 处理设备分类
                    category = None
                    if device_data.get('category'):
                        try:
                            category = DeviceCategory.objects.get(name=device_data['category'])
                        except DeviceCategory.DoesNotExist:
                            # 如果分类不存在，创建新分类
                            category = DeviceCategory.objects.create(
                                name=device_data['category'],
                                description=f"自动创建的分类: {device_data['category']}"
                            )

                    # 检查序列号唯一性
                    if Device.objects.filter(serial_number=device_data.get('serial_number')).exists():
                        errors.append(f"第{index+1}行: 序列号 '{device_data.get('serial_number')}' 已存在")
                        continue

                    # 创建设备数据
                    device_create_data = {
                        'name': device_data.get('name'),
                        'model': device_data.get('model'),
                        'serial_number': device_data.get('serial_number'),
                        'brand': device_data.get('brand'),
                        'status': device_data.get('status', 'in_stock'),
                        'category': category,
                        'owner': owner,
                        'cpu': device_data.get('cpu', ''),
                        'gpu': device_data.get('gpu', ''),
                        'memory': device_data.get('memory', ''),
                        'storage': device_data.get('storage', ''),
                        'resolution': device_data.get('resolution', ''),
                        'screen_size': device_data.get('screen_size', ''),
                        'special_screen': device_data.get('special_screen', ''),
                        'os': device_data.get('os', ''),
                        'os_version': device_data.get('os_version', ''),
                        'purchase_price': device_data.get('purchase_price'),
                        'purchase_date': device_data.get('purchase_date'),
                        'warranty_period': device_data.get('warranty_period', ''),
                        'special_notes': device_data.get('special_notes', ''),
                    }

                    # 验证必填字段
                    required_fields = ['name', 'model', 'serial_number', 'brand']
                    missing_fields = [field for field in required_fields if not device_create_data.get(field)]
                    if missing_fields:
                        errors.append(f"第{index+1}行: 缺少必填字段 {', '.join(missing_fields)}")
                        continue

                    # 创建设备
                    device = Device.objects.create(**device_create_data)
                    imported_devices.append(device)

                except Exception as e:
                    errors.append(f"第{index+1}行: {str(e)}")

            # 如果有错误，回滚事务
            if errors:
                transaction.set_rollback(True)
                return Response(
                    {
                        "error": "导入失败，请修正以下错误后重新导入",
                        "errors": errors
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(
            {
                "message": f"成功导入 {len(imported_devices)} 条设备数据",
                "imported_count": len(imported_devices)
            },
            status=status.HTTP_201_CREATED
        )

    except Exception as e:
        return Response(
            {"error": f"导入过程中发生错误: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def device_config_options(request):
    """获取设备配置选项"""
    return Response({
        'brands': get_brand_options(),
        'categories': get_category_options(),
        'special_screens': get_special_screen_options(),
        'operating_systems': get_os_options(),
        'statuses': get_status_options()
    })


class DeviceStatusChangeView(APIView):
    """设备状态变更视图"""

    permission_classes = [DeviceStatusPermission]

    @extend_schema(
        summary="修改设备状态",
        description="根据权限规则修改设备状态",
        tags=["设备管理"],
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'status': {'type': 'string', 'description': '目标状态'},
                    'reason': {'type': 'string', 'description': '操作原因'}
                },
                'required': ['status']
            }
        }
    )
    def post(self, request, pk):
        try:
            device = Device.objects.get(pk=pk, is_deleted=False)
        except Device.DoesNotExist:
            return Response(
                {"error": "设备不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        target_status = request.data.get('status')
        reason = request.data.get('reason', '')

        if not target_status:
            return Response(
                {"error": "请提供目标状态"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 权限检查
        can_change, message = DevicePermissionService.can_change_device_status(
            request.user, device, target_status
        )

        if not can_change:
            return Response(
                {"error": message},
                status=status.HTTP_403_FORBIDDEN
            )

        # 执行状态转换
        old_status = device.status
        old_status_display = device.get_status_display()

        try:
            # 根据目标状态调用相应的状态机方法
            if target_status == 'assigned' and old_status == 'in_stock':
                # 这个转换应该通过分配API处理
                return Response(
                    {"error": "请使用设备分配API"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            elif target_status == 'available' and old_status == 'assigned':
                device.make_available()
            elif target_status == 'locked' and old_status in ['assigned', 'available']:
                device.make_locked()
            elif target_status == 'assigned' and old_status in ['available', 'locked']:
                device.make_assigned()
            elif target_status == 'in_stock' and old_status in ['assigned', 'locked']:
                device.warehouse_in()
            elif target_status == 'maintenance' and old_status in ['assigned', 'available', 'locked']:
                device.start_maintenance()
            elif target_status == 'in_stock' and old_status == 'maintenance':
                device.finish_maintenance()
            elif target_status == 'scrapped':
                device.mark_as_scrapped()
            elif target_status == 'lost':
                device.mark_as_lost()
            else:
                return Response(
                    {"error": f"不支持从{old_status_display}到{dict(device.STATUS_CHOICES).get(target_status, target_status)}的状态转换"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            device.save()

            # 记录状态变更日志
            DeviceStatusLog.objects.create(
                device=device,
                old_status=old_status,
                new_status=target_status,
                operator=request.user,
                reason=reason or f"状态变更: {old_status_display} → {device.get_status_display()}"
            )

            return Response({
                "message": f"设备状态已从{old_status_display}修改为{device.get_status_display()}",
                "old_status": old_status,
                "new_status": target_status,
                "device_id": device.id
            })

        except Exception as e:
            return Response(
                {"error": f"状态转换失败: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )


class DeviceWarehouseInView(APIView):
    """设备入库视图"""

    permission_classes = [IsDeviceOwnerOrAdmin]

    @extend_schema(
        summary="设备入库",
        description="将设备入库，可重新分配给其他归属者",
        tags=["设备管理"],
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'reason': {'type': 'string', 'description': '入库原因'}
                }
            }
        }
    )
    def post(self, request, pk):
        try:
            device = Device.objects.get(pk=pk, is_deleted=False)
        except Device.DoesNotExist:
            return Response(
                {"error": "设备不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        reason = request.data.get('reason', '')

        # 权限检查
        can_warehouse, message = DevicePermissionService.can_warehouse_in_device(
            request.user, device
        )

        if not can_warehouse:
            return Response(
                {"error": message},
                status=status.HTTP_403_FORBIDDEN
            )

        # 执行入库操作
        old_status = device.status
        old_status_display = device.get_status_display()
        old_owner = device.owner

        try:
            device.warehouse_in()
            device.save()

            # 记录状态变更日志
            DeviceStatusLog.objects.create(
                device=device,
                old_status=old_status,
                new_status='in_stock',
                operator=request.user,
                reason=reason or f"设备入库: {old_owner.username if old_owner else '无归属者'} → 库存中"
            )

            return Response({
                "message": f"设备已从{old_status_display}状态入库，可重新分配",
                "old_status": old_status,
                "new_status": "in_stock",
                "device_id": device.id
            })

        except Exception as e:
            return Response(
                {"error": f"入库操作失败: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
