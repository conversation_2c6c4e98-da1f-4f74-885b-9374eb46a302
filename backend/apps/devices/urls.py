"""
设备相关URL配置
"""
from django.urls import path
from . import views

app_name = 'devices'

urlpatterns = [
    # 设备管理
    path('', views.DeviceListCreateView.as_view(), name='device_list_create'),
    path('<uuid:pk>/', views.DeviceDetailView.as_view(), name='device_detail'),
    path('<uuid:pk>/change-status/', views.DeviceStatusChangeView.as_view(), name='device_change_status'),
    path('<uuid:pk>/assign-owner/', views.DeviceAssignOwnerView.as_view(), name='device_assign_owner'),
    path('<uuid:device_id>/status-logs/', views.DeviceStatusLogView.as_view(), name='device_status_logs'),

    # 批量操作
    path('batch-operation/', views.DeviceBatchOperationView.as_view(), name='device_batch_operation'),
    path('batch-import/', views.batch_import_devices, name='device_batch_import'),

    # 状态日志
    path('status-logs/', views.DeviceStatusLogView.as_view(), name='all_status_logs'),

    # 设备分类
    path('categories/', views.DeviceCategoryListCreateView.as_view(), name='category_list_create'),
    path('categories/<uuid:pk>/', views.DeviceCategoryDetailView.as_view(), name='category_detail'),

    # 配置选项
    path('config-options/', views.device_config_options, name='device_config_options'),
]
