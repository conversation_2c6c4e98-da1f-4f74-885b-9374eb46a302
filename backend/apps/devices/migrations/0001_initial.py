# Generated by Django 4.2.7 on 2025-07-27 12:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
                ('name', models.Char<PERSON>ield(max_length=200, verbose_name='设备名称')),
                ('model', models.CharField(max_length=100, verbose_name='设备型号')),
                ('serial_number', models.CharField(max_length=100, unique=True, verbose_name='序列号')),
                ('brand', models.CharField(max_length=100, verbose_name='品牌')),
                ('device_number', models.CharField(blank=True, max_length=50, unique=True, verbose_name='设备编号')),
                ('cpu', models.CharField(blank=True, max_length=200, verbose_name='CPU')),
                ('gpu', models.CharField(blank=True, max_length=200, verbose_name='GPU')),
                ('memory', models.CharField(blank=True, max_length=100, verbose_name='内存')),
                ('storage', models.CharField(blank=True, max_length=100, verbose_name='存储')),
                ('resolution', models.CharField(blank=True, max_length=50, verbose_name='分辨率')),
                ('screen_size', models.CharField(blank=True, max_length=50, verbose_name='屏幕尺寸')),
                ('special_screen', models.CharField(blank=True, max_length=100, verbose_name='特殊屏幕')),
                ('os', models.CharField(blank=True, max_length=100, verbose_name='操作系统')),
                ('os_version', models.CharField(blank=True, max_length=100, verbose_name='系统版本')),
                ('status', django_fsm.FSMField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('idle', '闲置'), ('locked', '锁定'), ('borrowed', '借出中'), ('maintenance', '维修中'), ('lost', '丢失'), ('scrapped', '报废')], default='in_stock', max_length=20, verbose_name='设备状态')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='采购价格')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='采购日期')),
                ('warranty_period', models.CharField(blank=True, max_length=100, verbose_name='保修期')),
                ('special_notes', models.TextField(blank=True, verbose_name='特殊说明')),
                ('image', models.ImageField(blank=True, null=True, upload_to='device_images/', verbose_name='设备图片')),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='qr_codes/', verbose_name='二维码')),
            ],
            options={
                'verbose_name': '设备',
                'verbose_name_plural': '设备',
                'db_table': 'devices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceStatusLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('old_status', models.CharField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('idle', '闲置'), ('locked', '锁定'), ('borrowed', '借出中'), ('maintenance', '维修中'), ('lost', '丢失'), ('scrapped', '报废')], max_length=20, verbose_name='原状态')),
                ('new_status', models.CharField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('idle', '闲置'), ('locked', '锁定'), ('borrowed', '借出中'), ('maintenance', '维修中'), ('lost', '丢失'), ('scrapped', '报废')], max_length=20, verbose_name='新状态')),
                ('reason', models.TextField(blank=True, verbose_name='变更原因')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='devices.device', verbose_name='设备')),
                ('operator', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
            ],
            options={
                'verbose_name': '设备状态日志',
                'verbose_name_plural': '设备状态日志',
                'db_table': 'device_status_logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='devices.devicecategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '设备分类',
                'verbose_name_plural': '设备分类',
                'db_table': 'device_categories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='device',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='devices.devicecategory', verbose_name='设备分类'),
        ),
        migrations.AddField(
            model_name='device',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddField(
            model_name='device',
            name='current_user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='borrowed_devices', to=settings.AUTH_USER_MODEL, verbose_name='当前使用人'),
        ),
        migrations.AddField(
            model_name='device',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_devices', to=settings.AUTH_USER_MODEL, verbose_name='设备归属者'),
        ),
        migrations.AddField(
            model_name='device',
            name='updated_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['status'], name='devices_status_9f14cf_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['owner'], name='devices_owner_i_508ed1_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['serial_number'], name='devices_serial__9114fa_idx'),
        ),
        migrations.AddIndex(
            model_name='device',
            index=models.Index(fields=['is_deleted', 'deleted_at'], name='devices_is_dele_9c46b7_idx'),
        ),
    ]
