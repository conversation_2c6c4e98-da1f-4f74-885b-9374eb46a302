# Generated by Django 4.2.7 on 2025-08-01 08:02

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('devices', '0002_alter_device_warranty_period'),
    ]

    operations = [
        migrations.AlterField(
            model_name='device',
            name='brand',
            field=models.CharField(choices=[('HUAWEI', 'HUAWEI'), ('<PERSON><PERSON>', '<PERSON><PERSON>'), ('OPPO', 'OPPO'), ('VIVO', 'VIVO'), ('<PERSON><PERSON>', '<PERSON><PERSON>'), ('SAMSUNG', 'SAMSUNG'), ('HONOR', 'HONOR'), ('OnePlus', 'OnePlus'), ('Google', 'Google'), ('Apple', 'Apple'), ('realme', 'realme'), ('ZTE', 'ZTE'), ('MEIZU', 'MEIZU'), ('SONY', 'SONY'), ('Asus', 'Asus'), ('nubia', 'nubia'), ('<PERSON>to', 'Moto'), ('Other', 'Other')], default='HUAWEI', max_length=100, verbose_name='品牌'),
        ),
        migrations.AlterField(
            model_name='device',
            name='category',
            field=models.CharField(blank=True, choices=[('手机', '手机'), ('平板', '平板'), ('笔记本', '笔记本'), ('PC', 'PC'), ('其它', '其它')], max_length=100, null=True, verbose_name='设备分类'),
        ),
        migrations.AlterField(
            model_name='device',
            name='os',
            field=models.CharField(blank=True, choices=[('Android', 'Android'), ('iOS', 'iOS'), ('HarmonyOS', 'HarmonyOS'), ('HarmonyOSNext', 'HarmonyOSNext')], max_length=100, verbose_name='操作系统'),
        ),
        migrations.AlterField(
            model_name='device',
            name='special_screen',
            field=models.CharField(blank=True, choices=[('刘海屏', '刘海屏'), ('水滴屏', '水滴屏'), ('挖孔屏', '挖孔屏'), ('瀑布屏', '瀑布屏'), ('曲面屏', '曲面屏'), ('真全面屏', '真全面屏'), ('内折屏', '内折屏'), ('外折屏', '外折屏'), ('上下折叠屏', '上下折叠屏'), ('三折屏', '三折屏'), ('环绕屏', '环绕屏'), ('滑盖全面屏', '滑盖全面屏')], max_length=100, verbose_name='特殊屏幕'),
        ),
        migrations.AlterField(
            model_name='device',
            name='status',
            field=django_fsm.FSMField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('borrowed', '已借出'), ('maintenance', '维修中'), ('retired', '已报废'), ('locked', '已锁定')], default='in_stock', max_length=20, verbose_name='设备状态'),
        ),
        migrations.AlterField(
            model_name='devicestatuslog',
            name='new_status',
            field=models.CharField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('borrowed', '已借出'), ('maintenance', '维修中'), ('retired', '已报废'), ('locked', '已锁定')], max_length=20, verbose_name='新状态'),
        ),
        migrations.AlterField(
            model_name='devicestatuslog',
            name='old_status',
            field=models.CharField(choices=[('in_stock', '库存中'), ('available', '可借用'), ('borrowed', '已借出'), ('maintenance', '维修中'), ('retired', '已报废'), ('locked', '已锁定')], max_length=20, verbose_name='原状态'),
        ),
    ]
