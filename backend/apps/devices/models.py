"""
设备管理模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_fsm import FSMField, transition
from apps.common.models import BaseModel, SoftDeleteModel, AuditModel
from .constants import (
    BRAND_CHOICES, CATEGORY_CHOICES, SPECIAL_SCREEN_CHOICES,
    OS_CHOICES, STATUS_CHOICES, get_default_brand, get_default_os
)
import qrcode
from io import BytesIO
from django.core.files import File
from PIL import Image


class DeviceCategory(BaseModel):
    """设备分类"""
    
    name = models.CharField(_('分类名称'), max_length=100, unique=True)
    description = models.TextField(_('描述'), blank=True)
    parent = models.ForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True,
        related_name='children',
        verbose_name=_('父分类')
    )
    
    class Meta:
        db_table = 'device_categories'
        verbose_name = _('设备分类')
        verbose_name_plural = _('设备分类')
        ordering = ['name']
    
    def __str__(self):
        return self.name


class Device(SoftDeleteModel, AuditModel):
    """设备模型"""
    
    # 基础信息
    name = models.CharField(_('设备名称'), max_length=200)
    model = models.CharField(_('设备型号'), max_length=100)
    serial_number = models.CharField(_('序列号'), max_length=100, unique=True)
    brand = models.CharField(_('品牌'), max_length=100, choices=BRAND_CHOICES, default='HUAWEI')
    device_number = models.CharField(_('设备编号'), max_length=50, unique=True, blank=True)
    category = models.CharField(_('设备分类'), max_length=100, choices=CATEGORY_CHOICES, blank=True, null=True)
    
    # 技术规格
    cpu = models.CharField(_('CPU'), max_length=200, blank=True)
    gpu = models.CharField(_('GPU'), max_length=200, blank=True)
    memory = models.CharField(_('内存'), max_length=100, blank=True)
    storage = models.CharField(_('存储'), max_length=100, blank=True)
    resolution = models.CharField(_('分辨率'), max_length=50, blank=True)
    screen_size = models.CharField(_('屏幕尺寸'), max_length=50, blank=True)
    special_screen = models.CharField(_('特殊屏幕'), max_length=100, choices=SPECIAL_SCREEN_CHOICES, blank=True)
    
    # 系统信息
    os = models.CharField(_('操作系统'), max_length=100, choices=OS_CHOICES, blank=True)
    os_version = models.CharField(_('系统版本'), max_length=100, blank=True)
    
    # 管理信息
    status = FSMField(
        _('设备状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='in_stock'
    )
    owner = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='owned_devices',
        verbose_name=_('设备归属者')
    )
    current_user = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='borrowed_devices',
        verbose_name=_('当前使用人')
    )
    
    # 采购信息
    purchase_price = models.DecimalField(_('采购价格'), max_digits=10, decimal_places=2, null=True, blank=True)
    purchase_date = models.DateField(_('采购日期'), null=True, blank=True)
    warranty_period = models.CharField(_('保修期'), max_length=100, blank=True, null=True)
    
    # 其他信息
    special_notes = models.TextField(_('特殊说明'), blank=True)
    image = models.ImageField(_('设备图片'), upload_to='device_images/', blank=True, null=True)
    qr_code = models.ImageField(_('二维码'), upload_to='qr_codes/', blank=True, null=True)
    
    class Meta:
        db_table = 'devices'
        verbose_name = _('设备')
        verbose_name_plural = _('设备')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['owner']),
            models.Index(fields=['serial_number']),
            models.Index(fields=['is_deleted', 'deleted_at']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.model})"
    
    def save(self, *args, **kwargs):
        # 自动生成设备编号
        if not self.device_number:
            self.device_number = self.generate_device_number()
        
        super().save(*args, **kwargs)
        
        # 生成二维码
        if not self.qr_code:
            self.generate_qr_code()
    
    def generate_device_number(self):
        """生成设备编号 - 自然数递增"""
        from django.db import transaction

        # 使用数据库锁确保线程安全
        with transaction.atomic():
            # 获取当前最大的设备编号
            existing_devices = Device.objects.exclude(
                device_number__isnull=True
            ).exclude(
                device_number__exact=''
            ).order_by('-id')

            if existing_devices.exists():
                # 获取最大的数字编号
                max_number = 0
                for device in existing_devices:
                    try:
                        # 尝试提取数字部分
                        number_str = device.device_number
                        # 如果是纯数字，直接转换
                        if number_str.isdigit():
                            max_number = max(max_number, int(number_str))
                        # 如果包含字母，尝试提取数字部分
                        else:
                            import re
                            numbers = re.findall(r'\d+', number_str)
                            if numbers:
                                max_number = max(max_number, int(numbers[-1]))
                    except (ValueError, IndexError):
                        continue

                next_number = max_number + 1
            else:
                next_number = 1

            # 生成新编号，如果冲突则递增重试
            max_attempts = 100
            for attempt in range(max_attempts):
                device_number = str(next_number + attempt)
                if not Device.objects.filter(device_number=device_number).exists():
                    return device_number

            # 如果所有尝试都失败，使用当前时间戳
            import time
            return str(int(time.time()))
    
    def generate_qr_code(self):
        """生成二维码"""
        qr_data = {
            'id': str(self.id),
            'name': self.name,
            'serial_number': self.serial_number,
            'device_number': self.device_number
        }
        
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_M,
            box_size=10,
            border=4,
        )
        qr.add_data(str(qr_data))
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        
        filename = f'qr_{self.device_number}.png'
        self.qr_code.save(filename, File(buffer), save=False)
        buffer.close()
        
        # 保存但不触发递归
        Device.objects.filter(id=self.id).update(qr_code=self.qr_code)
    
    # 状态机转换方法
    @transition(field=status, source='in_stock', target='available')
    def make_available(self):
        """设为可借用"""
        pass
    
    @transition(field=status, source='in_stock', target='idle')
    def make_idle(self):
        """设为闲置"""
        pass
    
    @transition(field=status, source='in_stock', target='locked')
    def make_locked(self):
        """设为锁定"""
        pass
    
    @transition(field=status, source=['available', 'idle', 'locked'], target='borrowed')
    def borrow(self, user):
        """借出设备"""
        self.current_user = user
    
    @transition(field=status, source='borrowed', target='available')
    def return_to_available(self):
        """归还为可借用"""
        self.current_user = None
    
    @transition(field=status, source='borrowed', target='idle')
    def return_to_idle(self):
        """归还为闲置"""
        self.current_user = None
    
    @transition(field=status, source='borrowed', target='locked')
    def return_to_locked(self):
        """归还为锁定"""
        self.current_user = None
    
    @transition(field=status, source=['available', 'idle', 'locked'], target='maintenance')
    def start_maintenance(self):
        """开始维修"""
        pass
    
    @transition(field=status, source='maintenance', target='available')
    def finish_maintenance_to_available(self):
        """维修完成，设为可借用"""
        pass
    
    @transition(field=status, source='maintenance', target='idle')
    def finish_maintenance_to_idle(self):
        """维修完成，设为闲置"""
        pass
    
    @transition(field=status, source='maintenance', target='locked')
    def finish_maintenance_to_locked(self):
        """维修完成，设为锁定"""
        pass
    
    @transition(field=status, source=['available', 'idle', 'locked', 'borrowed', 'maintenance'], target='lost')
    def mark_as_lost(self):
        """标记为丢失"""
        self.current_user = None
    
    @transition(field=status, source=['available', 'idle', 'locked', 'maintenance'], target='scrapped')
    def mark_as_scrapped(self):
        """标记为报废"""
        self.current_user = None
    
    @property
    def is_available_for_borrow(self):
        """是否可以借用"""
        return self.status == 'available' and not self.is_deleted
    
    @property
    def can_be_managed_by(self, user):
        """是否可以被指定用户管理"""
        return user.can_manage_device(self)


class DeviceStatusLog(BaseModel):
    """设备状态变更日志"""
    
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='status_logs',
        verbose_name=_('设备')
    )
    old_status = models.CharField(_('原状态'), max_length=20, choices=STATUS_CHOICES)
    new_status = models.CharField(_('新状态'), max_length=20, choices=STATUS_CHOICES)
    operator = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('操作人')
    )
    reason = models.TextField(_('变更原因'), blank=True)
    
    class Meta:
        db_table = 'device_status_logs'
        verbose_name = _('设备状态日志')
        verbose_name_plural = _('设备状态日志')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.device.name}: {self.old_status} -> {self.new_status}"
