"""
设备相关序列化器
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Device, DeviceCategory, DeviceStatusLog
from .constants import STATUS_CHOICES

User = get_user_model()


class DeviceCategorySerializer(serializers.ModelSerializer):
    """设备分类序列化器"""
    
    class Meta:
        model = DeviceCategory
        fields = ['id', 'name', 'description', 'parent', 'created_at', 'updated_at']


class DeviceListSerializer(serializers.ModelSerializer):
    """设备列表序列化器 - 优化显示字段"""

    owner_info = serializers.SerializerMethodField()
    current_user_info = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = Device
        fields = [
            'id', 'device_number', 'name', 'model', 'os_version', 'memory',
            'serial_number', 'status', 'status_display', 'owner_info', 'current_user_info'
        ]

    def get_owner_info(self, obj):
        """获取归属者信息"""
        if obj.owner:
            department = getattr(obj.owner, 'department', None)
            department_name = department.name if department else '无部门'
            return f"{obj.owner.username} ({department_name})"
        return "未分配"

    def get_current_user_info(self, obj):
        """获取当前使用人信息"""
        if obj.current_user:
            department = getattr(obj.current_user, 'department', None)
            department_name = department.name if department else '无部门'
            return f"{obj.current_user.username} ({department_name})"
        return "无"


class DeviceDetailSerializer(serializers.ModelSerializer):
    """设备详情序列化器"""
    
    owner_info = serializers.SerializerMethodField()
    current_user_info = serializers.SerializerMethodField()
    category_info = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Device
        fields = [
            'id', 'name', 'model', 'serial_number', 'brand', 'device_number',
            'cpu', 'gpu', 'memory', 'storage', 'resolution', 'screen_size', 'special_screen',
            'os', 'os_version', 'status', 'status_display', 'owner', 'owner_info',
            'current_user', 'current_user_info', 'category', 'category_info',
            'purchase_price', 'purchase_date', 'warranty_period', 'special_notes',
            'image', 'qr_code', 'is_deleted', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'device_number', 'qr_code', 'created_at', 'updated_at']
    
    def get_owner_info(self, obj):
        if obj.owner:
            return {
                'id': obj.owner.id,
                'username': obj.owner.username,
                'email': obj.owner.email,
                'department': obj.owner.department
            }
        return None
    
    def get_current_user_info(self, obj):
        if obj.current_user:
            return {
                'id': obj.current_user.id,
                'username': obj.current_user.username,
                'email': obj.current_user.email,
                'department': obj.current_user.department
            }
        return None
    
    def get_category_info(self, obj):
        if obj.category:
            # category现在是字符串字段，不是外键对象
            return {
                'id': obj.category,  # 使用字符串值作为id
                'name': obj.category,  # 显示名称就是字符串值
                'description': f'{obj.category}类设备'  # 生成描述
            }
        return None


class DeviceCreateUpdateSerializer(serializers.ModelSerializer):
    """设备创建/更新序列化器"""

    class Meta:
        model = Device
        fields = [
            'id', 'name', 'model', 'serial_number', 'brand', 'category',
            'cpu', 'gpu', 'memory', 'storage', 'resolution', 'screen_size', 'special_screen',
            'os', 'os_version', 'status', 'owner', 'purchase_price', 'purchase_date',
            'warranty_period', 'special_notes', 'image'
        ]
        read_only_fields = ['id']
    
    def validate_serial_number(self, value):
        """验证序列号唯一性"""
        instance = getattr(self, 'instance', None)
        if Device.objects.filter(serial_number=value).exclude(pk=instance.pk if instance else None).exists():
            raise serializers.ValidationError("序列号已存在")
        return value

    def validate_warranty_period(self, value):
        """验证保修期字段"""
        if value is None:
            return ''  # 将None转换为空字符串
        return value
    
    def create(self, validated_data):
        """创建设备"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新设备"""
        validated_data['updated_by'] = self.context['request'].user
        return super().update(instance, validated_data)


class DeviceStatusChangeSerializer(serializers.Serializer):
    """设备状态变更序列化器"""
    
    new_status = serializers.ChoiceField(choices=STATUS_CHOICES)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate(self, attrs):
        device = self.context['device']
        new_status = attrs['new_status']
        
        # 检查状态转换是否合法
        valid_transitions = {
            'in_stock': ['available', 'idle', 'locked'],
            'available': ['borrowed', 'idle', 'locked', 'maintenance'],
            'idle': ['available', 'locked', 'maintenance'],
            'locked': ['available', 'idle', 'maintenance'],
            'borrowed': ['available', 'idle', 'locked'],
            'maintenance': ['available', 'idle', 'locked'],
            'lost': [],
            'scrapped': []
        }
        
        if new_status not in valid_transitions.get(device.status, []):
            raise serializers.ValidationError(f"不能从 {device.get_status_display()} 转换到 {dict(STATUS_CHOICES)[new_status]}")
        
        return attrs


class DeviceStatusLogSerializer(serializers.ModelSerializer):
    """设备状态日志序列化器"""
    
    device_name = serializers.CharField(source='device.name', read_only=True)
    operator_name = serializers.CharField(source='operator.username', read_only=True)
    old_status_display = serializers.SerializerMethodField()
    new_status_display = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceStatusLog
        fields = [
            'id', 'device', 'device_name', 'old_status', 'old_status_display',
            'new_status', 'new_status_display', 'operator', 'operator_name',
            'reason', 'created_at'
        ]
    
    def get_old_status_display(self, obj):
        return dict(STATUS_CHOICES).get(obj.old_status, obj.old_status)

    def get_new_status_display(self, obj):
        return dict(STATUS_CHOICES).get(obj.new_status, obj.new_status)


class DeviceAssignOwnerSerializer(serializers.Serializer):
    """设备分配归属者序列化器"""
    
    owner_id = serializers.UUIDField()
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate_owner_id(self, value):
        try:
            user = User.objects.get(id=value)
            if not user.is_device_owner:
                raise serializers.ValidationError("指定用户不具备设备归属者权限")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("指定用户不存在")


class DeviceBatchOperationSerializer(serializers.Serializer):
    """设备批量操作序列化器"""
    
    device_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1,
        max_length=50
    )
    operation = serializers.ChoiceField(choices=[
        ('assign_owner', '分配归属者'),
        ('change_status', '变更状态'),
        ('delete', '删除设备')
    ])
    
    # 可选参数，根据操作类型使用
    owner_id = serializers.UUIDField(required=False)
    new_status = serializers.ChoiceField(choices=STATUS_CHOICES, required=False)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate(self, attrs):
        operation = attrs['operation']
        
        if operation == 'assign_owner' and not attrs.get('owner_id'):
            raise serializers.ValidationError("分配归属者操作需要提供 owner_id")
        
        if operation == 'change_status' and not attrs.get('new_status'):
            raise serializers.ValidationError("变更状态操作需要提供 new_status")
        
        # 验证设备是否存在
        device_ids = attrs['device_ids']
        existing_devices = Device.objects.filter(id__in=device_ids, is_deleted=False)
        if existing_devices.count() != len(device_ids):
            raise serializers.ValidationError("部分设备不存在或已删除")
        
        return attrs
