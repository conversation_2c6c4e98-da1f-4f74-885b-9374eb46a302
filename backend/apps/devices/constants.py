# -*- coding: utf-8 -*-
"""
设备管理常量配置
"""

# 品牌选项
BRAND_CHOICES = [
    ('HUAWEI', 'HUAWEI'),
    ('<PERSON><PERSON>', '<PERSON><PERSON>'),
    ('OPPO', 'OPPO'),
    ('VIVO', 'VIVO'),
    ('<PERSON><PERSON>', '<PERSON><PERSON>'),
    ('SAMSUNG', 'SAMSUNG'),
    ('HONOR', 'HONOR'),
    ('OnePlus', 'OnePlus'),
    ('Google', 'Google'),
    ('Apple', 'Apple'),
    ('realme', 'realme'),
    ('ZTE', 'ZTE'),
    ('MEIZU', 'MEIZU'),
    ('SONY', 'SONY'),
    ('Asus', 'Asus'),
    ('nubia', 'nubia'),
    ('Moto', 'Moto'),
    ('Other', 'Other'),
]

# 设备分类选项
CATEGORY_CHOICES = [
    ('手机', '手机'),
    ('平板', '平板'),
    ('笔记本', '笔记本'),
    ('PC', 'PC'),
    ('其它', '其它'),
]

# 特殊屏幕选项
SPECIAL_SCREEN_CHOICES = [
    ('刘海屏', '刘海屏'),
    ('水滴屏', '水滴屏'),
    ('挖孔屏', '挖孔屏'),
    ('瀑布屏', '瀑布屏'),
    ('曲面屏', '曲面屏'),
    ('真全面屏', '真全面屏'),
    ('内折屏', '内折屏'),
    ('外折屏', '外折屏'),
    ('上下折叠屏', '上下折叠屏'),
    ('三折屏', '三折屏'),
    ('环绕屏', '环绕屏'),
    ('滑盖全面屏', '滑盖全面屏'),
]

# 操作系统选项
OS_CHOICES = [
    ('Android', 'Android'),
    ('iOS', 'iOS'),
    ('HarmonyOS', 'HarmonyOS'),
    ('HarmonyOSNext', 'HarmonyOSNext'),
]

# 设备状态选项 - 与数据库设计文档保持一致
STATUS_CHOICES = [
    ('in_stock', '库存中'),
    ('available', '可借用'),
    ('idle', '闲置'),
    ('locked', '锁定'),
    ('borrowed', '借出中'),
    ('maintenance', '维修中'),
    ('lost', '丢失'),
    ('scrapped', '报废'),
]

# 获取选项值列表（用于前端）
def get_brand_options():
    """获取品牌选项列表"""
    return [{'value': choice[0], 'label': choice[1]} for choice in BRAND_CHOICES]

def get_category_options():
    """获取设备分类选项列表"""
    return [{'value': choice[0], 'label': choice[1]} for choice in CATEGORY_CHOICES]

def get_special_screen_options():
    """获取特殊屏幕选项列表"""
    return [{'value': choice[0], 'label': choice[1]} for choice in SPECIAL_SCREEN_CHOICES]

def get_os_options():
    """获取操作系统选项列表"""
    return [{'value': choice[0], 'label': choice[1]} for choice in OS_CHOICES]

def get_status_options():
    """获取设备状态选项列表"""
    return [{'value': choice[0], 'label': choice[1]} for choice in STATUS_CHOICES]

# 获取默认值（第一个选项）
def get_default_brand():
    """获取默认品牌"""
    return BRAND_CHOICES[0][0] if BRAND_CHOICES else ''

def get_default_category():
    """获取默认设备分类"""
    return CATEGORY_CHOICES[0][0] if CATEGORY_CHOICES else ''

def get_default_special_screen():
    """获取默认特殊屏幕"""
    return SPECIAL_SCREEN_CHOICES[0][0] if SPECIAL_SCREEN_CHOICES else ''

def get_default_os():
    """获取默认操作系统"""
    return OS_CHOICES[0][0] if OS_CHOICES else ''

def get_default_status():
    """获取默认设备状态"""
    return STATUS_CHOICES[0][0] if STATUS_CHOICES else 'in_stock'
