"""
借用管理视图
"""
from rest_framework import generics, status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from django.db.models import Q, Count
from django.utils import timezone
from datetime import datetime, timedelta
from drf_spectacular.utils import extend_schema
from apps.common.permissions import IsAdminUser, IsDeviceOwnerOrAdmin
from .models import LoanApplication, LoanExtension, LoanHistory, LoanReminder
from .serializers import (
    LoanApplicationCreateSerializer, LoanApplicationListSerializer, LoanApplicationDetailSerializer,
    LoanApprovalSerializer, LoanStartSerializer, LoanReturnSerializer,
    LoanExtensionCreateSerializer, LoanExtensionSerializer,
    LoanHistorySerializer, LoanReminderSerializer, LoanStatisticsSerializer
)


class LoanApplicationListCreateView(generics.ListCreateAPIView):
    """借用申请列表和创建视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['status', 'device', 'borrower', 'priority']
    search_fields = ['reason', 'device__name', 'borrower__username']
    ordering_fields = ['created_at', 'expected_start_date', 'priority']
    ordering = ['-created_at']
    
    def get_queryset(self):
        queryset = LoanApplication.objects.all()
        
        # 根据用户权限过滤
        if not self.request.user.is_device_admin:
            if self.request.user.role == 'device_owner':
                # 设备归属者可以看到自己设备的申请
                queryset = queryset.filter(
                    Q(device__owner=self.request.user) | Q(borrower=self.request.user)
                )
            else:
                # 普通用户只能看到自己的申请
                queryset = queryset.filter(borrower=self.request.user)
        
        return queryset
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return LoanApplicationCreateSerializer
        return LoanApplicationListSerializer
    
    @extend_schema(
        summary="获取借用申请列表",
        description="获取借用申请列表，支持筛选和搜索",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    @extend_schema(
        summary="创建借用申请",
        description="创建新的借用申请",
        tags=["借用管理"]
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class LoanApplicationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """借用申请详情视图"""
    
    queryset = LoanApplication.objects.all()
    serializer_class = LoanApplicationDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_permissions(self):
        if self.request.method in ['PUT', 'PATCH', 'DELETE']:
            # 只有申请人或管理员可以修改/删除
            return [permissions.IsAuthenticated()]
        return [permissions.IsAuthenticated()]
    
    def get_object(self):
        obj = super().get_object()
        
        # 权限检查
        if not self.request.user.is_device_admin:
            if self.request.user.role == 'device_owner':
                # 设备归属者可以查看自己设备的申请
                if obj.device.owner != self.request.user and obj.borrower != self.request.user:
                    self.permission_denied(self.request)
            elif obj.borrower != self.request.user:
                # 普通用户只能查看自己的申请
                self.permission_denied(self.request)
        
        return obj
    
    @extend_schema(
        summary="获取借用申请详情",
        description="获取指定借用申请的详细信息",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
    
    def delete(self, request, *args, **kwargs):
        loan = self.get_object()
        
        # 只能删除待审批状态的申请
        if loan.status != 'pending':
            return Response(
                {"error": "只能删除待审批状态的申请"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 只有申请人可以删除自己的申请
        if loan.borrower != request.user:
            return Response(
                {"error": "只能删除自己的申请"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        loan.cancel()
        loan.save()
        return Response({"message": "申请已取消"})


class LoanApprovalView(APIView):
    """借用申请审批视图"""
    
    permission_classes = [IsDeviceOwnerOrAdmin]
    
    @extend_schema(
        summary="审批借用申请",
        description="批准或拒绝借用申请",
        tags=["借用管理"],
        request=LoanApprovalSerializer
    )
    def post(self, request, pk):
        try:
            loan = LoanApplication.objects.get(pk=pk)
        except LoanApplication.DoesNotExist:
            return Response(
                {"error": "借用申请不存在"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 权限检查：只有设备归属者或管理员可以审批
        if not request.user.is_device_admin and loan.device.owner != request.user:
            return Response(
                {"error": "没有权限审批此申请"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = LoanApprovalSerializer(data=request.data, context={'loan': loan})
        
        if serializer.is_valid():
            action = serializer.validated_data['action']
            notes = serializer.validated_data.get('notes', '')
            
            if action == 'approve':
                loan.approve(request.user, notes)
                message = "申请已批准"
            else:
                loan.reject(request.user, notes)
                message = "申请已拒绝"
            
            loan.save()
            
            return Response({
                "message": message,
                "status": loan.status
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoanStartView(APIView):
    """开始借用视图"""
    
    permission_classes = [IsDeviceOwnerOrAdmin]
    
    @extend_schema(
        summary="开始借用",
        description="开始已批准的借用申请",
        tags=["借用管理"],
        request=LoanStartSerializer
    )
    def post(self, request, pk):
        try:
            loan = LoanApplication.objects.get(pk=pk)
        except LoanApplication.DoesNotExist:
            return Response(
                {"error": "借用申请不存在"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 权限检查
        if not request.user.is_device_admin and loan.device.owner != request.user:
            return Response(
                {"error": "没有权限操作此申请"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = LoanStartSerializer(data=request.data, context={'loan': loan})
        
        if serializer.is_valid():
            loan.start_loan()
            loan.save()
            
            return Response({
                "message": "借用已开始",
                "actual_start_date": loan.actual_start_date
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoanReturnView(APIView):
    """归还设备视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @extend_schema(
        summary="归还设备",
        description="归还借用的设备",
        tags=["借用管理"],
        request=LoanReturnSerializer
    )
    def post(self, request, pk):
        try:
            loan = LoanApplication.objects.get(pk=pk)
        except LoanApplication.DoesNotExist:
            return Response(
                {"error": "借用申请不存在"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 权限检查：借用人、设备归属者或管理员可以归还
        if (loan.borrower != request.user and 
            not request.user.is_device_admin and 
            loan.device.owner != request.user):
            return Response(
                {"error": "没有权限归还此设备"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = LoanReturnSerializer(data=request.data, context={'loan': loan})
        
        if serializer.is_valid():
            condition = serializer.validated_data.get('condition', '')
            damage_report = serializer.validated_data.get('damage_report', '')
            
            loan.return_device(condition, damage_report)
            loan.save()
            
            # 创建历史记录
            LoanHistory.objects.create(
                device=loan.device,
                borrower=loan.borrower,
                start_date=loan.actual_start_date,
                end_date=loan.actual_end_date,
                expected_end_date=loan.expected_end_date,
                reason=loan.reason,
                return_condition=condition,
                damage_report=damage_report,
                is_overdue=loan.is_overdue,
                overdue_days=max(0, (loan.actual_end_date - loan.expected_end_date).days) if loan.is_overdue else 0,
                loan_application=loan
            )
            
            return Response({
                "message": "设备已归还",
                "actual_end_date": loan.actual_end_date
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MyLoanListView(generics.ListAPIView):
    """我的借用列表视图"""

    serializer_class = LoanApplicationListSerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-created_at']

    def get_queryset(self):
        return LoanApplication.objects.filter(
            borrower=self.request.user,
            status__in=['approved', 'borrowed']
        )

    @extend_schema(
        summary="获取我的借用列表",
        description="获取当前用户的借用列表",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class MyApplicationListView(generics.ListAPIView):
    """我的申请列表视图"""

    serializer_class = LoanApplicationListSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['status']
    ordering = ['-created_at']

    def get_queryset(self):
        return LoanApplication.objects.filter(borrower=self.request.user)

    @extend_schema(
        summary="获取我的申请列表",
        description="获取当前用户的所有申请",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class PendingApprovalListView(generics.ListAPIView):
    """待审批申请列表视图"""

    serializer_class = LoanApplicationListSerializer
    permission_classes = [IsDeviceOwnerOrAdmin]
    ordering = ['created_at']  # 按申请时间正序，优先处理早期申请

    def get_queryset(self):
        queryset = LoanApplication.objects.filter(status='pending')

        # 设备归属者只能看到自己设备的待审批申请
        if not self.request.user.is_device_admin:
            queryset = queryset.filter(device__owner=self.request.user)

        return queryset

    @extend_schema(
        summary="获取待审批申请列表",
        description="获取需要审批的借用申请",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class LoanExtensionListCreateView(generics.ListCreateAPIView):
    """借用延期申请列表和创建视图"""

    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-created_at']

    def get_queryset(self):
        loan_id = self.kwargs.get('loan_id')
        queryset = LoanExtension.objects.filter(loan_application_id=loan_id)

        # 权限过滤
        if not self.request.user.is_device_admin:
            queryset = queryset.filter(
                Q(loan_application__borrower=self.request.user) |
                Q(loan_application__device__owner=self.request.user)
            )

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return LoanExtensionCreateSerializer
        return LoanExtensionSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.request.method == 'POST':
            loan_id = self.kwargs.get('loan_id')
            try:
                loan = LoanApplication.objects.get(id=loan_id)
                context['loan'] = loan
            except LoanApplication.DoesNotExist:
                pass
        return context

    @extend_schema(
        summary="获取借用延期申请列表",
        description="获取指定借用的延期申请列表",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="创建借用延期申请",
        description="为指定借用创建延期申请",
        tags=["借用管理"]
    )
    def post(self, request, *args, **kwargs):
        loan_id = self.kwargs.get('loan_id')
        try:
            loan = LoanApplication.objects.get(id=loan_id)
        except LoanApplication.DoesNotExist:
            return Response(
                {"error": "借用申请不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 只有借用人可以申请延期
        if loan.borrower != request.user:
            return Response(
                {"error": "只能为自己的借用申请延期"},
                status=status.HTTP_403_FORBIDDEN
            )

        # 只能为借用中的申请延期
        if loan.status != 'borrowed':
            return Response(
                {"error": "只能为借用中的设备申请延期"},
                status=status.HTTP_400_BAD_REQUEST
            )

        return super().post(request, *args, **kwargs)


class LoanExtensionDetailView(generics.RetrieveAPIView):
    """借用延期详情视图"""

    queryset = LoanExtension.objects.all()
    serializer_class = LoanExtensionSerializer
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取借用延期详情",
        description="获取指定延期申请的详细信息",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ExtensionApprovalView(APIView):
    """延期申请审批视图"""

    permission_classes = [IsDeviceOwnerOrAdmin]

    @extend_schema(
        summary="审批延期申请",
        description="批准或拒绝延期申请",
        tags=["借用管理"]
    )
    def post(self, request, pk):
        try:
            extension = LoanExtension.objects.get(pk=pk)
        except LoanExtension.DoesNotExist:
            return Response(
                {"error": "延期申请不存在"},
                status=status.HTTP_404_NOT_FOUND
            )

        # 权限检查
        if (not request.user.is_device_admin and
            extension.loan_application.device.owner != request.user):
            return Response(
                {"error": "没有权限审批此延期申请"},
                status=status.HTTP_403_FORBIDDEN
            )

        action = request.data.get('action')
        notes = request.data.get('notes', '')

        if action not in ['approve', 'reject']:
            return Response(
                {"error": "无效的操作"},
                status=status.HTTP_400_BAD_REQUEST
            )

        if extension.status != 'pending':
            return Response(
                {"error": "只能审批待审批状态的延期申请"},
                status=status.HTTP_400_BAD_REQUEST
            )

        extension.status = 'approved' if action == 'approve' else 'rejected'
        extension.approver = request.user
        extension.approved_at = timezone.now()
        extension.approval_notes = notes
        extension.save()

        # 如果批准，更新原借用申请的结束时间
        if action == 'approve':
            loan = extension.loan_application
            loan.expected_end_date = extension.new_end_date
            loan.save()

        message = "延期申请已批准" if action == 'approve' else "延期申请已拒绝"
        return Response({"message": message})


class LoanHistoryListView(generics.ListAPIView):
    """借用历史列表视图"""

    serializer_class = LoanHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['device', 'borrower', 'is_overdue']
    search_fields = ['device__name', 'borrower__username', 'reason']
    ordering = ['-start_date']

    def get_queryset(self):
        queryset = LoanHistory.objects.all()

        # 根据用户权限过滤
        if not self.request.user.is_device_admin:
            if self.request.user.role == 'device_owner':
                # 设备归属者可以看到自己设备的历史
                queryset = queryset.filter(
                    Q(device__owner=self.request.user) | Q(borrower=self.request.user)
                )
            else:
                # 普通用户只能看到自己的借用历史
                queryset = queryset.filter(borrower=self.request.user)

        return queryset

    @extend_schema(
        summary="获取借用历史列表",
        description="获取借用历史记录",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class LoanReminderListView(generics.ListAPIView):
    """借用提醒列表视图"""

    serializer_class = LoanReminderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filterset_fields = ['reminder_type', 'is_sent']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = LoanReminder.objects.all()

        # 根据用户权限过滤
        if not self.request.user.is_device_admin:
            queryset = queryset.filter(loan_application__borrower=self.request.user)

        return queryset

    @extend_schema(
        summary="获取借用提醒列表",
        description="获取借用相关提醒",
        tags=["借用管理"]
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class LoanStatisticsView(APIView):
    """借用统计视图"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取借用统计数据",
        description="获取借用相关的统计信息",
        tags=["借用管理"]
    )
    def get(self, request):
        from apps.devices.models import Device

        # 基础统计
        total_applications = LoanApplication.objects.count()
        pending_applications = LoanApplication.objects.filter(status='pending').count()
        approved_applications = LoanApplication.objects.filter(status='approved').count()
        active_loans = LoanApplication.objects.filter(status='borrowed').count()
        overdue_loans = LoanApplication.objects.filter(status='borrowed').filter(
            expected_end_date__lt=timezone.now()
        ).count()

        # 设备统计
        total_devices = Device.objects.filter(is_deleted=False).count()
        available_devices = Device.objects.filter(status='available', is_deleted=False).count()
        borrowed_devices = Device.objects.filter(status='borrowed', is_deleted=False).count()

        # 用户相关统计
        user_loan_count = LoanApplication.objects.filter(borrower=request.user).count()
        user_overdue_count = LoanApplication.objects.filter(
            borrower=request.user,
            status='borrowed',
            expected_end_date__lt=timezone.now()
        ).count()

        # 时间范围统计
        now = timezone.now()
        month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        week_start = now - timedelta(days=now.weekday())

        loans_this_month = LoanApplication.objects.filter(
            created_at__gte=month_start
        ).count()
        loans_this_week = LoanApplication.objects.filter(
            created_at__gte=week_start
        ).count()

        data = {
            'total_applications': total_applications,
            'pending_applications': pending_applications,
            'approved_applications': approved_applications,
            'active_loans': active_loans,
            'overdue_loans': overdue_loans,
            'total_devices': total_devices,
            'available_devices': available_devices,
            'borrowed_devices': borrowed_devices,
            'user_loan_count': user_loan_count,
            'user_overdue_count': user_overdue_count,
            'loans_this_month': loans_this_month,
            'loans_this_week': loans_this_week,
        }

        serializer = LoanStatisticsSerializer(data)
        return Response(serializer.data)
