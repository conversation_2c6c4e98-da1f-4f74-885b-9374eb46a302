"""
借用管理序列化器
"""
from rest_framework import serializers
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import LoanApplication, LoanExtension, LoanHistory, LoanReminder

User = get_user_model()


class LoanApplicationCreateSerializer(serializers.ModelSerializer):
    """借用申请创建序列化器"""
    
    class Meta:
        model = LoanApplication
        fields = [
            'device', 'reason', 'expected_start_date', 'expected_end_date', 'priority'
        ]
    
    def validate(self, attrs):
        device = attrs['device']
        expected_start_date = attrs['expected_start_date']
        expected_end_date = attrs['expected_end_date']
        
        # 验证时间
        if expected_start_date >= expected_end_date:
            raise serializers.ValidationError("结束时间必须晚于开始时间")
        
        if expected_start_date < timezone.now():
            raise serializers.ValidationError("开始时间不能早于当前时间")
        
        # 验证设备状态
        if not device.is_available_for_borrow:
            raise serializers.ValidationError("设备当前不可借用")
        
        # 检查时间冲突
        conflicting_loans = LoanApplication.objects.filter(
            device=device,
            status__in=['approved', 'borrowed'],
            expected_start_date__lt=expected_end_date,
            expected_end_date__gt=expected_start_date
        )
        
        if conflicting_loans.exists():
            raise serializers.ValidationError("所选时间段与其他借用申请冲突")
        
        return attrs
    
    def create(self, validated_data):
        validated_data['borrower'] = self.context['request'].user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class LoanApplicationListSerializer(serializers.ModelSerializer):
    """借用申请列表序列化器"""
    
    device_info = serializers.SerializerMethodField()
    borrower_info = serializers.SerializerMethodField()
    approver_info = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    is_overdue = serializers.BooleanField(read_only=True)
    duration_days = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = LoanApplication
        fields = [
            'id', 'device', 'device_info', 'borrower', 'borrower_info',
            'reason', 'expected_start_date', 'expected_end_date', 'priority', 'priority_display',
            'status', 'status_display', 'approver', 'approver_info', 'approved_at',
            'actual_start_date', 'actual_end_date', 'is_overdue', 'duration_days',
            'created_at', 'updated_at'
        ]
    
    def get_device_info(self, obj):
        return {
            'id': obj.device.id,
            'name': obj.device.name,
            'model': obj.device.model,
            'serial_number': obj.device.serial_number,
            'device_number': obj.device.device_number
        }
    
    def get_borrower_info(self, obj):
        return {
            'id': obj.borrower.id,
            'username': obj.borrower.username,
            'email': obj.borrower.email,
            'department': obj.borrower.department
        }
    
    def get_approver_info(self, obj):
        if obj.approver:
            return {
                'id': obj.approver.id,
                'username': obj.approver.username,
                'email': obj.approver.email
            }
        return None


class LoanApplicationDetailSerializer(LoanApplicationListSerializer):
    """借用申请详情序列化器"""
    
    class Meta(LoanApplicationListSerializer.Meta):
        fields = LoanApplicationListSerializer.Meta.fields + [
            'approval_notes', 'return_condition', 'damage_report'
        ]


class LoanApprovalSerializer(serializers.Serializer):
    """借用审批序列化器"""
    
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    notes = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    
    def validate(self, attrs):
        loan = self.context['loan']
        
        if loan.status != 'pending':
            raise serializers.ValidationError("只能审批待审批状态的申请")
        
        # 如果是批准，检查设备是否仍然可用
        if attrs['action'] == 'approve':
            if not loan.device.is_available_for_borrow:
                raise serializers.ValidationError("设备当前不可借用")
        
        return attrs


class LoanStartSerializer(serializers.Serializer):
    """开始借用序列化器"""
    
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)
    
    def validate(self, attrs):
        loan = self.context['loan']
        
        if loan.status != 'approved':
            raise serializers.ValidationError("只能开始已批准的借用申请")
        
        if not loan.device.is_available_for_borrow:
            raise serializers.ValidationError("设备当前不可借用")
        
        return attrs


class LoanReturnSerializer(serializers.Serializer):
    """归还设备序列化器"""
    
    condition = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    damage_report = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    
    def validate(self, attrs):
        loan = self.context['loan']
        
        if loan.status != 'borrowed':
            raise serializers.ValidationError("只能归还借用中的设备")
        
        return attrs


class LoanExtensionCreateSerializer(serializers.ModelSerializer):
    """借用延期申请创建序列化器"""
    
    class Meta:
        model = LoanExtension
        fields = ['new_end_date', 'reason']
    
    def validate_new_end_date(self, value):
        loan = self.context['loan']
        
        if value <= loan.expected_end_date:
            raise serializers.ValidationError("新结束时间必须晚于当前结束时间")
        
        if value <= timezone.now():
            raise serializers.ValidationError("新结束时间不能早于当前时间")
        
        return value
    
    def create(self, validated_data):
        loan = self.context['loan']
        validated_data['loan_application'] = loan
        validated_data['original_end_date'] = loan.expected_end_date
        return super().create(validated_data)


class LoanExtensionSerializer(serializers.ModelSerializer):
    """借用延期序列化器"""
    
    loan_info = serializers.SerializerMethodField()
    approver_info = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = LoanExtension
        fields = [
            'id', 'loan_application', 'loan_info', 'original_end_date', 'new_end_date',
            'reason', 'status', 'status_display', 'approver', 'approver_info',
            'approved_at', 'approval_notes', 'created_at'
        ]
    
    def get_loan_info(self, obj):
        return {
            'id': obj.loan_application.id,
            'device_name': obj.loan_application.device.name,
            'borrower_name': obj.loan_application.borrower.username
        }
    
    def get_approver_info(self, obj):
        if obj.approver:
            return {
                'id': obj.approver.id,
                'username': obj.approver.username
            }
        return None


class LoanHistorySerializer(serializers.ModelSerializer):
    """借用历史序列化器"""
    
    device_info = serializers.SerializerMethodField()
    borrower_info = serializers.SerializerMethodField()
    duration_days = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = LoanHistory
        fields = [
            'id', 'device', 'device_info', 'borrower', 'borrower_info',
            'start_date', 'end_date', 'expected_end_date', 'reason',
            'return_condition', 'damage_report', 'is_overdue', 'overdue_days',
            'duration_days', 'created_at'
        ]
    
    def get_device_info(self, obj):
        return {
            'id': obj.device.id,
            'name': obj.device.name,
            'model': obj.device.model,
            'device_number': obj.device.device_number
        }
    
    def get_borrower_info(self, obj):
        return {
            'id': obj.borrower.id,
            'username': obj.borrower.username,
            'department': obj.borrower.department
        }


class LoanReminderSerializer(serializers.ModelSerializer):
    """借用提醒序列化器"""
    
    loan_info = serializers.SerializerMethodField()
    reminder_type_display = serializers.CharField(source='get_reminder_type_display', read_only=True)
    
    class Meta:
        model = LoanReminder
        fields = [
            'id', 'loan_application', 'loan_info', 'reminder_type', 'reminder_type_display',
            'message', 'is_sent', 'sent_at', 'created_at'
        ]
    
    def get_loan_info(self, obj):
        return {
            'id': obj.loan_application.id,
            'device_name': obj.loan_application.device.name,
            'borrower_name': obj.loan_application.borrower.username
        }


class LoanStatisticsSerializer(serializers.Serializer):
    """借用统计序列化器"""
    
    total_applications = serializers.IntegerField()
    pending_applications = serializers.IntegerField()
    approved_applications = serializers.IntegerField()
    active_loans = serializers.IntegerField()
    overdue_loans = serializers.IntegerField()
    total_devices = serializers.IntegerField()
    available_devices = serializers.IntegerField()
    borrowed_devices = serializers.IntegerField()
    
    # 用户统计
    user_loan_count = serializers.IntegerField()
    user_overdue_count = serializers.IntegerField()
    
    # 时间范围统计
    loans_this_month = serializers.IntegerField()
    loans_this_week = serializers.IntegerField()
