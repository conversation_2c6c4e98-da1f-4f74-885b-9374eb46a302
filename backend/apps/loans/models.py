"""
借用管理模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_fsm import FSMField, transition
from django.utils import timezone
from datetime import timedelta
from apps.common.models import BaseModel, AuditModel


class LoanApplication(AuditModel):
    """借用申请"""
    
    STATUS_CHOICES = [
        ('pending', '待审批'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
        ('cancelled', '已取消'),
        ('borrowed', '已借出'),
        ('returned', '已归还'),
        ('overdue', '已超期'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    # 基础信息
    device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        related_name='loan_applications',
        verbose_name=_('设备')
    )
    borrower = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='loan_applications',
        verbose_name=_('借用人')
    )
    
    # 申请信息
    reason = models.TextField(_('借用理由'))
    expected_start_date = models.DateTimeField(_('预期开始时间'))
    expected_end_date = models.DateTimeField(_('预期结束时间'))
    priority = models.CharField(_('优先级'), max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # 状态管理
    status = FSMField(
        _('申请状态'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    
    # 审批信息
    approver = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_loans',
        verbose_name=_('审批人')
    )
    approved_at = models.DateTimeField(_('审批时间'), null=True, blank=True)
    approval_notes = models.TextField(_('审批备注'), blank=True)
    
    # 实际借用信息
    actual_start_date = models.DateTimeField(_('实际开始时间'), null=True, blank=True)
    actual_end_date = models.DateTimeField(_('实际结束时间'), null=True, blank=True)
    
    # 归还信息
    return_condition = models.TextField(_('归还状态说明'), blank=True)
    damage_report = models.TextField(_('损坏报告'), blank=True)
    
    class Meta:
        db_table = 'loan_applications'
        verbose_name = _('借用申请')
        verbose_name_plural = _('借用申请')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['borrower']),
            models.Index(fields=['device']),
            models.Index(fields=['expected_start_date']),
        ]
    
    def __str__(self):
        return f"{self.borrower.username} 申请借用 {self.device.name}"
    
    @property
    def is_overdue(self):
        """是否超期"""
        if self.status == 'borrowed' and self.expected_end_date:
            return timezone.now() > self.expected_end_date
        return False
    
    @property
    def duration_days(self):
        """借用天数"""
        if self.expected_start_date and self.expected_end_date:
            return (self.expected_end_date - self.expected_start_date).days
        return 0
    
    @property
    def actual_duration_days(self):
        """实际借用天数"""
        if self.actual_start_date and self.actual_end_date:
            return (self.actual_end_date - self.actual_start_date).days
        return 0
    
    # 状态机转换方法
    @transition(field=status, source='pending', target='approved')
    def approve(self, approver, notes=''):
        """批准申请"""
        self.approver = approver
        self.approved_at = timezone.now()
        self.approval_notes = notes
    
    @transition(field=status, source='pending', target='rejected')
    def reject(self, approver, notes=''):
        """拒绝申请"""
        self.approver = approver
        self.approved_at = timezone.now()
        self.approval_notes = notes
    
    @transition(field=status, source='pending', target='cancelled')
    def cancel(self):
        """取消申请"""
        pass
    
    @transition(field=status, source='approved', target='borrowed')
    def start_loan(self):
        """开始借用"""
        self.actual_start_date = timezone.now()
        # 更新设备状态
        self.device.borrow(self.borrower)
        self.device.save()
    
    @transition(field=status, source='borrowed', target='returned')
    def return_device(self, condition='', damage_report=''):
        """归还设备"""
        self.actual_end_date = timezone.now()
        self.return_condition = condition
        self.damage_report = damage_report
        # 更新设备状态
        self.device.return_to_available()
        self.device.save()
    
    @transition(field=status, source='borrowed', target='overdue')
    def mark_overdue(self):
        """标记为超期"""
        pass


class LoanExtension(BaseModel):
    """借用延期申请"""
    
    STATUS_CHOICES = [
        ('pending', '待审批'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
    ]
    
    loan_application = models.ForeignKey(
        LoanApplication,
        on_delete=models.CASCADE,
        related_name='extensions',
        verbose_name=_('借用申请')
    )
    
    original_end_date = models.DateTimeField(_('原结束时间'))
    new_end_date = models.DateTimeField(_('新结束时间'))
    reason = models.TextField(_('延期理由'))
    
    status = models.CharField(_('状态'), max_length=20, choices=STATUS_CHOICES, default='pending')
    
    approver = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_extensions',
        verbose_name=_('审批人')
    )
    approved_at = models.DateTimeField(_('审批时间'), null=True, blank=True)
    approval_notes = models.TextField(_('审批备注'), blank=True)
    
    class Meta:
        db_table = 'loan_extensions'
        verbose_name = _('借用延期')
        verbose_name_plural = _('借用延期')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.loan_application.borrower.username} 申请延期到 {self.new_end_date}"


class LoanHistory(BaseModel):
    """借用历史记录"""
    
    device = models.ForeignKey(
        'devices.Device',
        on_delete=models.CASCADE,
        related_name='loan_history',
        verbose_name=_('设备')
    )
    borrower = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='loan_history',
        verbose_name=_('借用人')
    )
    
    start_date = models.DateTimeField(_('开始时间'))
    end_date = models.DateTimeField(_('结束时间'), null=True, blank=True)
    expected_end_date = models.DateTimeField(_('预期结束时间'))
    
    reason = models.TextField(_('借用理由'))
    return_condition = models.TextField(_('归还状态'), blank=True)
    damage_report = models.TextField(_('损坏报告'), blank=True)
    
    is_overdue = models.BooleanField(_('是否超期'), default=False)
    overdue_days = models.PositiveIntegerField(_('超期天数'), default=0)
    
    # 关联原始申请
    loan_application = models.OneToOneField(
        LoanApplication,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='history_record',
        verbose_name=_('原始申请')
    )
    
    class Meta:
        db_table = 'loan_history'
        verbose_name = _('借用历史')
        verbose_name_plural = _('借用历史')
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['device']),
            models.Index(fields=['borrower']),
            models.Index(fields=['start_date']),
            models.Index(fields=['is_overdue']),
        ]
    
    def __str__(self):
        return f"{self.borrower.username} 借用 {self.device.name} ({self.start_date.date()})"
    
    @property
    def duration_days(self):
        """借用天数"""
        if self.end_date:
            return (self.end_date - self.start_date).days
        return (timezone.now() - self.start_date).days


class LoanReminder(BaseModel):
    """借用提醒"""
    
    REMINDER_TYPE_CHOICES = [
        ('due_soon', '即将到期'),
        ('overdue', '已超期'),
        ('return_request', '归还请求'),
    ]
    
    loan_application = models.ForeignKey(
        LoanApplication,
        on_delete=models.CASCADE,
        related_name='reminders',
        verbose_name=_('借用申请')
    )
    
    reminder_type = models.CharField(_('提醒类型'), max_length=20, choices=REMINDER_TYPE_CHOICES)
    message = models.TextField(_('提醒消息'))
    
    is_sent = models.BooleanField(_('是否已发送'), default=False)
    sent_at = models.DateTimeField(_('发送时间'), null=True, blank=True)
    
    class Meta:
        db_table = 'loan_reminders'
        verbose_name = _('借用提醒')
        verbose_name_plural = _('借用提醒')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.get_reminder_type_display()} - {self.loan_application.borrower.username}"
