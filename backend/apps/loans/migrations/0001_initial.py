# Generated by Django 4.2.7 on 2025-07-27 13:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_fsm
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('devices', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoanApplication',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('reason', models.TextField(verbose_name='借用理由')),
                ('expected_start_date', models.DateTimeField(verbose_name='预期开始时间')),
                ('expected_end_date', models.DateTimeField(verbose_name='预期结束时间')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=10, verbose_name='优先级')),
                ('status', django_fsm.FSMField(choices=[('pending', '待审批'), ('approved', '已批准'), ('rejected', '已拒绝'), ('cancelled', '已取消'), ('borrowed', '已借出'), ('returned', '已归还'), ('overdue', '已超期')], default='pending', max_length=20, verbose_name='申请状态')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审批时间')),
                ('approval_notes', models.TextField(blank=True, verbose_name='审批备注')),
                ('actual_start_date', models.DateTimeField(blank=True, null=True, verbose_name='实际开始时间')),
                ('actual_end_date', models.DateTimeField(blank=True, null=True, verbose_name='实际结束时间')),
                ('return_condition', models.TextField(blank=True, verbose_name='归还状态说明')),
                ('damage_report', models.TextField(blank=True, verbose_name='损坏报告')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_loans', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('borrower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_applications', to=settings.AUTH_USER_MODEL, verbose_name='借用人')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建者')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_applications', to='devices.device', verbose_name='设备')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新者')),
            ],
            options={
                'verbose_name': '借用申请',
                'verbose_name_plural': '借用申请',
                'db_table': 'loan_applications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LoanReminder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('reminder_type', models.CharField(choices=[('due_soon', '即将到期'), ('overdue', '已超期'), ('return_request', '归还请求')], max_length=20, verbose_name='提醒类型')),
                ('message', models.TextField(verbose_name='提醒消息')),
                ('is_sent', models.BooleanField(default=False, verbose_name='是否已发送')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='发送时间')),
                ('loan_application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='loans.loanapplication', verbose_name='借用申请')),
            ],
            options={
                'verbose_name': '借用提醒',
                'verbose_name_plural': '借用提醒',
                'db_table': 'loan_reminders',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LoanExtension',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('original_end_date', models.DateTimeField(verbose_name='原结束时间')),
                ('new_end_date', models.DateTimeField(verbose_name='新结束时间')),
                ('reason', models.TextField(verbose_name='延期理由')),
                ('status', models.CharField(choices=[('pending', '待审批'), ('approved', '已批准'), ('rejected', '已拒绝')], default='pending', max_length=20, verbose_name='状态')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审批时间')),
                ('approval_notes', models.TextField(blank=True, verbose_name='审批备注')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_extensions', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('loan_application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extensions', to='loans.loanapplication', verbose_name='借用申请')),
            ],
            options={
                'verbose_name': '借用延期',
                'verbose_name_plural': '借用延期',
                'db_table': 'loan_extensions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LoanHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('start_date', models.DateTimeField(verbose_name='开始时间')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('expected_end_date', models.DateTimeField(verbose_name='预期结束时间')),
                ('reason', models.TextField(verbose_name='借用理由')),
                ('return_condition', models.TextField(blank=True, verbose_name='归还状态')),
                ('damage_report', models.TextField(blank=True, verbose_name='损坏报告')),
                ('is_overdue', models.BooleanField(default=False, verbose_name='是否超期')),
                ('overdue_days', models.PositiveIntegerField(default=0, verbose_name='超期天数')),
                ('borrower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_history', to=settings.AUTH_USER_MODEL, verbose_name='借用人')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loan_history', to='devices.device', verbose_name='设备')),
                ('loan_application', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='history_record', to='loans.loanapplication', verbose_name='原始申请')),
            ],
            options={
                'verbose_name': '借用历史',
                'verbose_name_plural': '借用历史',
                'db_table': 'loan_history',
                'ordering': ['-start_date'],
                'indexes': [models.Index(fields=['device'], name='loan_histor_device__2e774a_idx'), models.Index(fields=['borrower'], name='loan_histor_borrowe_ae7668_idx'), models.Index(fields=['start_date'], name='loan_histor_start_d_27898e_idx'), models.Index(fields=['is_overdue'], name='loan_histor_is_over_d879b9_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='loanapplication',
            index=models.Index(fields=['status'], name='loan_applic_status_2fc934_idx'),
        ),
        migrations.AddIndex(
            model_name='loanapplication',
            index=models.Index(fields=['borrower'], name='loan_applic_borrowe_dd0501_idx'),
        ),
        migrations.AddIndex(
            model_name='loanapplication',
            index=models.Index(fields=['device'], name='loan_applic_device__56a522_idx'),
        ),
        migrations.AddIndex(
            model_name='loanapplication',
            index=models.Index(fields=['expected_start_date'], name='loan_applic_expecte_cf1cdf_idx'),
        ),
    ]
