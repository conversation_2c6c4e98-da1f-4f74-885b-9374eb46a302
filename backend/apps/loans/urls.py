"""
借用相关URL配置
"""
from django.urls import path
from . import views

app_name = 'loans'

urlpatterns = [
    # 借用申请
    path('applications/', views.LoanApplicationListCreateView.as_view(), name='application_list_create'),
    path('applications/<uuid:pk>/', views.LoanApplicationDetailView.as_view(), name='application_detail'),
    path('applications/<uuid:pk>/approve/', views.LoanApprovalView.as_view(), name='application_approve'),
    path('applications/<uuid:pk>/start/', views.LoanStartView.as_view(), name='loan_start'),
    path('applications/<uuid:pk>/return/', views.LoanReturnView.as_view(), name='loan_return'),

    # 我的借用
    path('my-loans/', views.MyLoanListView.as_view(), name='my_loans'),
    path('my-applications/', views.MyApplicationListView.as_view(), name='my_applications'),

    # 待审批
    path('pending-approvals/', views.PendingApprovalListView.as_view(), name='pending_approvals'),

    # 借用延期
    path('applications/<uuid:loan_id>/extensions/', views.LoanExtensionListCreateView.as_view(), name='extension_list_create'),
    path('extensions/<uuid:pk>/', views.LoanExtensionDetailView.as_view(), name='extension_detail'),
    path('extensions/<uuid:pk>/approve/', views.ExtensionApprovalView.as_view(), name='extension_approve'),

    # 借用历史
    path('history/', views.LoanHistoryListView.as_view(), name='loan_history'),

    # 提醒
    path('reminders/', views.LoanReminderListView.as_view(), name='loan_reminders'),

    # 统计
    path('statistics/', views.LoanStatisticsView.as_view(), name='loan_statistics'),
]
