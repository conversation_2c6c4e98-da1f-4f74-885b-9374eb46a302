"""
报表相关序列化器
"""
from rest_framework import serializers


class DeviceStatsSerializer(serializers.Serializer):
    """设备统计序列化器"""
    total = serializers.IntegerField()
    available = serializers.IntegerField()
    borrowed = serializers.IntegerField()
    maintenance = serializers.IntegerField()
    utilization_rate = serializers.FloatField()


class LoanStatsSerializer(serializers.Serializer):
    """借用统计序列化器"""
    total = serializers.IntegerField()
    active = serializers.IntegerField()
    pending = serializers.IntegerField()
    overdue = serializers.IntegerField()
    this_month = serializers.IntegerField()
    this_week = serializers.IntegerField()


class UserStatsSerializer(serializers.Serializer):
    """用户统计序列化器"""
    total = serializers.IntegerField()
    active_borrowers = serializers.IntegerField()


class DashboardStatisticsSerializer(serializers.Serializer):
    """仪表盘统计序列化器"""
    device_stats = DeviceStatsSerializer()
    loan_stats = LoanStatsSerializer()
    user_stats = UserStatsSerializer()


class DeviceStatisticsSerializer(serializers.Serializer):
    """设备统计序列化器"""
    status_distribution = serializers.ListField()
    category_distribution = serializers.ListField()
    owner_distribution = serializers.ListField()
    utilization_stats = serializers.ListField()


class LoanStatisticsSerializer(serializers.Serializer):
    """借用统计序列化器"""
    monthly_trend = serializers.ListField()
    success_rate = serializers.FloatField()
    average_duration_days = serializers.IntegerField()
    overdue_count = serializers.IntegerField()
    popular_devices = serializers.ListField()


class UserStatisticsSerializer(serializers.Serializer):
    """用户统计序列化器"""
    role_distribution = serializers.ListField()
    active_users_count = serializers.IntegerField()
    user_ranking = serializers.ListField()
    department_stats = serializers.ListField()


class DashboardStatisticsSerializer(serializers.Serializer):
    """仪表盘统计序列化器"""
    device_stats = DeviceStatsSerializer()
    loan_stats = LoanStatsSerializer()
    user_stats = UserStatsSerializer()


class StatusDistributionSerializer(serializers.Serializer):
    """状态分布序列化器"""
    status = serializers.CharField()
    count = serializers.IntegerField()


class CategoryDistributionSerializer(serializers.Serializer):
    """分类分布序列化器"""
    category__name = serializers.CharField()
    count = serializers.IntegerField()
    available_count = serializers.IntegerField()
    borrowed_count = serializers.IntegerField()


class OwnerDistributionSerializer(serializers.Serializer):
    """归属者分布序列化器"""
    owner__username = serializers.CharField()
    count = serializers.IntegerField()
    available_count = serializers.IntegerField()
    borrowed_count = serializers.IntegerField()


class UtilizationStatsSerializer(serializers.Serializer):
    """利用率统计序列化器"""
    device_name = serializers.CharField()
    device_number = serializers.CharField()
    utilization_rate = serializers.FloatField()


class DeviceStatisticsSerializer(serializers.Serializer):
    """设备统计序列化器"""
    status_distribution = StatusDistributionSerializer(many=True)
    category_distribution = CategoryDistributionSerializer(many=True)
    owner_distribution = OwnerDistributionSerializer(many=True)
    utilization_stats = UtilizationStatsSerializer(many=True)


class MonthlyTrendSerializer(serializers.Serializer):
    """月度趋势序列化器"""
    month = serializers.CharField()
    loan_count = serializers.IntegerField()


class PopularDeviceSerializer(serializers.Serializer):
    """热门设备序列化器"""
    device__name = serializers.CharField()
    device__model = serializers.CharField()
    loan_count = serializers.IntegerField()


class LoanStatisticsSerializer(serializers.Serializer):
    """借用统计序列化器"""
    monthly_trend = MonthlyTrendSerializer(many=True)
    success_rate = serializers.FloatField()
    average_duration_days = serializers.IntegerField()
    overdue_count = serializers.IntegerField()
    popular_devices = PopularDeviceSerializer(many=True)


class RoleDistributionSerializer(serializers.Serializer):
    """角色分布序列化器"""
    role = serializers.CharField()
    count = serializers.IntegerField()


class UserRankingSerializer(serializers.Serializer):
    """用户排行序列化器"""
    borrower__username = serializers.CharField()
    borrower__department = serializers.CharField()
    loan_count = serializers.IntegerField()
    success_count = serializers.IntegerField()


class DepartmentStatsSerializer(serializers.Serializer):
    """部门统计序列化器"""
    borrower__department = serializers.CharField()
    loan_count = serializers.IntegerField()
    user_count = serializers.IntegerField()


class UserStatisticsSerializer(serializers.Serializer):
    """用户统计序列化器"""
    role_distribution = RoleDistributionSerializer(many=True)
    active_users_count = serializers.IntegerField()
    user_ranking = UserRankingSerializer(many=True)
    department_stats = DepartmentStatsSerializer(many=True)


class ExportReportSerializer(serializers.Serializer):
    """报表导出序列化器"""
    report_type = serializers.ChoiceField(
        choices=[
            ('device', '设备报表'),
            ('loan', '借用报表'),
            ('user', '用户报表'),
            ('dashboard', '仪表盘报表')
        ]
    )
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)
    format = serializers.ChoiceField(
        choices=[('excel', 'Excel'), ('pdf', 'PDF')],
        default='excel'
    )


class ChartDataSerializer(serializers.Serializer):
    """图表数据序列化器"""
    chart_type = serializers.ChoiceField(
        choices=[
            ('pie', '饼图'),
            ('bar', '柱状图'),
            ('line', '折线图'),
            ('area', '面积图')
        ]
    )
    title = serializers.CharField()
    data = serializers.JSONField()
    labels = serializers.ListField(child=serializers.CharField())
    colors = serializers.ListField(child=serializers.CharField(), required=False)
