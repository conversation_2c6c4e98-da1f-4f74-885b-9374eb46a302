"""
报表相关URL配置
"""
from django.urls import path
from .views import (
    DashboardStatisticsView, DeviceStatisticsView,
    LoanStatisticsView, UserStatisticsView, ExportReportView
)

app_name = 'reports'

urlpatterns = [
    path('dashboard/', DashboardStatisticsView.as_view(), name='dashboard_statistics'),
    path('devices/', DeviceStatisticsView.as_view(), name='device_statistics'),
    path('loans/', LoanStatisticsView.as_view(), name='loan_statistics'),
    path('users/', UserStatisticsView.as_view(), name='user_statistics'),
    path('export/', ExportReportView.as_view(), name='export_report'),
]
