"""
用户模型的Django Admin配置
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserProfile, LoginLog


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """用户管理"""
    
    list_display = [
        'username', 'email', 'first_name', 'last_name',
        'role', 'department', 'is_active', 'created_at'
    ]
    list_filter = ['role', 'department', 'is_active', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name', 'employee_id']
    ordering = ['-created_at']
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('个人信息'), {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'avatar')
        }),
        (_('工作信息'), {
            'fields': ('role', 'department', 'employee_id')
        }),
        (_('权限'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('重要日期'), {
            'fields': ('last_login', 'date_joined', 'created_at', 'updated_at')
        }),
        (_('登录信息'), {
            'fields': ('last_login_ip', 'login_count', 'failed_login_attempts', 'locked_until')
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'password1', 'password2', 'role'),
        }),
    )
    
    readonly_fields = ['created_at', 'updated_at', 'last_login', 'login_count']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料管理"""
    
    list_display = ['user', 'location', 'language', 'timezone', 'created_at']
    list_filter = ['language', 'timezone', 'email_notifications']
    search_fields = ['user__username', 'user__email', 'location']
    
    fieldsets = (
        (_('基本信息'), {
            'fields': ('user', 'bio', 'location', 'website')
        }),
        (_('通知设置'), {
            'fields': ('email_notifications', 'sms_notifications', 'wechat_notifications')
        }),
        (_('偏好设置'), {
            'fields': ('language', 'timezone')
        }),
    )


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """登录日志管理"""
    
    list_display = ['user', 'ip_address', 'login_time', 'success']
    list_filter = ['success', 'login_time']
    search_fields = ['user__username', 'ip_address']
    ordering = ['-login_time']
    
    readonly_fields = ['user', 'ip_address', 'user_agent', 'login_time', 'success', 'failure_reason']
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
