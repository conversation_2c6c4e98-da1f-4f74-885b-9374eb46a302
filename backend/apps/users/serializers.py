"""
用户相关序列化器
"""
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import User, UserProfile, LoginLog


class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器"""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'password', 'password_confirm',
            'first_name', 'last_name', 'phone', 'department', 'employee_id'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("密码确认不匹配")
        return attrs
    
    def create(self, validated_data):
        import uuid
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')

        # 处理空的employee_id，在创建用户前生成唯一值避免约束冲突
        if not validated_data.get('employee_id'):
            # 生成基于用户名和UUID的唯一employee_id
            unique_suffix = str(uuid.uuid4())[:8]
            validated_data['employee_id'] = f"USER_{validated_data['username'].upper()}_{unique_suffix}"

        # 创建用户
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()

        # 创建用户资料
        UserProfile.objects.create(user=user)

        return user


class UserLoginSerializer(serializers.Serializer):
    """用户登录序列化器"""
    
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('用户名或密码错误')
            if not user.is_active:
                raise serializers.ValidationError('用户账户已被禁用')
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError('必须提供用户名和密码')


class UserProfileSerializer(serializers.ModelSerializer):
    """用户资料序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'bio', 'location', 'website', 'email_notifications',
            'sms_notifications', 'wechat_notifications', 'language', 'timezone'
        ]


class UserSerializer(serializers.ModelSerializer):
    """用户信息序列化器"""

    profile = UserProfileSerializer(read_only=True)
    role_display = serializers.CharField(source='get_role_display', read_only=True)
    # 添加权限相关字段
    is_device_admin = serializers.BooleanField(read_only=True)
    is_device_owner = serializers.BooleanField(read_only=True)
    is_super_admin = serializers.BooleanField(read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'role', 'role_display', 'department', 'employee_id',
            'avatar', 'is_active', 'created_at', 'updated_at',
            'last_login', 'login_count', 'profile',
            'is_device_admin', 'is_device_owner', 'is_super_admin'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_login', 'login_count']


class UserUpdateSerializer(serializers.ModelSerializer):
    """用户信息更新序列化器（用户自己更新）"""

    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'phone', 'department',
            'employee_id', 'avatar'
        ]


class AdminUserUpdateSerializer(serializers.ModelSerializer):
    """管理员用户更新序列化器"""

    class Meta:
        model = User
        fields = [
            'email', 'first_name', 'last_name', 'phone', 'role',
            'department', 'employee_id', 'is_active'
        ]

    def validate_role(self, value):
        """验证角色修改权限"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            # 只有超级管理员可以修改超级管理员角色
            if value == 'super_admin' and request.user.role != 'super_admin':
                raise serializers.ValidationError('只有超级管理员可以设置超级管理员角色')

            # 检查是否试图修改超级管理员的角色
            if self.instance and self.instance.role == 'super_admin' and request.user.role != 'super_admin':
                raise serializers.ValidationError('只有超级管理员可以修改超级管理员的角色')

        return value

    def validate_is_active(self, value):
        """验证状态修改权限"""
        # 超级管理员不能被禁用
        if self.instance and self.instance.role == 'super_admin' and not value:
            raise serializers.ValidationError('超级管理员账户不能被禁用')

        return value


class UserStatusUpdateSerializer(serializers.ModelSerializer):
    """用户状态更新序列化器（仅用于启用/禁用操作）"""

    class Meta:
        model = User
        fields = ['is_active']

    def validate_is_active(self, value):
        """验证状态修改权限"""
        # 超级管理员不能被禁用
        if self.instance and self.instance.role == 'super_admin' and not value:
            raise serializers.ValidationError('超级管理员账户不能被禁用')

        return value


class PasswordChangeSerializer(serializers.Serializer):
    """密码修改序列化器"""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('原密码错误')
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError('新密码确认不匹配')
        return attrs
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class LoginLogSerializer(serializers.ModelSerializer):
    """登录日志序列化器"""
    
    user_display = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = LoginLog
        fields = [
            'id', 'user_display', 'ip_address', 'user_agent',
            'login_time', 'success', 'failure_reason'
        ]


class UserListSerializer(serializers.ModelSerializer):
    """用户列表序列化器"""

    role_display = serializers.CharField(source='get_role_display', read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'role', 'role_display', 'department', 'is_active',
            'created_at', 'last_login'
        ]


class DeviceOwnerOptionsSerializer(serializers.ModelSerializer):
    """设备归属者选项序列化器（受限用户信息）"""

    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'first_name', 'last_name', 'full_name',
            'department', 'role', 'is_active'
        ]

    def get_full_name(self, obj):
        """获取用户全名"""
        if obj.first_name and obj.last_name:
            return f"{obj.first_name} {obj.last_name}"
        return obj.username
