"""
用户相关URL配置
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views
from .views import (
    UserRegistrationView, UserLoginView, UserLogoutView,
    UserProfileView, UserUpdateView, PasswordChangeView,
    AvatarUploadView, UserListView, UserDetailView, LoginLogListView,
    DeviceOwnerOptionsView
)

app_name = 'users'

urlpatterns = [
    # 认证相关
    path('register/', UserRegistrationView.as_view(), name='register'),
    path('login/', UserLoginView.as_view(), name='login'),
    path('logout/', UserLogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # 用户管理
    path('profile/', UserProfileView.as_view(), name='profile'),
    path('update/', UserUpdateView.as_view(), name='update'),
    path('change-password/', PasswordChangeView.as_view(), name='change_password'),
    path('upload-avatar/', AvatarUploadView.as_view(), name='upload_avatar'),
    
    # 用户列表和详情
    path('', UserListView.as_view(), name='user_list'),
    path('device-owner-options/', DeviceOwnerOptionsView.as_view(), name='device_owner_options'),
    path('<uuid:pk>/', UserDetailView.as_view(), name='user_detail'),
    path('<uuid:pk>/reset-password/', views.reset_user_password, name='reset_password'),
    
    # 登录日志
    path('login-logs/', LoginLogListView.as_view(), name='login_logs'),
]
