# Generated by Django 4.2.7 on 2025-07-27 12:49

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱地址')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='手机号码')),
                ('role', models.CharField(choices=[('super_admin', '超级管理员'), ('device_admin', '设备管理员'), ('device_owner', '设备归属者'), ('normal_user', '普通用户')], default='normal_user', max_length=20, verbose_name='用户角色')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='部门')),
                ('employee_id', models.CharField(blank=True, max_length=50, unique=True, verbose_name='员工编号')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='最后登录IP')),
                ('login_count', models.PositiveIntegerField(default=0, verbose_name='登录次数')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0, verbose_name='失败登录次数')),
                ('locked_until', models.DateTimeField(blank=True, null=True, verbose_name='锁定到')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(blank=True, verbose_name='个人简介')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='所在地')),
                ('website', models.URLField(blank=True, verbose_name='个人网站')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='邮件通知')),
                ('sms_notifications', models.BooleanField(default=False, verbose_name='短信通知')),
                ('wechat_notifications', models.BooleanField(default=True, verbose_name='微信通知')),
                ('language', models.CharField(default='zh-hans', max_length=10, verbose_name='语言')),
                ('timezone', models.CharField(default='Asia/Shanghai', max_length=50, verbose_name='时区')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='登录时间')),
                ('success', models.BooleanField(default=True, verbose_name='是否成功')),
                ('failure_reason', models.CharField(blank=True, max_length=200, verbose_name='失败原因')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='login_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'login_logs',
                'ordering': ['-login_time'],
            },
        ),
    ]
