"""
用户模型定义
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
import uuid


class User(AbstractUser):
    """扩展的用户模型"""
    
    ROLE_CHOICES = [
        ('super_admin', '超级管理员'),
        ('device_admin', '设备管理员'),
        ('device_owner', '设备归属者'),
        ('normal_user', '普通用户'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(_('邮箱地址'), unique=True)
    phone = models.CharField(_('手机号码'), max_length=20, blank=True)
    role = models.CharField(_('用户角色'), max_length=20, choices=ROLE_CHOICES, default='normal_user')
    department = models.CharField(_('部门'), max_length=100, blank=True)
    employee_id = models.Char<PERSON><PERSON>(_('员工编号'), max_length=50, blank=True, null=True, unique=True)
    avatar = models.ImageField(_('头像'), upload_to='avatars/', blank=True, null=True)
    
    # 扩展字段
    is_active = models.BooleanField(_('是否激活'), default=True)
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    last_login_ip = models.GenericIPAddressField(_('最后登录IP'), blank=True, null=True)
    login_count = models.PositiveIntegerField(_('登录次数'), default=0)
    
    # 权限相关
    failed_login_attempts = models.PositiveIntegerField(_('失败登录次数'), default=0)
    locked_until = models.DateTimeField(_('锁定到'), blank=True, null=True)
    
    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']
    
    class Meta:
        db_table = 'users'
        verbose_name = _('用户')
        verbose_name_plural = _('用户')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    @property
    def is_super_admin(self):
        """是否为超级管理员"""
        return self.role == 'super_admin' or self.is_superuser
    
    @property
    def is_device_admin(self):
        """是否为设备管理员"""
        return self.role in ['super_admin', 'device_admin'] or self.is_superuser
    
    @property
    def is_device_owner(self):
        """是否为设备归属者"""
        return self.role in ['super_admin', 'device_admin', 'device_owner'] or self.is_superuser
    
    def can_manage_device(self, device):
        """是否可以管理指定设备"""
        if self.is_device_admin:
            return True
        if self.role == 'device_owner':
            return device.owner == self
        return False
    
    def get_managed_devices(self):
        """获取用户管理的设备"""
        if self.is_device_admin:
            from apps.devices.models import Device
            return Device.objects.filter(is_deleted=False)
        elif self.role == 'device_owner':
            return self.owned_devices.filter(is_deleted=False)
        return self.owned_devices.none()


class UserProfile(models.Model):
    """用户扩展信息"""
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(_('个人简介'), blank=True)
    location = models.CharField(_('所在地'), max_length=100, blank=True)
    website = models.URLField(_('个人网站'), blank=True)
    
    # 通知设置
    email_notifications = models.BooleanField(_('邮件通知'), default=True)
    sms_notifications = models.BooleanField(_('短信通知'), default=False)
    wechat_notifications = models.BooleanField(_('微信通知'), default=True)
    
    # 偏好设置
    language = models.CharField(_('语言'), max_length=10, default='zh-hans')
    timezone = models.CharField(_('时区'), max_length=50, default='Asia/Shanghai')
    
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = _('用户资料')
        verbose_name_plural = _('用户资料')
    
    def __str__(self):
        return f"{self.user.username}的资料"


class LoginLog(models.Model):
    """登录日志"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_logs')
    ip_address = models.GenericIPAddressField(_('IP地址'))
    user_agent = models.TextField(_('用户代理'))
    login_time = models.DateTimeField(_('登录时间'), auto_now_add=True)
    success = models.BooleanField(_('是否成功'), default=True)
    failure_reason = models.CharField(_('失败原因'), max_length=200, blank=True)
    
    class Meta:
        db_table = 'login_logs'
        verbose_name = _('登录日志')
        verbose_name_plural = _('登录日志')
        ordering = ['-login_time']
    
    def __str__(self):
        status = '成功' if self.success else '失败'
        return f"{self.user.username} - {status} - {self.login_time}"
