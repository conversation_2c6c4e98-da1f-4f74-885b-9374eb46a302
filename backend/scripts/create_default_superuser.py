#!/usr/bin/env python
"""
创建默认超级管理员用户的标准脚本
此脚本应该集成到容器启动流程中，确保每次启动都有可用的管理员账户
"""
import os
import sys
import django
from django.core.management.base import BaseCommand

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

# 默认超级用户配置
DEFAULT_SUPERUSER = {
    'username': 'admin',
    'email': '<EMAIL>',
    'password': 'admin123456',
    'first_name': '系统',
    'last_name': '管理员'
}

def create_default_superuser():
    """
    创建默认超级管理员用户
    如果用户已存在，则跳过创建
    """
    username = DEFAULT_SUPERUSER['username']
    
    try:
        with transaction.atomic():
            # 检查用户是否已存在
            if User.objects.filter(username=username).exists():
                user = User.objects.get(username=username)
                print(f"✅ 超级用户 '{username}' 已存在")
                print(f"   用户信息: {user.username} ({user.email})")
                
                # 确保用户是超级用户
                if not user.is_superuser:
                    user.is_superuser = True
                    user.is_staff = True
                    user.save()
                    print(f"   已更新用户权限为超级管理员")
                
                return user
            
            # 创建新的超级用户
            user = User.objects.create_superuser(
                username=DEFAULT_SUPERUSER['username'],
                email=DEFAULT_SUPERUSER['email'],
                password=DEFAULT_SUPERUSER['password'],
                first_name=DEFAULT_SUPERUSER['first_name'],
                last_name=DEFAULT_SUPERUSER['last_name']
            )
            
            print(f"🎉 超级用户创建成功!")
            print(f"   用户名: {DEFAULT_SUPERUSER['username']}")
            print(f"   邮箱: {DEFAULT_SUPERUSER['email']}")
            print(f"   密码: {DEFAULT_SUPERUSER['password']}")
            print(f"   管理后台: http://localhost:8000/admin/")
            print(f"   ⚠️  请在生产环境中修改默认密码!")
            
            return user
            
    except Exception as e:
        print(f"❌ 创建超级用户失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 初始化默认超级管理员用户")
    print("=" * 50)
    
    # 等待数据库就绪
    from django.db import connection
    try:
        connection.ensure_connection()
        print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)
    
    # 创建超级用户
    create_default_superuser()
    
    print("=" * 50)
    print("✅ 超级用户初始化完成")
    print("=" * 50)

if __name__ == '__main__':
    main()
