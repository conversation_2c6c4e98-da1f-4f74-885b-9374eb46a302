#!/usr/bin/env python3
"""
Celery Beat健康检查脚本
用于检查Celery Beat调度器的健康状态
"""

import os
import sys
import time
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def check_celery_beat_health():
    """检查Celery Beat健康状态"""
    try:
        # 检查Celery Beat进程是否在运行
        import subprocess

        # 使用ps命令检查celery beat进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        if 'celery' in result.stdout and 'beat' in result.stdout:
            print("✅ Celery Beat进程正在运行")
            return True
        else:
            print("❌ 未找到Celery Beat进程")
            return False

    except Exception as e:
        print(f"❌ 检查Celery Beat进程失败: {e}")
        # 如果ps命令失败，尝试简单的存活检查
        try:
            # 简单检查：如果能导入celery并且没有异常，认为是健康的
            from celery import Celery
            print("✅ Celery模块可正常导入")
            return True
        except Exception as e2:
            print(f"❌ Celery模块导入失败: {e2}")
            return False

def check_database_connection():
    """检查数据库连接"""
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result and result[0] == 1:
                print("✅ 数据库连接正常")
                return True
            else:
                print("❌ 数据库连接异常")
                return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_django_celery_beat():
    """检查django_celery_beat应用状态"""
    try:
        from django_celery_beat.models import PeriodicTask
        
        # 尝试查询定时任务
        task_count = PeriodicTask.objects.count()
        print(f"✅ django_celery_beat正常，共有 {task_count} 个定时任务")
        return True
        
    except Exception as e:
        print(f"❌ django_celery_beat检查失败: {e}")
        return False

def check_redis_connection():
    """检查Redis连接"""
    try:
        import redis
        from django.conf import settings
        
        # 从Django配置获取Redis URL
        redis_url = getattr(settings, 'CELERY_BROKER_URL', 'redis://redis:6379/0')
        
        # 解析Redis URL
        if redis_url.startswith('redis://'):
            r = redis.from_url(redis_url)
            r.ping()
            print("✅ Redis连接正常")
            return True
        else:
            print("⚠️ 非Redis broker，跳过Redis检查")
            return True
            
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def main():
    """主健康检查函数"""
    print(f"🔧 Celery Beat健康检查开始 - {datetime.now()}")
    print("=" * 50)
    
    checks = [
        ("数据库连接", check_database_connection),
        ("Redis连接", check_redis_connection),
        ("django_celery_beat", check_django_celery_beat),
        ("Celery Beat调度器", check_celery_beat_health),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n🔍 检查 {check_name}...")
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✅ 所有健康检查通过")
        sys.exit(0)
    else:
        print("❌ 健康检查失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
