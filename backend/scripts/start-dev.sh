#!/bin/bash

# 企业级Django开发环境启动脚本
# 作者: CEO级别技术团队
# 版本: v1.0.0

set -e  # 遇到错误立即退出

echo "🚀 启动设备管理平台后端服务..."

# 等待数据库就绪
echo "⏳ 等待数据库连接..."
python manage.py wait_for_db

# 运行数据库迁移
echo "📊 执行数据库迁移..."
python manage.py migrate --noinput

# 收集静态文件
echo "📁 收集静态文件..."
python manage.py collectstatic --noinput

# 创建超级用户（如果不存在）
echo "👤 检查超级用户..."
python manage.py shell << EOF
from apps.users.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        first_name='系统管理员',
        employee_id='ADMIN001'
    )
    print("✅ 创建默认超级用户: admin/admin123")
else:
    print("✅ 超级用户已存在")
EOF

# 启动开发服务器
echo "🌟 启动Django开发服务器..."
exec python manage.py runserver 0.0.0.0:8000
