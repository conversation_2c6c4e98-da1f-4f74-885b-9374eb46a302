# Prometheus告警规则
groups:
  # 应用服务告警
  - name: mdm_application
    rules:
      # 服务不可用
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 不可用"
          description: "服务 {{ $labels.instance }} 已经停止响应超过1分钟"

      # HTTP错误率过高
      - alert: HighErrorRate
        expr: rate(django_http_responses_total{status=~"5.."}[5m]) / rate(django_http_responses_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP错误率过高"
          description: "5xx错误率超过10%，当前值: {{ $value | humanizePercentage }}"

      # 响应时间过长
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(django_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95%的请求响应时间超过2秒，当前值: {{ $value }}s"

  # 系统资源告警
  - name: mdm_system
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

      # 磁盘空间不足
      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 磁盘使用率超过85%，当前值: {{ $value }}%"

  # 数据库告警
  - name: mdm_database
    rules:
      # 数据库连接数过多
      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过多"
          description: "数据库连接数超过80，当前值: {{ $value }}"

      # 数据库慢查询
      - alert: SlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库查询效率低"
          description: "数据库查询效率低于10%，可能存在慢查询"

  # Redis告警
  - name: mdm_redis
    rules:
      # Redis内存使用率过高
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过80%，当前值: {{ $value }}%"

      # Redis连接数过多
      - alert: HighRedisConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis连接数过多"
          description: "Redis连接数超过100，当前值: {{ $value }}"

  # 业务指标告警
  - name: mdm_business
    rules:
      # 用户登录失败率过高
      - alert: HighLoginFailureRate
        expr: rate(django_http_responses_total{view="login",status="400"}[5m]) / rate(django_http_responses_total{view="login"}[5m]) > 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "用户登录失败率过高"
          description: "登录失败率超过30%，可能存在安全问题"

      # API调用量异常
      - alert: UnusualAPITraffic
        expr: rate(django_http_requests_total[5m]) > 100 or rate(django_http_requests_total[5m]) < 1
        for: 10m
        labels:
          severity: info
        annotations:
          summary: "API调用量异常"
          description: "API调用量异常，当前QPS: {{ $value }}"
