# ===========================================
# 开发环境配置文件
# ===========================================

# ===========================================
# 基础配置
# ===========================================
NODE_ENV=development
DEBUG=True

# 应用密钥 (开发环境)
SECRET_KEY=dev-secret-key-change-in-production

# 允许的主机
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,nginx,backend

# CORS允许的源
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://frontend:3000

# ===========================================
# 数据库配置 (开发环境)
# ===========================================
POSTGRES_DB=mdm_dev
POSTGRES_USER=mdm_user
POSTGRES_PASSWORD=mdm_password
DATABASE_URL=******************************************/mdm_dev

# ===========================================
# 缓存配置 (开发环境)
# ===========================================
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379/0

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:8000

# ===========================================
# 邮件配置 (开发环境 - 使用MailHog)
# ===========================================
EMAIL_HOST=mailhog
EMAIL_PORT=1025
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
EMAIL_USE_TLS=False
DEFAULT_FROM_EMAIL=<EMAIL>

# ===========================================
# 开发工具配置
# ===========================================
# pgAdmin配置
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

# 端口配置
POSTGRES_PORT=5432
REDIS_PORT=6379
BACKEND_PORT=8000
FRONTEND_PORT=3000

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=DEBUG

# ===========================================
# Celery配置
# ===========================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
