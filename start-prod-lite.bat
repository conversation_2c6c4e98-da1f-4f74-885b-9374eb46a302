@echo off
REM 设备管理平台生产环境启动脚本 (精简版)
REM 适用于预发布环境和快速验收测试

echo ========================================
echo 设备管理平台 - 生产环境启动 (精简版)
echo ========================================
echo.

echo [1/4] 停止现有服务...
docker compose -f docker-compose.prod-lite.yml down --remove-orphans

echo [2/4] 清理Docker资源...
docker system prune -f

echo [3/4] 启动精简生产环境...
docker compose -f docker-compose.prod-lite.yml up -d --build

echo [4/4] 等待服务就绪...
timeout /t 20 /nobreak > nul

echo.
echo ========================================
echo 精简生产环境启动完成
echo ========================================
echo.

echo 服务状态:
docker compose -f docker-compose.prod-lite.yml ps

echo.
echo 访问地址:
echo - 系统入口: http://localhost (通过Nginx)
echo - 前端应用: http://localhost:3000 (直接访问)
echo - 后端API: http://localhost:8000/api/ (直接访问)
echo - 管理后台: http://localhost/admin/ (通过Nginx)
echo - API文档: http://localhost/api/docs/ (通过Nginx)
echo.

echo 数据库连接:
echo - 主机: localhost
echo - 端口: 5432
echo - 数据库: mdm_prod
echo - 用户: mdm_prod_user
echo.

echo 日志查看:
echo - 所有服务: docker compose -f docker-compose.prod-lite.yml logs -f
echo - 后端服务: docker compose -f docker-compose.prod-lite.yml logs -f backend
echo - 前端服务: docker compose -f docker-compose.prod-lite.yml logs -f frontend
echo - Nginx日志: docker compose -f docker-compose.prod-lite.yml logs -f nginx
echo.

echo ========================================
echo 精简生产环境已就绪，可以开始验收测试
echo ========================================

pause
