@echo off
setlocal enabledelayedexpansion

REM ===========================================
REM Celery Beat数据库迁移脚本 (Windows版本)
REM ===========================================
REM 用途: 为django_celery_beat应用创建数据库表
REM 适用: 所有环境 (dev/prod/prod-lite)
REM ===========================================

echo [INFO] Celery Beat数据库迁移脚本

REM 检查参数
if "%1"=="" (
    echo [ERROR] 请指定环境: dev, prod, 或 prod-lite
    echo 用法: %0 ^<environment^>
    echo 示例: %0 dev
    exit /b 1
)

set ENVIRONMENT=%1

REM 验证环境参数
if "%ENVIRONMENT%"=="dev" goto :valid_env
if "%ENVIRONMENT%"=="prod" goto :valid_env
if "%ENVIRONMENT%"=="prod-lite" goto :valid_env

echo [ERROR] 无效的环境参数: %ENVIRONMENT%
echo [ERROR] 支持的环境: dev, prod, prod-lite
exit /b 1

:valid_env
echo [INFO] 开始为 %ENVIRONMENT% 环境执行Celery Beat迁移...

REM 设置容器名称
if "%ENVIRONMENT%"=="dev" (
    set CONTAINER_NAME=mdm_backend_dev
    set COMPOSE_FILE=docker-compose.dev.yml
    set BEAT_CONTAINER=mdm_celery_beat_dev
)
if "%ENVIRONMENT%"=="prod" (
    set CONTAINER_NAME=mdm_backend_prod
    set COMPOSE_FILE=docker-compose.prod.yml
    set BEAT_CONTAINER=mdm_celery_beat_prod
)
if "%ENVIRONMENT%"=="prod-lite" (
    set CONTAINER_NAME=mdm_backend_prod_lite
    set COMPOSE_FILE=docker-compose.prod-lite.yml
    set BEAT_CONTAINER=mdm_prod_lite_celery_beat
)

REM 检查容器是否运行
echo [INFO] 检查容器状态...
docker ps | findstr %CONTAINER_NAME% >nul
if errorlevel 1 (
    echo [ERROR] 容器 %CONTAINER_NAME% 未运行
    echo [INFO] 请先启动环境: start-%ENVIRONMENT%.bat
    exit /b 1
)

echo [SUCCESS] 容器 %CONTAINER_NAME% 正在运行

REM 执行迁移
echo [INFO] 执行django_celery_beat数据库迁移...

REM 1. 创建迁移文件
echo [INFO] 1. 创建迁移文件...
docker exec %CONTAINER_NAME% python manage.py makemigrations django_celery_beat

REM 2. 应用迁移
echo [INFO] 2. 应用数据库迁移...
docker exec %CONTAINER_NAME% python manage.py migrate django_celery_beat

REM 3. 验证迁移
echo [INFO] 3. 验证迁移结果...
docker exec %CONTAINER_NAME% python manage.py shell -c "from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule; print('✅ django_celery_beat表已创建:'); print(f'  - PeriodicTask: {PeriodicTask.objects.count()} 个任务'); print(f'  - IntervalSchedule: {IntervalSchedule.objects.count()} 个间隔调度'); print(f'  - CrontabSchedule: {CrontabSchedule.objects.count()} 个Cron调度')"

REM 4. 重启celery-beat容器
echo [INFO] 4. 重启celery-beat容器...
docker ps | findstr %BEAT_CONTAINER% >nul
if not errorlevel 1 (
    echo [INFO] 重启 %BEAT_CONTAINER% 容器...
    docker restart %BEAT_CONTAINER%
    timeout /t 5 /nobreak >nul
    
    REM 检查容器状态
    docker ps | findstr %BEAT_CONTAINER% >nul
    if not errorlevel 1 (
        echo [SUCCESS] %BEAT_CONTAINER% 重启成功
    ) else (
        echo [WARNING] %BEAT_CONTAINER% 重启后未运行，请检查日志
        echo [INFO] 查看日志: docker logs %BEAT_CONTAINER%
    )
) else (
    echo [WARNING] %BEAT_CONTAINER% 容器未运行，请手动启动
)

echo [SUCCESS] Celery Beat迁移完成！
echo [INFO] 现在可以使用DatabaseScheduler进行定时任务管理

REM 显示使用说明
echo.
echo === 使用说明 ===
if "%ENVIRONMENT%"=="dev" (
    echo 1. 访问Django管理后台: http://localhost:8000/admin/
) else (
    echo 1. 访问Django管理后台: http://localhost/admin/
)
echo 2. 在 'DJANGO CELERY BEAT' 部分可以管理定时任务
echo 3. 创建周期性任务、间隔调度、Cron调度等
echo 4. 查看celery-beat日志: docker logs %BEAT_CONTAINER%
echo.

pause
