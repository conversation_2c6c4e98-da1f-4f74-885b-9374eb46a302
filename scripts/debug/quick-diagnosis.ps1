# 快速诊断脚本 - 生产环境验收
# 使用方法：在PowerShell中运行此脚本

Write-Host "=== MDM 生产环境快速诊断 ===" -ForegroundColor Green

# 1. 检查容器状态
Write-Host "`n1. 容器状态检查：" -ForegroundColor Yellow
docker compose -f docker-compose.simple-prod.yml ps

# 2. 检查服务健康状态
Write-Host "`n2. 服务健康检查：" -ForegroundColor Yellow

# 前端健康检查
Write-Host "前端服务 (http://localhost:3000)："
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ 状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ 前端服务异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 后端健康检查
Write-Host "后端服务 (http://localhost:8000)："
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/health/" -UseBasicParsing -TimeoutSec 5
    Write-Host "✅ 状态码: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "❌ 后端服务异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 检查最近的日志
Write-Host "`n3. 最近日志检查：" -ForegroundColor Yellow
Write-Host "后端日志 (最近10行)："
docker compose -f docker-compose.simple-prod.yml logs backend --tail 10

Write-Host "`n前端日志 (最近5行)："
docker compose -f docker-compose.simple-prod.yml logs frontend --tail 5

# 4. 检查数据库连接
Write-Host "`n4. 数据库连接检查：" -ForegroundColor Yellow
docker compose -f docker-compose.simple-prod.yml exec backend python manage.py check --database default

# 5. 检查端口占用
Write-Host "`n5. 端口占用检查：" -ForegroundColor Yellow
Write-Host "端口 3000 (前端)："
netstat -an | findstr ":3000"
Write-Host "端口 8000 (后端)："
netstat -an | findstr ":8000"

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Green
Write-Host "如有问题，请将以上输出信息提供给技术支持" -ForegroundColor Cyan
