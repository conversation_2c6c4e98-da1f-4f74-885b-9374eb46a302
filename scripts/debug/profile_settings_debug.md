# 个人信息和设置功能调试指南

## 问题描述
前端点击个人信息和设置菜单项时，没有任何请求发出，页面可能无法正常跳转或加载。

## 已修复的问题

### 1. API接口重复定义
**问题**: `frontend/src/api/auth.ts` 中存在重复的 `getLoginLogs` 方法定义
**解决**: 已删除重复的方法定义

### 2. 类型导入路径错误
**问题**: `frontend/src/views/Profile.vue` 中从不存在的路径导入类型
**解决**: 修改导入路径从 `@/types/auth` 改为 `@/api/auth`

### 3. 构建错误
**问题**: 由于上述问题导致前端构建失败
**解决**: 修复后重新构建成功

## 当前测试状态

### 服务状态
- ✅ 前端服务: http://localhost:3000 (正常运行)
- ✅ 后端服务: http://localhost:8000 (正常运行)
- ✅ 构建状态: 无错误

### 临时测试页面
为了排查问题，已创建简化的测试页面：
- `frontend/src/views/ProfileTest.vue` - 简化的个人资料测试页面
- 当前 `/profile` 路由指向测试页面

## 测试步骤

### 第一步：基本功能测试
1. 打开浏览器访问 http://localhost:3000
2. 登录系统（如果还没有登录）
3. 点击右上角用户头像
4. 点击"个人资料"菜单项
5. 观察是否跳转到 `/profile` 页面

**预期结果**: 
- 页面应该跳转到 `/profile`
- 显示"个人资料测试页面"
- 显示当前时间和用户信息

### 第二步：网络请求测试
1. 打开浏览器开发者工具 (F12)
2. 切换到 Network (网络) 标签
3. 点击"个人资料"菜单项
4. 观察是否有网络请求

**预期结果**:
- 应该看到页面路由变化
- 可能有API请求获取用户数据

### 第三步：控制台错误检查
1. 在开发者工具中切换到 Console (控制台) 标签
2. 点击"个人资料"和"设置"菜单项
3. 检查是否有错误信息

**预期结果**:
- 控制台应该显示 "ProfileTest 组件已挂载"
- 无红色错误信息

### 第四步：API测试
1. 在个人资料测试页面中
2. 点击"测试API调用"按钮
3. 观察API调用结果

**预期结果**:
- 显示成功消息
- 显示用户数据JSON

## 可能的问题和解决方案

### 问题1: 页面无法跳转
**症状**: 点击菜单项后URL没有变化
**可能原因**: 
- 路由配置错误
- 菜单事件处理函数未正确绑定

**排查方法**:
```javascript
// 在浏览器控制台中执行
console.log('当前路由:', window.location.pathname)
// 手动测试路由跳转
window.location.href = '/profile'
```

### 问题2: 页面跳转但显示空白
**症状**: URL变化但页面内容为空
**可能原因**:
- 组件加载失败
- 组件内部有JavaScript错误

**排查方法**:
1. 检查浏览器控制台错误
2. 检查Network标签是否有404错误
3. 查看组件是否正确导入

### 问题3: 组件加载但API请求失败
**症状**: 页面显示但无法获取数据
**可能原因**:
- 后端API接口问题
- 认证token过期
- 网络连接问题

**排查方法**:
```bash
# 测试后端API
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/auth/profile/
```

### 问题4: 权限问题
**症状**: 页面跳转到登录页
**可能原因**:
- 用户未登录
- Token过期
- 路由守卫配置问题

**排查方法**:
```javascript
// 检查认证状态
console.log('Access Token:', localStorage.getItem('access_token'))
console.log('Refresh Token:', localStorage.getItem('refresh_token'))
```

## 手动测试命令

### 测试前端路由
```bash
# 检查前端服务状态
docker compose -f docker-compose.dev.yml ps frontend

# 查看前端日志
docker compose -f docker-compose.dev.yml logs frontend --tail 20

# 重启前端服务
docker compose -f docker-compose.dev.yml restart frontend
```

### 测试后端API
```bash
# 检查后端服务状态
docker compose -f docker-compose.dev.yml ps backend

# 测试用户资料API
curl http://localhost:8000/api/auth/profile/

# 查看后端日志
docker compose -f docker-compose.dev.yml logs backend --tail 20
```

### 测试数据库连接
```bash
# 连接到数据库
docker compose -f docker-compose.dev.yml exec db psql -U mdm_user -d mdm_db

# 查询用户表
SELECT id, username, email, role FROM users LIMIT 5;
```

## 恢复正常功能

当测试完成后，恢复正常的Profile页面：

1. 修改路由配置：
```typescript
// frontend/src/router/index.ts
{
  path: '/profile',
  name: 'Profile',
  component: () => import('@/views/Profile.vue'),  // 改回原来的Profile.vue
  meta: { requiresAuth: true }
}
```

2. 重启前端服务：
```bash
docker compose -f docker-compose.dev.yml restart frontend
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 浏览器控制台的完整错误信息
2. Network标签中的请求详情
3. 后端服务日志
4. 具体的操作步骤和现象描述

---

**调试指南版本**: v1.0
**创建时间**: 2025-07-30
**适用系统**: MDM设备管理系统 v2.3+
