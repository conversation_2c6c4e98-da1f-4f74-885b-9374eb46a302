# 日志收集脚本 - 用于问题排查
# 使用方法：.\log-collector.ps1 [问题描述]

param(
    [string]$ProblemDescription = "未指定问题描述"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logDir = "logs/debug/$timestamp"

Write-Host "=== MDM 日志收集工具 ===" -ForegroundColor Green
Write-Host "问题描述: $ProblemDescription" -ForegroundColor Yellow
Write-Host "收集时间: $(Get-Date)" -ForegroundColor Yellow

# 创建日志目录
New-Item -ItemType Directory -Force -Path $logDir | Out-Null

# 1. 收集容器状态
Write-Host "`n收集容器状态..." -ForegroundColor Cyan
docker compose -f docker-compose.simple-prod.yml ps > "$logDir/container-status.txt"
docker ps -a > "$logDir/all-containers.txt"

# 2. 收集服务日志
Write-Host "收集服务日志..." -ForegroundColor Cyan
docker compose -f docker-compose.simple-prod.yml logs backend > "$logDir/backend.log"
docker compose -f docker-compose.simple-prod.yml logs frontend > "$logDir/frontend.log"
docker compose -f docker-compose.simple-prod.yml logs db > "$logDir/database.log"
docker compose -f docker-compose.simple-prod.yml logs redis > "$logDir/redis.log"

# 3. 收集系统信息
Write-Host "收集系统信息..." -ForegroundColor Cyan
@"
问题描述: $ProblemDescription
收集时间: $(Get-Date)
操作系统: $env:OS
计算机名: $env:COMPUTERNAME
用户名: $env:USERNAME
PowerShell版本: $($PSVersionTable.PSVersion)
"@ > "$logDir/system-info.txt"

# 4. 收集网络信息
Write-Host "收集网络信息..." -ForegroundColor Cyan
netstat -an | findstr ":3000\|:8000\|:5432\|:6379" > "$logDir/port-status.txt"

# 5. 收集Docker信息
Write-Host "收集Docker信息..." -ForegroundColor Cyan
docker version > "$logDir/docker-version.txt"
docker compose version > "$logDir/docker-compose-version.txt"

# 6. 创建问题报告
Write-Host "生成问题报告..." -ForegroundColor Cyan
@"
# MDM 问题报告

## 基本信息
- 问题描述: $ProblemDescription
- 收集时间: $(Get-Date)
- 日志目录: $logDir

## 快速检查清单
请在浏览器开发者工具中检查以下内容：

### 网络面板 (Network)
- [ ] API请求是否发送成功
- [ ] 请求URL是否正确
- [ ] 响应状态码
- [ ] 响应内容是否有错误信息

### 控制台面板 (Console)
- [ ] 是否有JavaScript错误
- [ ] 是否有网络请求错误
- [ ] 是否有认证相关错误

### 应用面板 (Application)
- [ ] Local Storage中是否有access_token
- [ ] token是否过期
- [ ] 用户信息是否正确

## 常见问题排查
1. 登录问题：检查用户名密码、token存储
2. 页面加载问题：检查静态资源加载、API连接
3. 功能异常：检查API请求响应、权限验证
4. 性能问题：检查网络延迟、资源大小

## 联系技术支持
请将此目录下的所有文件打包发送给技术支持：
$logDir
"@ > "$logDir/README.md"

Write-Host "`n=== 日志收集完成 ===" -ForegroundColor Green
Write-Host "日志保存位置: $logDir" -ForegroundColor Yellow
Write-Host "请将该目录下的文件提供给技术支持进行问题分析" -ForegroundColor Cyan
