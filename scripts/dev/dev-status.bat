@echo off
chcp 65001 >nul
echo === Development Status ===
echo.
echo Docker Services:
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=mdm_"
echo.
echo Access URLs:
echo - Frontend: http://localhost:5174 (local dev)
echo - Backend:  http://127.0.0.1:8000 (local dev)
echo - Database: localhost:5432 (postgres/postgres)
echo - Redis:    localhost:6379 (password: redis123)
echo.
echo Development modes:
echo - Fast: Use scripts\dev\start-services.bat + local apps
echo - Full: Use docker compose -f docker-compose.dev.yml up -d
