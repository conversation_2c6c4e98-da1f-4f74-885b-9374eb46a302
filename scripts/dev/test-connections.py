#!/usr/bin/env python3
"""
测试Docker容器中的PostgreSQL和Redis连接
"""
import sys
import time

def test_postgresql():
    """测试PostgreSQL连接"""
    try:
        import psycopg2
        
        conn_params = {
            'host': 'localhost',
            'port': 5432,
            'database': 'mdm_prod',
            'user': 'mdm_user',
            'password': 'mdm_prod_pass_2025'
        }
        
        print("🐘 测试PostgreSQL连接...")
        print(f"连接参数: {conn_params}")
        
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL连接成功!")
        print(f"版本: {version}")
        
        # 查看数据库信息
        cursor.execute("SELECT current_database(), current_user;")
        db_info = cursor.fetchone()
        print(f"当前数据库: {db_info[0]}")
        print(f"当前用户: {db_info[1]}")
        
        # 查看表数量
        cursor.execute("""
            SELECT count(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'public';
        """)
        table_count = cursor.fetchone()[0]
        print(f"表数量: {table_count}")
        
        cursor.close()
        conn.close()
        return True
        
    except ImportError:
        print("❌ 缺少psycopg2库，请安装: pip install psycopg2-binary")
        return False
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

def test_redis():
    """测试Redis连接"""
    try:
        import redis
        
        conn_params = {
            'host': 'localhost',
            'port': 6379,
            'db': 0,
            'decode_responses': True
        }
        
        print("\n🔴 测试Redis连接...")
        print(f"连接参数: {conn_params}")
        
        r = redis.Redis(**conn_params)
        
        # 测试连接
        pong = r.ping()
        print(f"✅ Redis连接成功! PING响应: {pong}")
        
        # 获取Redis信息
        info = r.info()
        print(f"Redis版本: {info['redis_version']}")
        print(f"运行模式: {info['redis_mode']}")
        print(f"已用内存: {info['used_memory_human']}")
        print(f"连接数: {info['connected_clients']}")
        
        # 测试读写
        test_key = "test_connection"
        test_value = f"test_value_{int(time.time())}"
        
        r.set(test_key, test_value, ex=60)  # 60秒过期
        retrieved_value = r.get(test_key)
        
        if retrieved_value == test_value:
            print(f"✅ Redis读写测试成功!")
            r.delete(test_key)  # 清理测试数据
        else:
            print(f"❌ Redis读写测试失败")
            
        return True
        
    except ImportError:
        print("❌ 缺少redis库，请安装: pip install redis")
        return False
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔗 Docker容器数据库连接测试")
    print("=" * 50)
    
    pg_success = test_postgresql()
    redis_success = test_redis()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"PostgreSQL: {'✅ 成功' if pg_success else '❌ 失败'}")
    print(f"Redis: {'✅ 成功' if redis_success else '❌ 失败'}")
    
    if pg_success and redis_success:
        print("\n🎉 所有连接测试通过!")
        return 0
    else:
        print("\n⚠️  部分连接测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
