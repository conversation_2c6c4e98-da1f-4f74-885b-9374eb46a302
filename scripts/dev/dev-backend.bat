@echo off
cd backend

if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to create virtual environment!
        pause
        exit /b 1
    )
)

echo Activating virtual environment...
call venv\Scripts\activate

echo Upgrading pip...
python -m pip install --upgrade pip

echo Installing dependencies...
pip install -r requirements.txt
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install dependencies!
    echo Trying to install with --no-cache-dir...
    pip install --no-cache-dir -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo Installation failed! Please check the error above.
        pause
        exit /b 1
    )
)

echo Setting environment variables...
set DEBUG=True
set SECRET_KEY=dev-secret-key
set DB_NAME=mdm_dev
set DB_USER=postgres
set DB_PASSWORD=postgres
set DB_HOST=localhost
set DB_PORT=5432
set REDIS_URL=redis://:redis123@localhost:6379/0
set ALLOWED_HOSTS=localhost,127.0.0.1

echo Checking database connection...
echo Database: postgresql://postgres:postgres@localhost:5432/mdm_dev
echo Redis: redis://:redis123@localhost:6379/0

echo Running migrations...
python manage.py migrate
if %ERRORLEVEL% NEQ 0 (
    echo Migration failed! Please check database connection.
    pause
    exit /b 1
)

echo Starting backend server...
echo Backend will be available at: http://127.0.0.1:8000
echo API docs will be available at: http://127.0.0.1:8000/api/docs/
echo.
python manage.py runserver 127.0.0.1:8000
