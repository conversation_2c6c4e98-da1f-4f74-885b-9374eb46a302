@echo off
chcp 65001 >nul
echo === Quick Development Start (5-Second Rule) ===
echo.

REM Check file structure
call scripts\dev\check-file-structure.bat

echo.
echo Starting development environment...

REM 1. Start basic services (Docker)
echo [1/3] Starting database and cache services...
docker compose -f scripts/dev/docker-services.yml up -d
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to start services!
    pause
    exit /b 1
)

REM 2. Wait for services to be ready
echo [2/3] Waiting for services to be ready...
timeout /t 3 /nobreak >nul

REM 3. Verify service health
echo [3/3] Verifying service health...
docker exec mdm_db_dev pg_isready -U postgres >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Database: Ready
) else (
    echo [FAIL] Database: Not ready
)

docker exec mdm_redis_dev redis-cli -a redis123 ping >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Redis: Ready
) else (
    echo [FAIL] Redis: Not ready
)

echo.
echo [SUCCESS] Quick start complete! Now run:
echo.
echo Backend:  scripts\dev\dev-backend.bat
echo Frontend: cd frontend ^&^& npm run dev
echo.
echo Monitor: scripts\dev\health-check.bat
echo Status:  scripts\dev\dev-status.bat
