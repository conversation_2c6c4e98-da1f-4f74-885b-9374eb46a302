# 🔗 数据库连接配置指南

## PostgreSQL 连接配置

### DBeaver 连接设置
```
连接类型: PostgreSQL
服务器主机: localhost
端口: 5432
数据库: mdm_prod
用户名: mdm_user
密码: mdm_prod_pass_2025
```

### pgAdmin 连接设置
```
主机名/地址: localhost
端口: 5432
维护数据库: mdm_prod
用户名: mdm_user
密码: mdm_prod_pass_2025
```

### DataGrip 连接设置
```
Host: localhost
Port: 5432
Database: mdm_prod
User: mdm_user
Password: mdm_prod_pass_2025
URL: *****************************************
```

### 命令行连接
```bash
# psql 连接
psql -h localhost -p 5432 -U mdm_user -d mdm_prod

# 使用连接字符串
postgresql://mdm_user:mdm_prod_pass_2025@localhost:5432/mdm_prod
```

## Redis 连接配置

### Redis Desktop Manager
```
名称: MDM Redis Dev
地址: localhost
端口: 6379
认证: 无
```

### RedisInsight 连接设置
```
主机: localhost
端口: 6379
用户名: (留空)
密码: (留空)
```

### Another Redis Desktop Manager
```
连接名: MDM Dev Redis
主机: 127.0.0.1
端口: 6379
密码: (留空)
```

### 命令行连接
```bash
# redis-cli 连接
redis-cli -h localhost -p 6379

# 测试连接
redis-cli -h localhost -p 6379 ping
```

## 连接字符串格式

### PostgreSQL
```
# Django 格式
postgresql://mdm_user:mdm_prod_pass_2025@localhost:5432/mdm_prod

# SQLAlchemy 格式
postgresql+psycopg2://mdm_user:mdm_prod_pass_2025@localhost:5432/mdm_prod
```

### Redis
```
# 标准格式
redis://localhost:6379/0

# 带认证格式（如果有密码）
redis://:password@localhost:6379/0
```

## 环境变量配置

### .env 文件格式
```env
# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=mdm_prod
POSTGRES_USER=mdm_user
POSTGRES_PASSWORD=mdm_prod_pass_2025

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_URL=redis://localhost:6379/0
```

## 常见问题解决

### 1. 连接被拒绝
- 确认Docker容器正在运行: `docker ps`
- 检查端口映射是否正确
- 确认防火墙设置

### 2. 认证失败
- 检查用户名和密码是否正确
- 确认数据库是否存在

### 3. 网络问题
- 尝试使用 127.0.0.1 而不是 localhost
- 检查Docker网络配置

## 管理工具访问

### pgAdmin (已配置)
- URL: http://localhost:5050
- 邮箱: <EMAIL>
- 密码: admin

### Redis Commander (需启动)
```bash
# 启动Redis管理工具
docker-compose -f docker-compose.dev.yml --profile tools up redis-commander -d
```
- URL: http://localhost:8081

### MailHog (邮件测试，需启动)
```bash
# 启动邮件测试工具
docker-compose -f docker-compose.dev.yml --profile tools up mailhog -d
```
- URL: http://localhost:8025
