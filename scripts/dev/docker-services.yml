# 仅启动数据库和Redis服务
services:
  db:
    image: postgres:14-alpine
    container_name: mdm_db_dev
    environment:
      POSTGRES_DB: mdm_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    container_name: mdm_redis_dev
    command: redis-server --requirepass redis123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
