@echo off
echo Starting frontend development server...
echo.

REM Check if in correct directory
if not exist "frontend" (
    echo [ERROR] frontend directory not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

cd frontend

echo Installing dependencies...
npm install
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo Starting frontend server...
echo Frontend will be available at: http://localhost:5174
echo.
npm run dev
