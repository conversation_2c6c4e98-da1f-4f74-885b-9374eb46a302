#!/usr/bin/env python3
"""
快速连接Docker容器中的数据库
提供交互式数据库操作界面
"""
import sys
import json
from datetime import datetime

def connect_postgresql():
    """连接PostgreSQL并提供交互式查询"""
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='mdm_prod',
            user='mdm_user',
            password='mdm_prod_pass_2025'
        )
        
        print("🐘 已连接到PostgreSQL")
        print("=" * 50)
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        while True:
            try:
                query = input("\nSQL> ").strip()
                
                if query.lower() in ['exit', 'quit', 'q']:
                    break
                elif query.lower() == 'help':
                    print_pg_help()
                    continue
                elif query.lower() == 'tables':
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = 'public'
                        ORDER BY table_name;
                    """)
                    tables = cursor.fetchall()
                    print(f"\n📋 数据库表 ({len(tables)}个):")
                    for table in tables:
                        cursor.execute(f"SELECT count(*) FROM {table['table_name']};")
                        count = cursor.fetchone()[0]
                        print(f"  • {table['table_name']} ({count} 行)")
                    continue
                elif not query:
                    continue
                
                cursor.execute(query)
                
                if cursor.description:
                    results = cursor.fetchall()
                    if results:
                        print(f"\n📊 查询结果 ({len(results)} 行):")
                        for i, row in enumerate(results[:10]):  # 只显示前10行
                            print(f"  {i+1}: {dict(row)}")
                        if len(results) > 10:
                            print(f"  ... 还有 {len(results)-10} 行")
                    else:
                        print("📭 查询无结果")
                else:
                    conn.commit()
                    print("✅ 命令执行成功")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
                conn.rollback()
        
        cursor.close()
        conn.close()
        
    except ImportError:
        print("❌ 缺少psycopg2库，请运行: pip install psycopg2-binary")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def connect_redis():
    """连接Redis并提供交互式操作"""
    try:
        import redis
        
        r = redis.Redis(
            host='localhost',
            port=6379,
            db=0,
            decode_responses=True
        )
        
        print("🔴 已连接到Redis")
        print("=" * 50)
        
        while True:
            try:
                command = input("\nRedis> ").strip()
                
                if command.lower() in ['exit', 'quit', 'q']:
                    break
                elif command.lower() == 'help':
                    print_redis_help()
                    continue
                elif command.lower() == 'info':
                    info = r.info()
                    print(f"\n📊 Redis信息:")
                    print(f"  版本: {info['redis_version']}")
                    print(f"  内存使用: {info['used_memory_human']}")
                    print(f"  连接数: {info['connected_clients']}")
                    print(f"  键总数: {r.dbsize()}")
                    continue
                elif command.lower() == 'keys':
                    keys = r.keys('*')
                    print(f"\n🔑 所有键 ({len(keys)}个):")
                    for key in keys[:20]:  # 只显示前20个
                        key_type = r.type(key)
                        print(f"  • {key} ({key_type})")
                    if len(keys) > 20:
                        print(f"  ... 还有 {len(keys)-20} 个键")
                    continue
                elif not command:
                    continue
                
                # 解析Redis命令
                parts = command.split()
                if len(parts) == 1:
                    result = getattr(r, parts[0].lower())()
                elif len(parts) == 2:
                    result = getattr(r, parts[0].lower())(parts[1])
                elif len(parts) == 3:
                    result = getattr(r, parts[0].lower())(parts[1], parts[2])
                else:
                    result = getattr(r, parts[0].lower())(*parts[1:])
                
                print(f"📤 结果: {result}")
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
        
    except ImportError:
        print("❌ 缺少redis库，请运行: pip install redis")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def print_pg_help():
    """打印PostgreSQL帮助信息"""
    print("""
📚 PostgreSQL 快速命令:
  tables          - 显示所有表
  \\d table_name   - 显示表结构
  SELECT * FROM users LIMIT 5;  - 查询示例
  help            - 显示此帮助
  exit/quit/q     - 退出
    """)

def print_redis_help():
    """打印Redis帮助信息"""
    print("""
📚 Redis 快速命令:
  info            - 显示Redis信息
  keys            - 显示所有键
  get key         - 获取键值
  set key value   - 设置键值
  del key         - 删除键
  help            - 显示此帮助
  exit/quit/q     - 退出
    """)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🔗 Docker容器数据库快速连接工具")
        print("使用方法:")
        print("  python quick-connect.py pg    - 连接PostgreSQL")
        print("  python quick-connect.py redis - 连接Redis")
        return
    
    db_type = sys.argv[1].lower()
    
    if db_type in ['pg', 'postgres', 'postgresql']:
        connect_postgresql()
    elif db_type in ['redis', 'r']:
        connect_redis()
    else:
        print("❌ 不支持的数据库类型，请使用 'pg' 或 'redis'")

if __name__ == "__main__":
    main()
