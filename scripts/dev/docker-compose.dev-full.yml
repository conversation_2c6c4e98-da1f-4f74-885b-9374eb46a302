services:
  # PostgreSQL数据库
  db:
    image: postgres:14-alpine
    container_name: mdm_db_dev
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mdm_dev}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - mdm_network_dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:6-alpine
    container_name: mdm_redis_dev
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data_dev:/data
    ports:
      - "6379:6379"
    networks:
      - mdm_network_dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis123}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django后端应用
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mdm_backend_dev
    environment:
      - DEBUG=${DEBUG:-True}
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-mdm_dev}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,backend}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}
    volumes:
      - ./backend:/app
      - static_volume_dev:/app/staticfiles
      - media_volume_dev:/app/media
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    networks:
      - mdm_network_dev
    restart: unless-stopped

  # Vue.js前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=${NODE_ENV:-development}
        - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000}
    container_name: mdm_frontend_dev
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - mdm_network_dev
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://localhost:8000}
      - DOCKER_ENV=true

volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  static_volume_dev:
    driver: local
  media_volume_dev:
    driver: local

networks:
  mdm_network_dev:
    driver: bridge
