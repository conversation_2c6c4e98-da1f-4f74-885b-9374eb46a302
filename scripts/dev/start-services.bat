@echo off
chcp 65001 >nul
echo Starting database and Redis services...
docker compose -f scripts/dev/docker-services.yml up -d

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to start services!
    pause
    exit /b 1
)

echo.
echo Checking service status...
timeout /t 3 /nobreak >nul

docker compose -f scripts/dev/docker-services.yml ps

echo.
echo Service endpoints:
echo - Database: localhost:5432 (postgres/postgres)
echo - Redis: localhost:6379 (password: redis123)
echo.
echo Testing connections...

REM Test database connection
docker exec mdm_db_dev pg_isready -U postgres >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Database: Connected
) else (
    echo [FAIL] Database: Connection failed
)

REM Test Redis connection
docker exec mdm_redis_dev redis-cli -a redis123 ping >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Redis: Connected
) else (
    echo [FAIL] Redis: Connection failed
)

echo.
echo Next steps:
echo - Backend: scripts\dev\dev-backend.bat
echo - Frontend: cd frontend ^&^& npm run dev
echo - Status: scripts\dev\dev-status.bat
