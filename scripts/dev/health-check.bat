@echo off
chcp 65001 >nul
echo === MDM Development Environment Health Check ===
echo.

echo 1. Checking Docker containers...
docker ps --filter "name=mdm_" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo 2. Testing service connections...

REM Check if containers exist
docker inspect mdm_db_dev >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] Database container not found
    goto :check_backend
)

docker inspect mdm_redis_dev >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [FAIL] Redis container not found
    goto :check_backend
)

REM Test database connection
echo Testing database connection...
docker exec mdm_db_dev pg_isready -U postgres >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Database: Connected (localhost:5432)
) else (
    echo [FAIL] Database: Connection failed
)

REM Test Redis connection
echo Testing Redis connection...
docker exec mdm_redis_dev redis-cli -a redis123 ping >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Redis: Connected (localhost:6379)
) else (
    echo [FAIL] Redis: Connection failed
)

:check_backend
echo.
echo 3. Checking application services...

REM Check if backend is running
curl -s http://127.0.0.1:8000/api/health/ >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Backend: Running (http://127.0.0.1:8000)
) else (
    echo [FAIL] Backend: Not running or not responding
)

REM Check if frontend is running
curl -s http://localhost:5174 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Frontend: Running (http://localhost:5174)
) else (
    echo [FAIL] Frontend: Not running or not responding
)

echo.
echo 4. Service URLs:
echo - Frontend: http://localhost:5174
echo - Backend:  http://127.0.0.1:8000
echo - API Docs: http://127.0.0.1:8000/api/docs/
echo - Database: postgresql://postgres:postgres@localhost:5432/mdm_dev
echo - Redis:    redis://:redis123@localhost:6379/0

echo.
echo === Health Check Complete ===
