#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库查询示例脚本
此脚本展示如何在Python代码中执行PostgreSQL和Redis数据库查询
"""

import os
import sys
import django
import psycopg2
import redis
import json
from datetime import datetime

# 添加项目根目录到Python路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(BASE_DIR)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# 从环境变量或配置文件获取数据库连接信息
from django.conf import settings

# PostgreSQL连接信息
DB_NAME = settings.DATABASES['default']['NAME']
DB_USER = settings.DATABASES['default']['USER']
DB_PASSWORD = settings.DATABASES['default']['PASSWORD']
DB_HOST = settings.DATABASES['default']['HOST']
DB_PORT = settings.DATABASES['default']['PORT']

# Redis连接信息
REDIS_URL = settings.REDIS_URL

def postgres_examples():
    """PostgreSQL查询示例"""
    print("\n===== PostgreSQL查询示例 =====")
    
    # 连接PostgreSQL数据库
    conn = psycopg2.connect(
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT
    )
    
    # 创建游标
    cur = conn.cursor()
    
    try:
        # 示例1: 查询所有设备
        print("\n1. 查询所有设备:")
        cur.execute("SELECT id, name, model, serial_number, status FROM devices LIMIT 5")
        devices = cur.fetchall()
        for device in devices:
            print(f"ID: {device[0]}, 名称: {device[1]}, 型号: {device[2]}, 序列号: {device[3]}, 状态: {device[4]}")
        
        # 示例2: 查询设备状态统计
        print("\n2. 查询设备状态统计:")
        cur.execute("SELECT status, COUNT(*) FROM devices GROUP BY status")
        status_counts = cur.fetchall()
        for status_count in status_counts:
            print(f"状态: {status_count[0]}, 数量: {status_count[1]}")
        
        # 示例3: 查询特定品牌的设备
        print("\n3. 查询特定品牌的设备:")
        brand = "苹果"  # 可以替换为实际品牌
        cur.execute("SELECT id, name, model FROM devices WHERE brand = %s AND is_deleted = FALSE LIMIT 5", (brand,))
        brand_devices = cur.fetchall()
        for device in brand_devices:
            print(f"ID: {device[0]}, 名称: {device[1]}, 型号: {device[2]}")
        
        # 示例4: 查询设备及其分类信息
        print("\n4. 查询设备及其分类信息:")
        cur.execute("""
            SELECT d.id, d.name, d.model, c.name 
            FROM devices d
            LEFT JOIN device_categories c ON d.category_id = c.id
            WHERE d.is_deleted = FALSE
            LIMIT 5
        """)
        device_categories = cur.fetchall()
        for device in device_categories:
            print(f"ID: {device[0]}, 名称: {device[1]}, 型号: {device[2]}, 分类: {device[3] or '无分类'}")
        
        # 示例5: 使用参数化查询查找设备
        print("\n5. 使用参数化查询查找设备:")
        search_term = "%平板%"  # 可以替换为实际搜索词
        cur.execute("""
            SELECT id, name, model 
            FROM devices 
            WHERE name LIKE %s OR model LIKE %s
            LIMIT 5
        """, (search_term, search_term))
        search_results = cur.fetchall()
        for device in search_results:
            print(f"ID: {device[0]}, 名称: {device[1]}, 型号: {device[2]}")
            
    except Exception as e:
        print(f"PostgreSQL查询错误: {e}")
    finally:
        # 关闭游标和连接
        cur.close()
        conn.close()


def redis_examples():
    """Redis查询示例"""
    print("\n===== Redis查询示例 =====")
    
    # 连接Redis
    r = redis.from_url(REDIS_URL)
    
    try:
        # 示例1: 获取所有键
        print("\n1. 获取所有键:")
        keys = r.keys("*")
        print(f"键总数: {len(keys)}")
        for key in keys[:5]:  # 只显示前5个
            print(f"键: {key}")
        
        # 示例2: 查询Celery任务队列
        print("\n2. 查询Celery任务队列:")
        celery_keys = r.keys("celery*")
        print(f"Celery相关键数量: {len(celery_keys)}")
        for key in celery_keys[:5]:
            print(f"键: {key}")
        
        # 示例3: 查看Celery任务队列长度
        print("\n3. 查看Celery任务队列长度:")
        queue_length = r.llen("celery")
        print(f"队列长度: {queue_length}")
        
        # 示例4: 查询Django会话数据
        print("\n4. 查询Django会话数据:")
        session_keys = r.keys("django:session:*")
        print(f"会话数量: {len(session_keys)}")
        for key in session_keys[:3]:
            print(f"会话键: {key}")
        
        # 示例5: 获取系统信息
        print("\n5. 获取Redis系统信息:")
        info = r.info()
        print(f"Redis版本: {info.get('redis_version')}")
        print(f"连接的客户端数: {info.get('connected_clients')}")
        print(f"内存使用: {info.get('used_memory_human')}")
        
        # 示例6: 设置和获取键值
        print("\n6. 设置和获取键值:")
        test_key = "test:example:" + datetime.now().strftime("%Y%m%d%H%M%S")
        test_value = {"timestamp": datetime.now().isoformat(), "message": "测试消息"}
        r.set(test_key, json.dumps(test_value))
        print(f"设置键 {test_key}")
        
        retrieved_value = r.get(test_key)
        if retrieved_value:
            print(f"获取键值: {json.loads(retrieved_value)}")
        
        # 清理测试键
        r.delete(test_key)
        print(f"删除测试键: {test_key}")
        
    except Exception as e:
        print(f"Redis查询错误: {e}")
    finally:
        # 关闭Redis连接
        r.close()


def django_orm_examples():
    """Django ORM查询示例"""
    print("\n===== Django ORM查询示例 =====")
    
    try:
        # 导入模型
        from apps.devices.models import Device, DeviceCategory
        
        # 示例1: 查询所有设备
        print("\n1. 查询所有设备:")
        devices = Device.objects.filter(is_deleted=False)[:5]
        for device in devices:
            print(f"ID: {device.id}, 名称: {device.name}, 型号: {device.model}, 状态: {device.status}")
        
        # 示例2: 查询设备状态统计
        print("\n2. 查询设备状态统计:")
        from django.db.models import Count
        status_counts = Device.objects.values('status').annotate(count=Count('id'))
        for status in status_counts:
            print(f"状态: {status['status']}, 数量: {status['count']}")
        
        # 示例3: 查询可借用的设备
        print("\n3. 查询可借用的设备:")
        available_devices = Device.objects.filter(status='available', is_deleted=False)[:5]
        print(f"可借用设备数量: {available_devices.count()}")
        for device in available_devices:
            print(f"ID: {device.id}, 名称: {device.name}, 型号: {device.model}")
        
        # 示例4: 查询设备及其分类
        print("\n4. 查询设备及其分类:")
        devices_with_category = Device.objects.select_related('category').filter(is_deleted=False)[:5]
        for device in devices_with_category:
            category_name = device.category.name if device.category else "无分类"
            print(f"ID: {device.id}, 名称: {device.name}, 分类: {category_name}")
        
        # 示例5: 复杂查询 - 查询最近添加的每个分类的设备
        print("\n5. 复杂查询 - 查询最近添加的每个分类的设备:")
        from django.db.models import Max
        
        # 获取所有分类
        categories = DeviceCategory.objects.all()[:5]
        
        for category in categories:
            # 获取该分类下最新的设备
            latest_device = Device.objects.filter(
                category=category, 
                is_deleted=False
            ).order_by('-created_at').first()
            
            if latest_device:
                print(f"分类: {category.name}, 最新设备: {latest_device.name}, 添加时间: {latest_device.created_at}")
            else:
                print(f"分类: {category.name}, 无设备")
        
    except Exception as e:
        print(f"Django ORM查询错误: {e}")


if __name__ == "__main__":
    print("数据库查询示例脚本")
    print("=" * 50)
    
    # 执行PostgreSQL示例
    postgres_examples()
    
    # 执行Redis示例
    redis_examples()
    
    # 执行Django ORM示例
    django_orm_examples()
    
    print("\n" + "=" * 50)
    print("查询示例完成")