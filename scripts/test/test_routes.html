<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-link {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 15px;
            background-color: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .test-link:hover {
            background-color: #337ecc;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background-color: #f4f4f5;
            border: 1px solid #909399;
            color: #606266;
        }
    </style>
</head>
<body>
    <h1>MDM系统路由测试页面</h1>
    
    <div class="test-section">
        <h2>基本信息</h2>
        <div class="status info">
            <strong>前端地址:</strong> <span id="frontend-url">http://localhost:3000</span><br>
            <strong>当前时间:</strong> <span id="current-time"></span><br>
            <strong>测试状态:</strong> <span id="test-status">准备测试</span>
        </div>
    </div>

    <div class="test-section">
        <h2>路由测试链接</h2>
        <p>点击以下链接测试各个页面是否正常加载：</p>
        
        <h3>主要页面</h3>
        <a href="http://localhost:3000/" class="test-link" target="_blank">首页 (/)</a>
        <a href="http://localhost:3000/login" class="test-link" target="_blank">登录页 (/login)</a>
        
        <h3>功能页面（需要登录）</h3>
        <a href="http://localhost:3000/profile" class="test-link" target="_blank">个人资料 (/profile)</a>
        <a href="http://localhost:3000/settings" class="test-link" target="_blank">设置页面 (/settings)</a>
        <a href="http://localhost:3000/devices" class="test-link" target="_blank">设备列表 (/devices)</a>
        <a href="http://localhost:3000/loans" class="test-link" target="_blank">借用管理 (/loans)</a>
        <a href="http://localhost:3000/users" class="test-link" target="_blank">用户管理 (/users)</a>
        <a href="http://localhost:3000/reports" class="test-link" target="_blank">统计报表 (/reports)</a>
    </div>

    <div class="test-section">
        <h2>自动测试</h2>
        <button onclick="runAutoTest()" style="padding: 10px 20px; background-color: #67c23a; color: white; border: none; border-radius: 4px; cursor: pointer;">
            开始自动测试
        </button>
        <div id="auto-test-results"></div>
    </div>

    <div class="test-section">
        <h2>测试说明</h2>
        <div class="status info">
            <h4>测试步骤：</h4>
            <ol>
                <li>首先访问登录页面，使用测试账户登录</li>
                <li>登录成功后，点击用户头像下拉菜单</li>
                <li>分别点击"个人资料"和"设置"菜单项</li>
                <li>验证页面是否正确跳转和加载</li>
                <li>检查浏览器控制台是否有错误信息</li>
            </ol>
            
            <h4>预期结果：</h4>
            <ul>
                <li>✅ 点击"个人资料"应该跳转到 /profile 页面</li>
                <li>✅ 点击"设置"应该跳转到 /settings 页面</li>
                <li>✅ 页面应该正常加载，显示相应的内容</li>
                <li>✅ 浏览器控制台无错误信息</li>
                <li>✅ 网络请求正常，能够获取用户数据</li>
            </ul>
            
            <h4>常见问题排查：</h4>
            <ul>
                <li><strong>页面空白：</strong> 检查组件是否有语法错误</li>
                <li><strong>路由不工作：</strong> 检查路由配置是否正确</li>
                <li><strong>API请求失败：</strong> 检查后端服务是否正常</li>
                <li><strong>权限问题：</strong> 确保用户已登录且有相应权限</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>测试记录</h2>
        <div id="test-log">
            <div class="status info">
                测试日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        }
        
        // 记录测试日志
        function logTest(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
        }
        
        // 自动测试函数
        async function runAutoTest() {
            const resultsDiv = document.getElementById('auto-test-results');
            resultsDiv.innerHTML = '<div class="status info">正在运行自动测试...</div>';
            
            const testUrls = [
                { url: 'http://localhost:3000/', name: '首页' },
                { url: 'http://localhost:3000/login', name: '登录页' },
                { url: 'http://localhost:3000/profile', name: '个人资料页' },
                { url: 'http://localhost:3000/settings', name: '设置页面' }
            ];
            
            let results = [];
            
            for (const test of testUrls) {
                try {
                    logTest(`测试 ${test.name} (${test.url})`, 'info');
                    
                    const response = await fetch(test.url, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    
                    results.push({
                        name: test.name,
                        url: test.url,
                        status: 'success',
                        message: '服务响应正常'
                    });
                    
                    logTest(`✅ ${test.name} 测试通过`, 'success');
                } catch (error) {
                    results.push({
                        name: test.name,
                        url: test.url,
                        status: 'error',
                        message: error.message
                    });
                    
                    logTest(`❌ ${test.name} 测试失败: ${error.message}`, 'error');
                }
            }
            
            // 显示测试结果
            let resultHtml = '<h4>自动测试结果：</h4>';
            results.forEach(result => {
                const statusClass = result.status === 'success' ? 'success' : 'error';
                const statusIcon = result.status === 'success' ? '✅' : '❌';
                resultHtml += `
                    <div class="status ${statusClass}">
                        ${statusIcon} <strong>${result.name}</strong>: ${result.message}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = resultHtml;
            
            // 更新总体测试状态
            const successCount = results.filter(r => r.status === 'success').length;
            const totalCount = results.length;
            document.getElementById('test-status').textContent = 
                `${successCount}/${totalCount} 项测试通过`;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            logTest('测试页面已加载', 'success');
        });
        
        // 监听链接点击
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const url = this.href;
                const text = this.textContent;
                logTest(`点击测试链接: ${text} (${url})`, 'info');
            });
        });
    </script>
</body>
</html>
