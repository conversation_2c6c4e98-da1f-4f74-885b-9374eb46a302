@echo off
echo Testing frontend configuration...
echo.

if not exist "frontend" (
    echo ERROR: frontend directory not found!
    pause
    exit /b 1
)

cd frontend

echo Checking Node.js version...
node --version

echo.
echo Checking npm version...
npm --version

echo.
echo Checking if package.json exists...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ✗ package.json not found
    pause
    exit /b 1
)

echo.
echo Checking if vite.config.ts exists...
if exist "vite.config.ts" (
    echo ✓ vite.config.ts found
) else (
    echo ✗ vite.config.ts not found
    pause
    exit /b 1
)

echo.
echo Checking dependencies...
if exist "node_modules" (
    echo ✓ node_modules exists
    npm list vite --depth=0
    npm list vue --depth=0
) else (
    echo ✗ node_modules not found - run npm install first
)

echo.
echo Testing Vite configuration...
npx vite --version

echo.
echo Configuration test complete!
pause
