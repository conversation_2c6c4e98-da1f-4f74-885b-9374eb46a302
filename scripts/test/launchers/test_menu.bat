@echo off
echo === MDM Testing Tools ===
echo.
echo Available tests:
echo 1. API Test
echo 2. Create Test Data
echo 3. Create Test User
echo 4. Manual Test (HTML)
echo 0. Exit
echo.
set /p choice="Enter choice (0-4): "

if "%choice%"=="1" goto api_test
if "%choice%"=="2" goto test_data
if "%choice%"=="3" goto test_user
if "%choice%"=="4" goto manual_test
if "%choice%"=="0" goto exit

:api_test
python scripts\test\api\quick_api_test.py
goto end

:test_data
python scripts\test\data\create_test_data.py
goto end

:test_user
python scripts\test\data\create_test_user.py
goto end

:manual_test
start scripts\test\ui\test_manual.html
goto end

:exit
exit

:end
pause
