@echo off
chcp 65001 > nul
echo.
echo ==========================================
echo    MDM 测试环境启动器
echo ==========================================
echo.

:menu
echo 请选择测试环境类型:
echo.
echo 1. 内存数据库测试环境 (数据不持久化)
echo 2. 独立测试数据库环境 (可清理数据)
echo 3. 当前开发环境 (数据会保存)
echo 4. 清理测试数据
echo 5. 重置数据库到初始状态
echo 0. 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto memory_db
if "%choice%"=="2" goto test_db
if "%choice%"=="3" goto dev_env
if "%choice%"=="4" goto cleanup
if "%choice%"=="5" goto reset
if "%choice%"=="0" goto exit
goto menu

:memory_db
echo.
echo 🧪 启动内存数据库测试环境...
echo ⚠️  数据不会持久化保存
echo.
cd /d "%~dp0..\..\..\"
set DJANGO_SETTINGS_MODULE=config.settings_test
backend\venv\Scripts\python.exe backend\manage.py migrate
backend\venv\Scripts\python.exe backend\manage.py runserver
goto end

:test_db
echo.
echo 🗄️  启动独立测试数据库环境...
echo ℹ️  使用 mdm_test 数据库
echo.
cd /d "%~dp0..\..\..\"
copy backend\.env.test backend\.env
backend\venv\Scripts\python.exe backend\manage.py migrate
backend\venv\Scripts\python.exe backend\manage.py runserver
goto end

:dev_env
echo.
echo 🔧 启动开发环境...
echo ⚠️  数据会保存到开发数据库
echo.
cd /d "%~dp0..\..\..\"
backend\venv\Scripts\python.exe backend\manage.py runserver
goto end

:cleanup
echo.
echo 🧹 清理测试数据...
cd /d "%~dp0..\..\..\"
backend\venv\Scripts\python.exe scripts\test\data\cleanup_test_data.py --cleanup
echo.
pause
goto menu

:reset
echo.
echo 🔄 重置数据库到初始状态...
echo ⚠️  这将删除所有数据！
set /p confirm=确认重置? (y/N): 
if /i "%confirm%"=="y" (
    cd /d "%~dp0..\..\..\"
    backend\venv\Scripts\python.exe scripts\test\data\cleanup_test_data.py --reset
    echo.
    echo ✅ 数据库已重置
) else (
    echo 取消重置
)
echo.
pause
goto menu

:exit
echo 退出测试环境启动器
goto end

:end
echo.
pause
