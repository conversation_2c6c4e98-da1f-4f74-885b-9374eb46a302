#!/usr/bin/env python
"""
创建测试数据脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append('D:/workDir/mdm/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.users.models import User
from apps.devices.models import Device, DeviceCategory
from apps.loans.models import LoanApplication

def create_test_data():
    """创建测试数据"""
    
    # 获取用户
    try:
        admin = User.objects.get(username='admin')
        employee = User.objects.get(username='employee')
        approver = User.objects.get(username='approver')
    except User.DoesNotExist:
        print("❌ 请先运行 create_test_user.py 创建测试用户")
        return
    
    # 创建设备分类
    categories = [
        {'name': '笔记本电脑', 'description': '便携式计算机设备'},
        {'name': '台式机', 'description': '桌面计算机设备'},
        {'name': '显示器', 'description': '显示设备'},
        {'name': '打印机', 'description': '打印设备'},
        {'name': '投影仪', 'description': '投影设备'},
    ]
    
    print("📁 创建设备分类...")
    for cat_data in categories:
        category, created = DeviceCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        if created:
            print(f"  ✅ 创建分类: {category.name}")
        else:
            print(f"  ℹ️  分类已存在: {category.name}")
    
    # 创建测试设备
    laptop_category = DeviceCategory.objects.get(name='笔记本电脑')
    desktop_category = DeviceCategory.objects.get(name='台式机')
    monitor_category = DeviceCategory.objects.get(name='显示器')
    printer_category = DeviceCategory.objects.get(name='打印机')
    projector_category = DeviceCategory.objects.get(name='投影仪')
    
    devices = [
        # 笔记本电脑
        {'name': 'ThinkPad X1 Carbon', 'model': 'X1C-001', 'serial_number': 'TP001', 'category': laptop_category, 'status': 'available'},
        {'name': 'MacBook Pro 16"', 'model': 'MBP-16', 'serial_number': 'MB001', 'category': laptop_category, 'status': 'available'},
        {'name': 'Dell XPS 13', 'model': 'XPS13-001', 'serial_number': 'DL001', 'category': laptop_category, 'status': 'borrowed'},
        {'name': 'HP EliteBook', 'model': 'EB-840', 'serial_number': 'HP001', 'category': laptop_category, 'status': 'maintenance'},
        
        # 台式机
        {'name': 'Dell OptiPlex', 'model': 'OP-7090', 'serial_number': 'DO001', 'category': desktop_category, 'status': 'available'},
        {'name': 'HP ProDesk', 'model': 'PD-600', 'serial_number': 'HP002', 'category': desktop_category, 'status': 'available'},
        
        # 显示器
        {'name': 'Dell UltraSharp 27"', 'model': 'U2720Q', 'serial_number': 'DM001', 'category': monitor_category, 'status': 'available'},
        {'name': 'LG 4K Monitor', 'model': 'LG27UK850', 'serial_number': 'LG001', 'category': monitor_category, 'status': 'borrowed'},
        
        # 打印机
        {'name': 'HP LaserJet Pro', 'model': 'LJ-M404', 'serial_number': 'HP003', 'category': printer_category, 'status': 'available'},
        
        # 投影仪
        {'name': 'Epson PowerLite', 'model': 'PL-1795F', 'serial_number': 'EP001', 'category': projector_category, 'status': 'available'},
    ]
    
    print("\n💻 创建测试设备...")
    for device_data in devices:
        device, created = Device.objects.get_or_create(
            serial_number=device_data['serial_number'],
            defaults={
                'name': device_data['name'],
                'model': device_data['model'],
                'category': device_data['category'],
                'status': device_data['status'],
                'owner': admin,
                'brand': 'Generic',
                'special_notes': f"{device_data['name']} - {device_data['model']}",
                'purchase_date': datetime.now().date() - timedelta(days=365),
                'warranty_period': '3年',
            }
        )
        if created:
            print(f"  ✅ 创建设备: {device.name} ({device.serial_number})")
        else:
            print(f"  ℹ️  设备已存在: {device.name} ({device.serial_number})")
    
    # 创建一些借用申请
    print("\n📋 创建测试借用申请...")
    
    # 获取一些设备
    available_devices = Device.objects.filter(status='available')[:3]
    borrowed_devices = Device.objects.filter(status='borrowed')
    
    # 创建已批准的借用申请
    for device in borrowed_devices:
        loan, created = LoanApplication.objects.get_or_create(
            device=device,
            borrower=employee,
            defaults={
                'reason': f'开发工作需要使用{device.name}',
                'expected_start_date': datetime.now(),
                'expected_end_date': datetime.now() + timedelta(days=30),
                'status': 'borrowed',
                'approver': approver,
                'approved_at': datetime.now(),
                'actual_start_date': datetime.now(),
            }
        )
        if created:
            print(f"  ✅ 创建借用申请: {device.name} (已借出)")
    
    # 创建待审批的借用申请
    if available_devices.exists():
        device = available_devices.first()
        loan, created = LoanApplication.objects.get_or_create(
            device=device,
            borrower=employee,
            status='pending',
            defaults={
                'reason': f'项目开发需要使用{device.name}',
                'expected_start_date': datetime.now() + timedelta(days=1),
                'expected_end_date': datetime.now() + timedelta(days=15),
            }
        )
        if created:
            print(f"  ✅ 创建借用申请: {device.name} (待审批)")
    
    print("\n🎉 测试数据创建完成！")
    print("=" * 60)
    print("📊 数据统计:")
    print(f"  用户数量: {User.objects.count()}")
    print(f"  设备分类: {DeviceCategory.objects.count()}")
    print(f"  设备数量: {Device.objects.count()}")
    print(f"  借用申请: {LoanApplication.objects.count()}")
    print("=" * 60)
    print("🌐 开发服务器: http://127.0.0.1:8000/")
    print("📚 API文档: http://127.0.0.1:8000/api/docs/")
    print("🔧 管理后台: http://127.0.0.1:8000/admin/")

if __name__ == '__main__':
    create_test_data()
