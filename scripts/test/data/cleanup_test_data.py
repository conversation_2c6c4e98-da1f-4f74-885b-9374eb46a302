#!/usr/bin/env python3
"""
清理测试数据脚本
删除通过测试创建的数据，保留核心数据
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.users.models import User
from apps.devices.models import Device, DeviceCategory
from apps.loans.models import LoanApplication

def cleanup_test_data():
    """清理测试数据"""
    print("🧹 开始清理测试数据...")
    print("=" * 50)
    
    # 统计清理前的数据
    users_before = User.objects.count()
    devices_before = Device.objects.count()
    loans_before = LoanApplication.objects.count()
    
    print(f"清理前统计:")
    print(f"  用户: {users_before}")
    print(f"  设备: {devices_before}")
    print(f"  借用记录: {loans_before}")
    
    # 清理测试设备（名称包含"测试"的设备）
    from django.db.models import Q

    test_devices = Device.objects.filter(
        Q(name__icontains='测试') |
        Q(name__icontains='test') |
        Q(name__icontains='同步测试')
    )

    test_device_count = test_devices.count()
    if test_device_count > 0:
        test_devices.delete()
        print(f"✅ 删除了 {test_device_count} 个测试设备")
    
    # 清理测试用户（保留admin和superadmin）
    test_users = User.objects.exclude(
        username__in=['admin', 'superadmin', 'sunyong', 'test']
    ).filter(
        username__icontains='test'
    )
    
    test_user_count = test_users.count()
    if test_user_count > 0:
        test_users.delete()
        print(f"✅ 删除了 {test_user_count} 个测试用户")
    
    # 清理最近创建的借用记录（最近1小时内创建的）
    recent_time = datetime.now() - timedelta(hours=1)
    recent_loans = LoanApplication.objects.filter(
        created_at__gte=recent_time
    )
    
    recent_loan_count = recent_loans.count()
    if recent_loan_count > 0:
        recent_loans.delete()
        print(f"✅ 删除了 {recent_loan_count} 个最近的借用记录")
    
    # 统计清理后的数据
    users_after = User.objects.count()
    devices_after = Device.objects.count()
    loans_after = LoanApplication.objects.count()
    
    print("\n" + "=" * 50)
    print(f"清理后统计:")
    print(f"  用户: {users_after} (减少 {users_before - users_after})")
    print(f"  设备: {devices_after} (减少 {devices_before - devices_after})")
    print(f"  借用记录: {loans_after} (减少 {loans_before - loans_after})")
    
    print("\n🎉 测试数据清理完成！")

def reset_to_initial_state():
    """重置到初始状态"""
    print("🔄 重置数据库到初始状态...")
    
    # 删除所有非核心数据
    LoanApplication.objects.all().delete()
    Device.objects.all().delete()
    User.objects.exclude(username__in=['admin', 'superadmin']).delete()
    
    # 重新创建基础数据
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from create_test_data import create_basic_data
    create_basic_data()
    
    print("✅ 数据库已重置到初始状态")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理测试数据')
    parser.add_argument('--reset', action='store_true', help='重置到初始状态')
    parser.add_argument('--cleanup', action='store_true', help='清理测试数据')
    
    args = parser.parse_args()
    
    if args.reset:
        reset_to_initial_state()
    elif args.cleanup:
        cleanup_test_data()
    else:
        # 默认执行清理
        cleanup_test_data()

if __name__ == '__main__':
    main()
