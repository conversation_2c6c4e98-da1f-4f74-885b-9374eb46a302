#!/usr/bin/env python
"""
创建测试用户脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('D:/workDir/mdm/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.users.models import User

def create_test_users():
    """创建测试用户"""
    
    # 创建管理员用户
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': '管理员',
            'last_name': '系统',
            'role': 'admin',
            'department': 'IT部门',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True,
        }
    )
    if created:
        admin_user.set_password('admin123456')
        admin_user.save()
        print(f"✅ 创建管理员用户: {admin_user.username}")
    else:
        print(f"ℹ️  管理员用户已存在: {admin_user.username}")
    
    # 创建普通员工用户
    employee_user, created = User.objects.get_or_create(
        username='employee',
        defaults={
            'email': '<EMAIL>',
            'first_name': '张',
            'last_name': '三',
            'role': 'employee',
            'department': '开发部',
            'employee_id': 'EMP001',
            'is_active': True,
        }
    )
    if created:
        employee_user.set_password('employee123')
        employee_user.save()
        print(f"✅ 创建员工用户: {employee_user.username}")
    else:
        print(f"ℹ️  员工用户已存在: {employee_user.username}")
    
    # 创建审批者用户
    approver_user, created = User.objects.get_or_create(
        username='approver',
        defaults={
            'email': '<EMAIL>',
            'first_name': '李',
            'last_name': '四',
            'role': 'approver',
            'department': '管理部',
            'employee_id': 'APP001',
            'is_active': True,
        }
    )
    if created:
        approver_user.set_password('approver123')
        approver_user.save()
        print(f"✅ 创建审批者用户: {approver_user.username}")
    else:
        print(f"ℹ️  审批者用户已存在: {approver_user.username}")
    
    print("\n🎯 测试用户创建完成！")
    print("=" * 50)
    print("登录信息:")
    print("管理员 - 用户名: admin, 密码: admin123456")
    print("员工   - 用户名: employee, 密码: employee123")
    print("审批者 - 用户名: approver, 密码: approver123")
    print("=" * 50)

if __name__ == '__main__':
    create_test_users()
