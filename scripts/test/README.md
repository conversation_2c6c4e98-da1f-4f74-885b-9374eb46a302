# MDM系统测试工具集

## 📁 目录结构

```
scripts/test/
├── README.md                    # 本文件 - 测试工具总览
├── testing_guide.md            # 详细测试指南
├── test_report.md              # 测试报告模板
│
├── launchers/                   # 启动器和菜单
│   ├── test_menu.bat           # 主测试菜单 (原test.bat)
│   ├── start_test_env.bat      # 测试环境启动器
│   └── test_frontend.bat       # 前端配置测试
│
├── api/                        # API测试工具
│   ├── quick_api_test.py       # 快速API测试
│   ├── validate_api.py         # API验证脚本
│   ├── test_reports_api.py     # 报表API测试
│   ├── test_admin_login.py     # 管理员登录测试
│   ├── test_doc_links.py       # 文档链接测试
│   └── check_api_docs.py       # API文档结构检查
│
├── data/                       # 测试数据管理
│   ├── create_test_data.py     # 创建测试数据
│   ├── create_test_user.py     # 创建测试用户
│   └── cleanup_test_data.py    # 清理测试数据
│
├── ui/                         # 用户界面测试
│   ├── test_manual.html        # 手动测试页面
│   └── debug_login.html        # 登录调试页面
│
└── config/                     # 测试配置
    └── settings_test.py        # 测试环境配置
```

## 🚀 快速开始

### 主要入口点

1. **测试菜单**: `launchers/test_menu.bat` - 统一的测试工具入口
2. **测试环境**: `launchers/start_test_env.bat` - 不同测试环境启动器
3. **手动测试**: `ui/test_manual.html` - 可视化测试界面

### 常用命令

```bash
# 启动测试菜单
scripts/test/launchers/test_menu.bat

# 启动测试环境选择器
scripts/test/launchers/start_test_env.bat

# 快速API测试
python scripts/test/api/quick_api_test.py

# 创建测试数据
python scripts/test/data/create_test_data.py

# 清理测试数据
python scripts/test/data/cleanup_test_data.py
```

## 📋 测试类型

### 1. API接口测试
- **快速测试**: 验证所有API端点基本功能
- **详细验证**: 检查响应格式、状态码、性能
- **文档测试**: 验证API文档的完整性和准确性

### 2. 数据管理测试
- **数据创建**: 创建各种测试数据和用户
- **数据清理**: 清理测试产生的数据
- **数据一致性**: 验证前后端数据同步

### 3. 用户界面测试
- **手动测试**: 通过Web界面进行交互测试
- **功能测试**: 验证业务流程和用户体验
- **兼容性测试**: 不同浏览器和设备的兼容性

### 4. 环境配置测试
- **开发环境**: 使用真实数据库的测试
- **测试环境**: 使用独立测试数据库
- **内存环境**: 使用内存数据库，数据不持久化

## ⚠️ 重要提醒

### 数据安全
- **开发环境**: 测试数据会写入真实数据库，需要手动清理
- **测试环境**: 使用独立数据库，相对安全
- **内存环境**: 数据不持久化，最安全

### 使用建议
1. **开发阶段**: 使用内存环境进行快速测试
2. **集成测试**: 使用测试环境进行完整验证
3. **生产前**: 使用开发环境进行最终确认

## 📚 详细文档

- [测试指南](testing_guide.md) - 详细的测试方法和流程
- [测试报告](test_report.md) - 当前测试状态和结果

## 🔧 维护说明

### 添加新测试
1. 根据测试类型放入对应目录
2. 更新主测试菜单
3. 更新本README文档

### 目录规范
- `api/` - 纯API接口测试，不涉及UI
- `data/` - 数据管理相关脚本
- `ui/` - 用户界面和交互测试
- `launchers/` - 启动器和菜单脚本
- `config/` - 测试环境配置文件
