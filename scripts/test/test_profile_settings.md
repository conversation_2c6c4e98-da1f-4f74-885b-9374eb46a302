# 个人信息和设置功能测试指南

## 测试环境
- 前端地址: http://localhost:3000
- 后端地址: http://localhost:8000
- 测试时间: 2025-07-30

## 功能测试清单

### 1. 个人资料页面测试 (`/profile`)

#### 1.1 页面访问测试
- [ ] 登录后点击用户头像下拉菜单
- [ ] 点击"个人资料"菜单项
- [ ] 验证页面正确跳转到 `/profile`
- [ ] 验证页面正常加载，无错误信息

#### 1.2 个人信息显示测试
- [ ] 验证用户头像正确显示
- [ ] 验证用户名、邮箱等基本信息正确显示
- [ ] 验证用户角色标签正确显示
- [ ] 验证账户信息（创建时间、登录次数等）正确显示

#### 1.3 编辑功能测试
- [ ] 点击"编辑资料"按钮，进入编辑模式
- [ ] 验证表单字段可以正常编辑
- [ ] 修改邮箱、手机号等信息
- [ ] 点击"保存"按钮，验证数据保存成功
- [ ] 点击"取消"按钮，验证数据恢复原状

#### 1.4 头像上传测试
- [ ] 在编辑模式下点击"更换头像"按钮
- [ ] 选择图片文件上传
- [ ] 验证文件类型限制（只允许图片）
- [ ] 验证文件大小限制（不超过2MB）
- [ ] 验证头像上传成功后正确显示

#### 1.5 表单验证测试
- [ ] 输入无效邮箱格式，验证错误提示
- [ ] 输入无效手机号格式，验证错误提示
- [ ] 输入无效网址格式，验证错误提示
- [ ] 验证必填字段的验证规则

### 2. 设置页面测试 (`/settings`)

#### 2.1 页面访问测试
- [ ] 登录后点击用户头像下拉菜单
- [ ] 点击"设置"菜单项
- [ ] 验证页面正确跳转到 `/settings`
- [ ] 验证页面正常加载，显示所有设置标签

#### 2.2 密码修改测试
- [ ] 切换到"密码设置"标签
- [ ] 输入当前密码、新密码、确认密码
- [ ] 验证密码强度要求提示
- [ ] 验证两次密码输入一致性检查
- [ ] 提交表单，验证密码修改成功
- [ ] 验证修改后可以用新密码登录

#### 2.3 通知设置测试
- [ ] 切换到"通知设置"标签
- [ ] 切换邮件通知开关，验证设置保存
- [ ] 切换短信通知开关，验证设置保存
- [ ] 切换微信通知开关，验证设置保存
- [ ] 验证设置变更后立即生效

#### 2.4 偏好设置测试
- [ ] 切换到"偏好设置"标签
- [ ] 修改语言设置，验证选项可选择
- [ ] 修改时区设置，验证选项可选择
- [ ] 验证偏好设置保存成功

#### 2.5 安全设置测试
- [ ] 切换到"安全设置"标签
- [ ] 验证登录统计信息正确显示
- [ ] 点击"查看日志"按钮，验证登录日志弹窗
- [ ] 点击"强制下线"按钮，验证确认对话框

### 3. 路由和导航测试

#### 3.1 路由配置测试
- [ ] 直接访问 `http://localhost:3000/profile`
- [ ] 直接访问 `http://localhost:3000/settings`
- [ ] 验证未登录时自动跳转到登录页
- [ ] 验证登录后可以正常访问

#### 3.2 导航功能测试
- [ ] 在个人资料页面点击浏览器后退按钮
- [ ] 在设置页面点击浏览器后退按钮
- [ ] 验证页面导航正常，无错误

### 4. API接口测试

#### 4.1 个人资料API测试
- [ ] 验证 `GET /api/auth/profile/` 返回用户信息
- [ ] 验证 `PATCH /api/auth/update/` 更新用户信息
- [ ] 验证 `POST /api/auth/upload-avatar/` 上传头像

#### 4.2 设置相关API测试
- [ ] 验证 `POST /api/auth/change-password/` 修改密码
- [ ] 验证 `GET /api/auth/login-logs/` 获取登录日志

### 5. 错误处理测试

#### 5.1 网络错误测试
- [ ] 断开网络连接，测试API调用失败处理
- [ ] 验证错误提示信息友好
- [ ] 验证页面不会崩溃

#### 5.2 权限错误测试
- [ ] 测试未登录访问受保护页面
- [ ] 验证自动跳转到登录页面

### 6. 用户体验测试

#### 6.1 界面响应性测试
- [ ] 在不同屏幕尺寸下测试页面布局
- [ ] 验证移动端适配效果
- [ ] 验证加载状态显示

#### 6.2 操作流畅性测试
- [ ] 验证表单提交后的反馈
- [ ] 验证成功/错误消息提示
- [ ] 验证页面切换动画效果

## 测试结果记录

### 通过的测试项
- [ ] 个人资料页面基本功能
- [ ] 设置页面基本功能
- [ ] 路由配置正确
- [ ] API接口正常

### 发现的问题
1. 问题描述：
   - 影响程度：
   - 解决方案：

2. 问题描述：
   - 影响程度：
   - 解决方案：

### 测试总结
- 测试完成时间：
- 总体评价：
- 建议改进：

## 自动化测试建议

### 单元测试
```javascript
// 建议添加的测试用例
describe('Profile Page', () => {
  test('should load user profile data', () => {
    // 测试用户资料加载
  })
  
  test('should update profile successfully', () => {
    // 测试资料更新
  })
})

describe('Settings Page', () => {
  test('should change password successfully', () => {
    // 测试密码修改
  })
  
  test('should update notification settings', () => {
    // 测试通知设置
  })
})
```

### 集成测试
```javascript
// API集成测试
describe('Profile API', () => {
  test('GET /api/auth/profile/', () => {
    // 测试获取用户信息API
  })
  
  test('POST /api/auth/upload-avatar/', () => {
    // 测试头像上传API
  })
})
```

---

**测试负责人**: 开发团队
**测试环境**: 开发环境
**测试版本**: v2.3
**文档更新**: 2025-07-30
