#!/usr/bin/env python3
"""
报表API测试脚本
用于验证Phase 3开发的报表统计功能是否正常工作
"""

import requests
import json
import sys
from datetime import datetime

# API基础URL
BASE_URL = "http://127.0.0.1:8000"

# 测试用户凭据
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

class ReportsAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def login(self):
        """登录获取认证token"""
        print("🔐 正在登录...")
        
        login_url = f"{BASE_URL}/api/auth/login/"
        response = self.session.post(login_url, json=TEST_CREDENTIALS)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access')
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
            print("✅ 登录成功")
            return True
        else:
            print(f"❌ 登录失败: {response.status_code}")
            print(response.text)
            return False
    
    def test_dashboard_stats(self):
        """测试仪表盘统计API"""
        print("\n📊 测试仪表盘统计API...")
        
        url = f"{BASE_URL}/api/reports/dashboard/"
        response = self.session.get(url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 仪表盘统计API正常")
            print(f"   设备总数: {data.get('device_stats', {}).get('total', 0)}")
            print(f"   可用设备: {data.get('device_stats', {}).get('available', 0)}")
            print(f"   借出设备: {data.get('device_stats', {}).get('borrowed', 0)}")
            print(f"   总借用数: {data.get('loan_stats', {}).get('total', 0)}")
            return True
        else:
            print(f"❌ 仪表盘统计API失败: {response.status_code}")
            print(response.text)
            return False
    
    def test_device_stats(self):
        """测试设备统计API"""
        print("\n🖥️  测试设备统计API...")
        
        url = f"{BASE_URL}/api/reports/devices/"
        response = self.session.get(url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 设备统计API正常")
            
            status_dist = data.get('status_distribution', [])
            print(f"   状态分布: {len(status_dist)} 种状态")
            
            category_dist = data.get('category_distribution', [])
            print(f"   分类分布: {len(category_dist)} 个分类")
            
            utilization = data.get('utilization_stats', [])
            print(f"   利用率统计: {len(utilization)} 台设备")
            
            return True
        else:
            print(f"❌ 设备统计API失败: {response.status_code}")
            print(response.text)
            return False
    
    def test_loan_stats(self):
        """测试借用统计API"""
        print("\n📋 测试借用统计API...")
        
        url = f"{BASE_URL}/api/reports/loans/"
        response = self.session.get(url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 借用统计API正常")
            
            monthly_trend = data.get('monthly_trend', [])
            print(f"   月度趋势: {len(monthly_trend)} 个月数据")
            print(f"   借用成功率: {data.get('success_rate', 0)}%")
            print(f"   平均借用时长: {data.get('average_duration_days', 0)} 天")
            
            popular_devices = data.get('popular_devices', [])
            print(f"   热门设备: {len(popular_devices)} 台")
            
            return True
        else:
            print(f"❌ 借用统计API失败: {response.status_code}")
            print(response.text)
            return False
    
    def test_user_stats(self):
        """测试用户统计API"""
        print("\n👥 测试用户统计API...")
        
        url = f"{BASE_URL}/api/reports/users/"
        response = self.session.get(url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 用户统计API正常")
            
            role_dist = data.get('role_distribution', [])
            print(f"   角色分布: {len(role_dist)} 种角色")
            print(f"   活跃用户: {data.get('active_users_count', 0)} 人")
            
            user_ranking = data.get('user_ranking', [])
            print(f"   用户排行: {len(user_ranking)} 人")
            
            dept_stats = data.get('department_stats', [])
            print(f"   部门统计: {len(dept_stats)} 个部门")
            
            return True
        else:
            print(f"❌ 用户统计API失败: {response.status_code}")
            print(response.text)
            return False
    
    def test_export_report(self):
        """测试报表导出功能"""
        print("\n📤 测试报表导出功能...")
        
        url = f"{BASE_URL}/api/reports/export/"
        payload = {
            "report_type": "device",
            "format": "excel"
        }
        
        response = self.session.post(url, json=payload)
        
        if response.status_code == 200:
            print("✅ 报表导出API正常")
            
            # 检查响应头
            content_type = response.headers.get('content-type', '')
            if 'spreadsheet' in content_type:
                print("   ✅ Excel文件格式正确")
            
            content_length = len(response.content)
            print(f"   文件大小: {content_length} 字节")
            
            return True
        else:
            print(f"❌ 报表导出API失败: {response.status_code}")
            print(response.text)
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试报表API功能...")
        print("=" * 50)
        
        # 登录
        if not self.login():
            return False
        
        # 运行各项测试
        tests = [
            self.test_dashboard_stats,
            self.test_device_stats,
            self.test_loan_stats,
            self.test_user_stats,
            self.test_export_report
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！Phase 3 报表功能开发成功！")
            return True
        else:
            print("⚠️  部分测试失败，需要检查相关功能")
            return False

def main():
    """主函数"""
    print("Phase 3 报表统计功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试目标: {BASE_URL}")
    
    tester = ReportsAPITester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Phase 3 开发验证完成！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查系统状态")
        sys.exit(1)

if __name__ == "__main__":
    main()
