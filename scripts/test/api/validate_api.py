#!/usr/bin/env python3
"""
API功能验证脚本
用于验证所有API端点是否正常工作
"""

import requests
import json
import sys
import time

# API配置
API_BASE = 'http://127.0.0.1:8000/api'
LOGIN_DATA = {
    'username': 'admin',
    'password': 'admin123456'
}

class APIValidator:
    def __init__(self):
        self.token = None
        self.session = requests.Session()
        
    def login(self):
        """登录获取JWT令牌"""
        try:
            response = self.session.post(f'{API_BASE}/auth/login/', json=LOGIN_DATA)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access')
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(response.text)
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_endpoint(self, endpoint, method='GET', data=None, expected_status=200):
        """测试API端点"""
        try:
            url = f'{API_BASE}{endpoint}'
            
            if method == 'GET':
                response = self.session.get(url)
            elif method == 'POST':
                response = self.session.post(url, json=data)
            elif method == 'PUT':
                response = self.session.put(url, json=data)
            elif method == 'DELETE':
                response = self.session.delete(url)
            else:
                print(f"❌ 不支持的HTTP方法: {method}")
                return False
            
            if response.status_code == expected_status:
                print(f"✅ {method} {endpoint} - 状态码: {response.status_code}")
                return True
            else:
                print(f"⚠️ {method} {endpoint} - 状态码: {response.status_code} (期望: {expected_status})")
                if response.status_code >= 400:
                    try:
                        error_data = response.json()
                        print(f"   错误信息: {error_data}")
                    except:
                        print(f"   错误信息: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"❌ {method} {endpoint} - 异常: {e}")
            return False
    
    def run_tests(self):
        """运行所有测试"""
        print("🚀 开始API功能验证...")
        print("=" * 50)
        
        # 登录测试
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return False
        
        print("\n📋 测试API端点...")
        
        # 测试端点列表
        test_cases = [
            # 用户相关
            ('/auth/profile/', 'GET'),
            
            # 设备相关
            ('/devices/', 'GET'),
            ('/devices/categories/', 'GET'),
            
            # 借用相关
            ('/loans/applications/', 'GET'),
            ('/loans/my-applications/', 'GET'),
            ('/loans/my-loans/', 'GET'),
            
            # 报表相关
            ('/reports/dashboard/', 'GET'),
            ('/reports/users/', 'GET'),
            ('/reports/devices/', 'GET'),
            ('/reports/loans/', 'GET'),
        ]
        
        success_count = 0
        total_count = len(test_cases)
        
        for endpoint, method in test_cases:
            if self.test_endpoint(endpoint, method):
                success_count += 1
            time.sleep(0.5)  # 避免请求过快
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {success_count}/{total_count} 通过")
        
        if success_count == total_count:
            print("🎉 所有API测试通过！")
            return True
        else:
            print(f"⚠️ 有 {total_count - success_count} 个API测试失败")
            return False

def main():
    """主函数"""
    validator = APIValidator()
    success = validator.run_tests()
    
    if success:
        print("\n✅ API验证完成，系统功能正常")
        sys.exit(0)
    else:
        print("\n❌ API验证失败，请检查系统状态")
        sys.exit(1)

if __name__ == '__main__':
    main()
