#!/usr/bin/env python3
"""
检查API文档结构
"""

import requests
import re
import yaml

def check_api_docs():
    """检查API文档的标签结构"""
    try:
        print("🔍 检查API文档结构...")
        
        # 获取API schema
        schema_response = requests.get('http://127.0.0.1:8000/api/schema/')
        if schema_response.status_code == 200:
            schema = yaml.safe_load(schema_response.text)
            
            # 提取标签信息
            if 'tags' in schema:
                print("\n📋 发现的API标签:")
                for tag in schema['tags']:
                    name = tag.get('name', '')
                    description = tag.get('description', '')
                    print(f"  - {name}: {description}")
            
            # 提取路径信息
            if 'paths' in schema:
                print("\n🛣️ API路径分组:")
                path_groups = {}
                for path in schema['paths'].keys():
                    # 提取第一级路径作为分组
                    parts = path.strip('/').split('/')
                    if len(parts) >= 2:
                        group = parts[1]  # api/auth -> auth
                        if group not in path_groups:
                            path_groups[group] = []
                        path_groups[group].append(path)
                
                for group, paths in path_groups.items():
                    print(f"  📁 {group}: {len(paths)} 个端点")
                    for path in paths[:3]:  # 只显示前3个
                        print(f"    - {path}")
                    if len(paths) > 3:
                        print(f"    - ... 还有 {len(paths) - 3} 个")
        
        # 检查文档页面
        docs_response = requests.get('http://127.0.0.1:8000/api/docs/')
        if docs_response.status_code == 200:
            print("\n✅ API文档页面可访问")
            
            # 查找可能的锚点
            content = docs_response.text
            
            # 查找Swagger UI的标签
            tag_matches = re.findall(r'"tags":\s*\["([^"]+)"\]', content)
            if tag_matches:
                print("\n🏷️ 文档中的标签:")
                for tag in set(tag_matches):
                    print(f"  - {tag}")
        
        print("\n🎯 建议的文档链接格式:")
        print("  - 用户认证: /api/docs/#/用户认证 或 /api/docs/#/auth")
        print("  - 设备管理: /api/docs/#/设备管理 或 /api/docs/#/devices") 
        print("  - 借用管理: /api/docs/#/借用管理 或 /api/docs/#/loans")
        print("  - 报表统计: /api/docs/#/报表统计 或 /api/docs/#/reports")
        
    except Exception as e:
        print(f"❌ 检查API文档时出错: {e}")

if __name__ == '__main__':
    check_api_docs()
