#!/usr/bin/env python3
"""
测试文档链接是否正确工作
"""

import requests
import time

def test_doc_links():
    """测试各种文档链接"""
    print("🔍 测试文档链接...")
    
    # 测试的链接列表
    test_links = [
        ("API文档主页", "http://127.0.0.1:8000/api/docs/"),
        ("ReDoc文档", "http://127.0.0.1:8000/api/redoc/"),
        ("API Schema", "http://127.0.0.1:8000/api/schema/"),
        ("健康检查", "http://127.0.0.1:8000/api/health/"),
        ("系统信息", "http://127.0.0.1:8000/api/system/info/"),
        ("管理后台", "http://127.0.0.1:8000/admin/"),
        ("API根路径", "http://127.0.0.1:8000/api/"),
    ]
    
    # 测试带锚点的文档链接
    anchor_links = [
        ("用户认证文档", "http://127.0.0.1:8000/api/docs/#/用户认证"),
        ("设备管理文档", "http://127.0.0.1:8000/api/docs/#/设备管理"),
        ("借用管理文档", "http://127.0.0.1:8000/api/docs/#/借用管理"),
        ("报表统计文档", "http://127.0.0.1:8000/api/docs/#/报表统计"),
    ]
    
    success_count = 0
    total_count = len(test_links) + len(anchor_links)
    
    print("\n📋 测试基础链接:")
    for name, url in test_links:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: {response.status_code}")
                success_count += 1
            else:
                print(f"⚠️ {name}: {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: {e}")
        time.sleep(0.2)  # 避免请求过快
    
    print("\n📋 测试锚点链接 (检查主页面):")
    for name, url in anchor_links:
        try:
            # 对于锚点链接，我们只测试主页面是否可访问
            base_url = url.split('#')[0]
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: 主页面可访问")
                success_count += 1
            else:
                print(f"⚠️ {name}: 主页面状态 {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: {e}")
        time.sleep(0.2)
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有文档链接测试通过！")
    else:
        print(f"⚠️ 有 {total_count - success_count} 个链接需要检查")
    
    print("\n💡 使用建议:")
    print("1. 点击'查看文档'按钮会跳转到对应的API模块文档")
    print("2. 在Swagger UI中，可以直接测试API接口")
    print("3. 锚点链接会自动滚动到对应的API模块部分")
    print("4. 如果锚点不工作，可能是因为中文编码问题，可以手动在页面中搜索模块名")

if __name__ == '__main__':
    test_doc_links()
