#!/usr/bin/env python3
"""
测试管理后台登录功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import authenticate
from apps.users.models import User

def test_admin_login():
    """测试管理员登录"""
    print("🔍 测试管理后台登录功能...")
    print("=" * 50)
    
    # 测试用户列表
    test_users = [
        ('admin', 'admin123456'),
        ('superadmin', 'superadmin123456'),
    ]
    
    for username, password in test_users:
        print(f"\n📋 测试用户: {username}")
        
        try:
            # 检查用户是否存在
            user = User.objects.filter(username=username).first()
            if not user:
                print(f"❌ 用户 {username} 不存在")
                continue
            
            print(f"✅ 用户存在: {user.username}")
            print(f"   - Email: {user.email}")
            print(f"   - Is Active: {user.is_active}")
            print(f"   - Is Staff: {user.is_staff}")
            print(f"   - Is Superuser: {user.is_superuser}")
            print(f"   - Role: {user.role}")
            
            # 测试密码认证
            auth_user = authenticate(username=username, password=password)
            if auth_user:
                print(f"✅ 密码认证成功")
                
                # 检查管理后台权限
                if auth_user.is_staff and auth_user.is_active:
                    print(f"✅ 可以访问管理后台")
                else:
                    print(f"❌ 无法访问管理后台 (is_staff: {auth_user.is_staff}, is_active: {auth_user.is_active})")
            else:
                print(f"❌ 密码认证失败")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("📊 管理后台登录测试完成")

if __name__ == '__main__':
    test_admin_login()
