<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理系统 - 人工测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #007bff;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .test-card h3 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .test-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .status-item .number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .status-item .label {
            color: #666;
            margin-top: 5px;
        }
        
        .login-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .login-info h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .login-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .login-table th,
        .login-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .login-table th {
            background: #1976d2;
            color: white;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .auth-status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-small {
            padding: 5px 15px;
            font-size: 0.9em;
        }

        .api-result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .api-result pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 设备管理系统</h1>
            <p>Phase 4 系统优化 - 人工测试环境</p>
        </div>
        
        <div class="content">
            <!-- 登录信息 -->
            <div class="login-info">
                <h3>🔐 测试账户信息</h3>
                <table class="login-table">
                    <thead>
                        <tr>
                            <th>角色</th>
                            <th>用户名</th>
                            <th>密码</th>
                            <th>权限</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>管理员</td>
                            <td>admin</td>
                            <td>admin123456</td>
                            <td>全部权限 + 管理后台</td>
                        </tr>
                        <tr>
                            <td>超级管理员</td>
                            <td>superadmin</td>
                            <td>superadmin123456</td>
                            <td>Django管理后台专用</td>
                        </tr>
                        <tr>
                            <td>普通用户</td>
                            <td>sunyong</td>
                            <td>（需要重置密码）</td>
                            <td>基本功能</td>
                        </tr>
                        <tr>
                            <td>测试用户</td>
                            <td>test</td>
                            <td>（需要重置密码）</td>
                            <td>基本功能</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 系统状态 -->
            <div class="section">
                <h2>📊 系统状态</h2>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="number">5</div>
                        <div class="label">用户数量</div>
                    </div>
                    <div class="status-item">
                        <div class="number">14</div>
                        <div class="label">设备数量</div>
                    </div>
                    <div class="status-item">
                        <div class="number">9</div>
                        <div class="label">设备分类</div>
                    </div>
                    <div class="status-item">
                        <div class="number">3</div>
                        <div class="label">借用申请</div>
                    </div>
                </div>
            </div>
            
            <!-- API接口测试 -->
            <div class="section">
                <h2>🔧 API接口技术测试</h2>
                <p style="text-align: center; color: #666; margin-bottom: 20px;">
                    专注于API接口的技术验证：响应格式、状态码、性能等
                </p>

                <!-- 认证状态 -->
                <div class="auth-status" id="authStatus">
                    <span id="authIndicator">🔒 未登录</span>
                    <button id="loginBtn" class="btn btn-small" onclick="login()">获取JWT令牌</button>
                    <button id="logoutBtn" class="btn btn-small" onclick="logout()" style="display:none;">清除令牌</button>
                    <button class="btn btn-small" onclick="testClick()">测试连接</button>
                </div>

                <!-- 数据库警告 -->
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 15px 0;">
                    <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ 数据库写入提醒</h4>
                    <p style="color: #856404; margin: 0; font-size: 0.9em;">
                        <strong>注意</strong>: 当前测试环境会将数据写入真实数据库！
                        创建的用户、设备、借用记录都会持久化保存。
                        <br>
                        <strong>建议</strong>: 测试完成后运行 <code>scripts/test/cleanup_test_data.py</code> 清理测试数据。
                    </p>
                </div>

                <div class="test-grid">
                    <div class="test-card">
                        <h3>🔐 认证API测试</h3>
                        <p><strong>技术验证</strong>: JWT令牌、刷新机制、权限验证</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: 登录响应格式、令牌有效期、权限控制
                        </div>
                        <button class="btn" onclick="login()">POST /auth/login/</button>
                        <button class="btn" onclick="testUserManagement()">GET /auth/profile/</button>
                        <a href="http://127.0.0.1:8000/api/docs/#/用户认证" class="btn" target="_blank">API文档</a>
                    </div>

                    <div class="test-card">
                        <h3>💻 设备API测试</h3>
                        <p><strong>技术验证</strong>: CRUD操作、分页、筛选、排序</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: 响应时间、数据格式、错误处理
                        </div>
                        <button class="btn btn-success" onclick="testDeviceList()">GET /devices/</button>
                        <button class="btn" onclick="createTestDevice()">POST /devices/</button>
                        <a href="http://127.0.0.1:8000/api/docs/#/设备管理" class="btn" target="_blank">API文档</a>
                    </div>

                    <div class="test-card">
                        <h3>📋 借用API测试</h3>
                        <p><strong>技术验证</strong>: 状态机、业务规则、数据一致性</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: 状态转换、并发控制、事务处理
                        </div>
                        <button class="btn btn-warning" onclick="testLoanList()">GET /loans/applications/</button>
                        <button class="btn" onclick="testLoanFlow()">测试借用流程</button>
                        <a href="http://127.0.0.1:8000/api/docs/#/借用管理" class="btn" target="_blank">API文档</a>
                    </div>

                    <div class="test-card">
                        <h3>📊 统计API测试</h3>
                        <p><strong>技术验证</strong>: 聚合查询、缓存机制、性能优化</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: 查询性能、数据准确性、缓存策略
                        </div>
                        <button class="btn btn-info" onclick="testDashboard()">GET /reports/dashboard/</button>
                        <button class="btn" onclick="testReports()">批量统计测试</button>
                        <a href="http://127.0.0.1:8000/api/docs/#/报表统计" class="btn" target="_blank">API文档</a>
                    </div>

                    <div class="test-card">
                        <h3>⚡ 性能和错误测试</h3>
                        <p><strong>技术验证</strong>: 响应时间、错误处理、边界条件</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: 并发请求、异常处理、限流机制
                        </div>
                        <button class="btn btn-danger" onclick="testErrorHandling()">错误处理测试</button>
                        <button class="btn" onclick="testPerformance()">性能测试</button>
                        <a href="http://127.0.0.1:8000/api/health/" class="btn" target="_blank">健康检查</a>
                    </div>

                    <div class="test-card">
                        <h3>🔍 API规范验证</h3>
                        <p><strong>技术验证</strong>: OpenAPI规范、响应格式、HTTP状态码</p>
                        <div style="text-align: left; font-size: 0.9em; margin: 10px 0;">
                            <strong>测试项目</strong>: Schema验证、CORS配置、内容类型
                        </div>
                        <button class="btn btn-secondary" onclick="testApiSchema()">Schema验证</button>
                        <button class="btn" onclick="testCORS()">CORS测试</button>
                        <a href="http://127.0.0.1:8000/api/schema/" class="btn" target="_blank">API Schema</a>
                    </div>
                </div>

                <!-- API测试结果 -->
                <div class="api-result" id="apiResult" style="display:none;">
                    <h3>📋 测试结果</h3>
                    <pre id="resultContent"></pre>
                </div>
            </div>
            
            <!-- 功能测试指南 -->
            <div class="section">
                <h2>🧪 业务功能测试指南</h2>
                <p style="text-align: center; color: #666; margin-bottom: 20px;">
                    以下是完整的业务流程测试，模拟真实用户使用场景
                </p>

                <div class="test-grid">
                    <div class="test-card">
                        <h3>🔐 1. 用户权限测试</h3>
                        <p><strong>测试目标</strong>: 验证不同角色的权限控制</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>使用admin账户登录管理后台</li>
                                <li>使用admin账户登录前端界面</li>
                                <li>验证管理员权限功能</li>
                                <li>测试普通用户权限限制</li>
                            </ol>
                        </div>
                        <a href="http://127.0.0.1:8000/admin/" class="btn" target="_blank">管理后台</a>
                        <a href="http://localhost:5174/login" class="btn" target="_blank">前端登录</a>
                    </div>

                    <div class="test-card">
                        <h3>📱 2. 设备生命周期测试</h3>
                        <p><strong>测试目标</strong>: 完整的设备管理流程</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>创建新设备 → 设置基本信息</li>
                                <li>分配设备归属者</li>
                                <li>修改设备状态（可用→维修→报废）</li>
                                <li>查看设备历史记录</li>
                            </ol>
                        </div>
                        <a href="http://localhost:5174/devices" class="btn btn-success" target="_blank">设备管理页面</a>
                        <button class="btn" onclick="createTestDevice()">创建测试设备</button>
                    </div>

                    <div class="test-card">
                        <h3>🔄 3. 借用业务流程测试</h3>
                        <p><strong>测试目标</strong>: 端到端借用流程</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>员工提交借用申请</li>
                                <li>管理员审批申请</li>
                                <li>设备借出和状态变更</li>
                                <li>到期提醒和归还流程</li>
                            </ol>
                        </div>
                        <a href="http://localhost:5174/loans" class="btn btn-warning" target="_blank">借用管理页面</a>
                        <a href="http://localhost:5174/loans/apply" class="btn" target="_blank">申请借用</a>
                    </div>

                    <div class="test-card">
                        <h3>📊 4. 数据统计和报表测试</h3>
                        <p><strong>测试目标</strong>: 统计数据准确性</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>查看仪表盘统计数据</li>
                                <li>验证设备统计准确性</li>
                                <li>检查借用统计报表</li>
                                <li>测试数据导出功能</li>
                            </ol>
                        </div>
                        <a href="http://localhost:5174/dashboard" class="btn btn-info" target="_blank">仪表盘</a>
                        <a href="http://localhost:5174/reports" class="btn" target="_blank">报表页面</a>
                    </div>

                    <div class="test-card">
                        <h3>🖥️ 5. 前端界面体验测试</h3>
                        <p><strong>测试目标</strong>: 用户界面和交互体验</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>测试响应式设计（手机/平板/桌面）</li>
                                <li>验证表单验证和错误提示</li>
                                <li>测试搜索和筛选功能</li>
                                <li>检查页面加载性能</li>
                            </ol>
                        </div>
                        <a href="http://localhost:5174/" class="btn btn-warning" target="_blank">前端主页</a>
                        <button class="btn" onclick="window.open('http://localhost:5174/', '_blank', 'width=375,height=667')">移动端预览</button>
                    </div>

                    <div class="test-card">
                        <h3>🔧 6. 系统集成测试</h3>
                        <p><strong>测试目标</strong>: 前后端数据一致性</p>
                        <div style="text-align: left; font-size: 0.9em;">
                            <p><strong>测试步骤</strong>:</p>
                            <ol>
                                <li>在前端创建数据，后端验证</li>
                                <li>在后端修改数据，前端刷新</li>
                                <li>测试实时数据同步</li>
                                <li>验证缓存和性能</li>
                            </ol>
                        </div>
                        <button class="btn btn-danger" onclick="testDataSync()">测试数据同步</button>
                        <a href="http://127.0.0.1:8000/api/health/" class="btn" target="_blank">系统状态</a>
                    </div>
                </div>

                <!-- 测试步骤说明 -->
                <div class="test-card" style="margin-top: 20px;">
                    <h3>📋 完整测试流程</h3>
                    <div style="text-align: left;">
                        <h4>🔧 API测试流程：</h4>
                        <ol style="padding-left: 20px;">
                            <li><strong>登录认证</strong>：点击"登录"按钮获取JWT令牌</li>
                            <li><strong>设备管理</strong>：测试设备列表、创建、更新功能</li>
                            <li><strong>借用流程</strong>：测试借用申请、审批、归还流程</li>
                            <li><strong>报表统计</strong>：测试各类统计数据和仪表盘</li>
                            <li><strong>用户管理</strong>：测试用户信息和权限功能</li>
                        </ol>

                        <h4>🖥️ 前端测试流程：</h4>
                        <ol style="padding-left: 20px;">
                            <li><strong>界面访问</strong>：访问 <a href="http://localhost:5174/" target="_blank">http://localhost:5174/</a></li>
                            <li><strong>用户登录</strong>：使用admin/admin123456登录</li>
                            <li><strong>功能测试</strong>：测试设备管理、借用申请等功能</li>
                            <li><strong>数据同步</strong>：验证前后端数据一致性</li>
                        </ol>

                        <h4>🔍 测试重点：</h4>
                        <ul style="padding-left: 20px;">
                            <li>API响应时间和数据格式</li>
                            <li>用户权限和安全控制</li>
                            <li>前后端数据同步</li>
                            <li>错误处理和用户提示</li>
                            <li>移动端适配和响应式设计</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 系统链接 -->
            <div class="section">
                <h2>🔗 系统链接</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>📚 API文档</h3>
                        <p>Swagger UI交互式API文档</p>
                        <a href="http://127.0.0.1:8000/api/docs/" class="btn" target="_blank">查看文档</a>
                    </div>
                    
                    <div class="test-card">
                        <h3>🔧 管理后台</h3>
                        <p>Django Admin管理界面</p>
                        <a href="http://127.0.0.1:8000/admin/" class="btn btn-success" target="_blank">管理后台</a>
                    </div>
                    
                    <div class="test-card">
                        <h3>📊 前端界面</h3>
                        <p>Vue.js前端用户界面</p>
                        <a href="http://localhost:5174/" class="btn btn-warning" target="_blank">前端界面</a>
                    </div>
                    
                    <div class="test-card">
                        <h3>🔍 API Schema</h3>
                        <p>OpenAPI规范文档和原始数据</p>
                        <a href="http://127.0.0.1:8000/api/schema/" class="btn btn-info" target="_blank">API Schema</a>
                    </div>

                    <div class="test-card">
                        <h3>📋 分模块文档</h3>
                        <p>按功能模块分类的API文档</p>
                        <a href="http://127.0.0.1:8000/api/docs/#/用户认证" class="btn" target="_blank">用户认证</a>
                        <a href="http://127.0.0.1:8000/api/docs/#/设备管理" class="btn" target="_blank">设备管理</a>
                    </div>

                    <div class="test-card">
                        <h3>📈 更多模块</h3>
                        <p>其他功能模块的API文档</p>
                        <a href="http://127.0.0.1:8000/api/docs/#/借用管理" class="btn" target="_blank">借用管理</a>
                        <a href="http://127.0.0.1:8000/api/docs/#/报表统计" class="btn" target="_blank">报表统计</a>
                    </div>

                    <div class="test-card">
                        <h3>🏥 系统监控</h3>
                        <p>系统健康状态和监控信息</p>
                        <a href="http://127.0.0.1:8000/api/health/" class="btn btn-success" target="_blank">健康检查</a>
                        <a href="http://127.0.0.1:8000/api/redoc/" class="btn" target="_blank">ReDoc文档</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 设备管理系统 Phase 4 - 系统优化完成 | 开发服务器运行在 http://127.0.0.1:8000/</p>
        </div>
    </div>

    <script>
        // API基础配置
        const API_BASE = 'http://127.0.0.1:8000/api';
        let authToken = localStorage.getItem('authToken');

        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成');
            updateAuthStatus();

            // 测试按钮点击
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) {
                console.log('✅ 找到登录按钮');
                loginBtn.addEventListener('click', function() {
                    console.log('🖱️ 登录按钮被点击');
                });
            } else {
                console.log('❌ 未找到登录按钮');
            }
        });

        // 更新认证状态显示
        function updateAuthStatus() {
            const indicator = document.getElementById('authIndicator');
            const loginBtn = document.getElementById('loginBtn');
            const logoutBtn = document.getElementById('logoutBtn');

            if (authToken) {
                indicator.textContent = '🔓 已登录';
                indicator.style.color = '#28a745';
                loginBtn.style.display = 'none';
                logoutBtn.style.display = 'inline-block';
            } else {
                indicator.textContent = '🔒 未登录';
                indicator.style.color = '#dc3545';
                loginBtn.style.display = 'inline-block';
                logoutBtn.style.display = 'none';
            }
        }

        // 登录函数
        async function login() {
            console.log('🔐 开始登录...');
            showResult('登录中...', '正在发送登录请求...');

            try {
                console.log('📤 发送请求到:', `${API_BASE}/auth/login/`);

                const loginData = {
                    username: 'admin',
                    password: 'admin123456'
                };

                console.log('📋 登录数据:', loginData);

                const response = await fetch(`${API_BASE}/auth/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });

                console.log('📥 收到响应:', response.status, response.statusText);

                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ 登录成功:', data);

                    authToken = data.access;
                    localStorage.setItem('authToken', authToken);
                    updateAuthStatus();
                    showResult('登录成功！', JSON.stringify(data, null, 2));
                } else {
                    const errorText = await response.text();
                    console.log('❌ 登录失败:', response.status, errorText);
                    showResult('登录失败', `状态码: ${response.status}\n响应: ${errorText}`);
                }
            } catch (error) {
                console.error('💥 登录异常:', error);
                showResult('登录错误', `错误: ${error.message}\n请检查网络连接和后端服务状态`);
            }
        }

        // 登出函数
        function logout() {
            authToken = null;
            localStorage.removeItem('authToken');
            updateAuthStatus();
            showResult('已登出', '认证令牌已清除');
        }

        // 测试点击函数
        function testClick() {
            console.log('🖱️ 测试点击按钮被点击');
            alert('测试点击成功！JavaScript正常工作。');
            showResult('测试点击', '按钮点击功能正常');
        }

        // 显示API测试结果
        function showResult(title, content) {
            const resultDiv = document.getElementById('apiResult');
            const resultContent = document.getElementById('resultContent');

            resultContent.textContent = `${title}\n\n${content}`;
            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 通用API请求函数
        async function apiRequest(url, method = 'GET', body = null) {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            try {
                const options = {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                const data = await response.json();

                if (response.ok) {
                    showResult(`${method} ${url} - 成功`, JSON.stringify(data, null, 2));
                } else {
                    showResult(`${method} ${url} - 失败 (${response.status})`, JSON.stringify(data, null, 2));
                }
            } catch (error) {
                showResult('请求错误', error.message);
            }
        }

        // 测试设备列表API
        function testDeviceList() {
            apiRequest(`${API_BASE}/devices/`);
        }

        // 测试借用列表API
        function testLoanList() {
            apiRequest(`${API_BASE}/loans/applications/`);
        }

        // 测试仪表盘API
        function testDashboard() {
            apiRequest(`${API_BASE}/reports/dashboard/`);
        }

        // 测试借用流程
        async function testLoanFlow() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            showResult('借用流程测试', '开始测试借用相关API...');

            // 测试多个借用相关API
            await apiRequest(`${API_BASE}/loans/applications/`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
            await apiRequest(`${API_BASE}/loans/my-applications/`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            await apiRequest(`${API_BASE}/loans/my-loans/`);
        }

        // 测试报表功能
        async function testReports() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            showResult('报表功能测试', '开始测试报表相关API...');

            // 测试多个报表API
            await apiRequest(`${API_BASE}/reports/dashboard/`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            await apiRequest(`${API_BASE}/reports/users/`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            await apiRequest(`${API_BASE}/reports/devices/`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            await apiRequest(`${API_BASE}/reports/loans/`);
        }

        // 测试用户管理
        function testUserManagement() {
            apiRequest(`${API_BASE}/auth/profile/`);
        }

        // 创建测试设备
        async function createTestDevice() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            const testDevice = {
                name: `测试设备-${Date.now()}`,
                model: 'TEST-001',
                serial_number: `SN${Date.now()}`,
                brand: '测试品牌',
                status: 'available',
                special_notes: '通过API测试页面创建的测试设备'
            };

            await apiRequest(`${API_BASE}/devices/`, 'POST', testDevice);
        }

        // 测试错误处理
        async function testErrorHandling() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            showResult('错误处理测试', '开始测试各种错误情况...');

            // 测试不存在的API端点
            await apiRequest(`${API_BASE}/nonexistent/`, 'GET');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 测试无效的设备ID
            await apiRequest(`${API_BASE}/devices/99999/`, 'GET');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 测试无效的数据格式
            await apiRequest(`${API_BASE}/devices/`, 'POST', { invalid: 'data' });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示当前环境信息
            const envInfo = `
测试环境信息：
- 后端API: ${API_BASE}
- 前端界面: http://localhost:5174/
- 管理后台: http://127.0.0.1:8000/admin/
- API文档: http://127.0.0.1:8000/api/docs/
- 当前时间: ${new Date().toLocaleString()}
            `;

            console.log(envInfo);

            // 检查服务可用性
            checkServiceStatus();
        });

        // 检查服务状态
        async function checkServiceStatus() {
            try {
                const response = await fetch(`${API_BASE}/health/`, { method: 'GET' });
                if (response.ok) {
                    console.log('✅ 后端服务正常');
                } else {
                    console.log('⚠️ 后端服务异常');
                }
            } catch (error) {
                console.log('❌ 后端服务不可用:', error.message);
            }
        }

        // 新增的测试函数

        // 测试性能
        async function testPerformance() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            showResult('性能测试', '开始测试API响应时间...');

            const testEndpoints = [
                '/devices/',
                '/reports/dashboard/',
                '/auth/profile/'
            ];

            for (const endpoint of testEndpoints) {
                const startTime = performance.now();
                try {
                    const response = await fetch(`${API_BASE}${endpoint}`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    });
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);

                    if (response.ok) {
                        console.log(`✅ ${endpoint}: ${duration}ms`);
                    } else {
                        console.log(`⚠️ ${endpoint}: ${response.status} (${duration}ms)`);
                    }
                } catch (error) {
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    console.log(`❌ ${endpoint}: ${error.message} (${duration}ms)`);
                }
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            showResult('性能测试完成', '请查看控制台获取详细的响应时间信息');
        }

        // 测试API Schema
        async function testApiSchema() {
            try {
                const response = await fetch(`${API_BASE}/schema/`);
                if (response.ok) {
                    const schema = await response.text();
                    showResult('API Schema验证', `Schema大小: ${schema.length} 字符\n格式: OpenAPI 3.0\n状态: 正常`);
                } else {
                    showResult('Schema验证失败', `状态码: ${response.status}`);
                }
            } catch (error) {
                showResult('Schema验证错误', error.message);
            }
        }

        // 测试CORS
        async function testCORS() {
            showResult('CORS测试', '测试跨域资源共享配置...');

            try {
                // 测试预检请求
                const response = await fetch(`${API_BASE}/auth/login/`, {
                    method: 'OPTIONS'
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                showResult('CORS配置检查', JSON.stringify(corsHeaders, null, 2));
            } catch (error) {
                showResult('CORS测试错误', error.message);
            }
        }

        // 测试数据同步
        async function testDataSync() {
            if (!authToken) {
                showResult('认证错误', '请先登录获取访问令牌');
                return;
            }

            showResult('数据同步测试', '测试前后端数据一致性...');

            try {
                // 1. 创建测试数据
                const testDevice = {
                    name: `同步测试设备-${Date.now()}`,
                    model: 'SYNC-TEST',
                    serial_number: `SYNC${Date.now()}`,
                    brand: '同步测试',
                    status: 'available'
                };

                const createResponse = await fetch(`${API_BASE}/devices/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testDevice)
                });

                if (createResponse.ok) {
                    const createdDevice = await createResponse.json();

                    // 2. 立即查询验证
                    const getResponse = await fetch(`${API_BASE}/devices/${createdDevice.id}/`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    });

                    if (getResponse.ok) {
                        const retrievedDevice = await getResponse.json();
                        const isConsistent = retrievedDevice.name === testDevice.name;

                        showResult('数据同步测试结果',
                            `创建设备: ${createdDevice.name}\n` +
                            `查询设备: ${retrievedDevice.name}\n` +
                            `数据一致性: ${isConsistent ? '✅ 通过' : '❌ 失败'}`
                        );
                    }
                }
            } catch (error) {
                showResult('数据同步测试错误', error.message);
            }
        }
    </script>
</body>
</html>
