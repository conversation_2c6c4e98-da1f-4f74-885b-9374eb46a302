<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔧 登录功能调试</h1>
    
    <div class="status info" id="status">
        状态: 页面已加载
    </div>
    
    <div>
        <button class="btn" onclick="testLogin()">测试登录</button>
        <button class="btn" onclick="testAPI()">测试API连接</button>
        <button class="btn" onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        
        function log(message, type = 'info') {
            console.log(message);
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = `状态: ${message}`;
            status.className = `status ${type}`;
        }
        
        async function testAPI() {
            log('🔍 测试API连接...', 'info');
            updateStatus('测试API连接中...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/health/`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API连接成功: ${JSON.stringify(data)}`, 'success');
                    updateStatus('API连接正常', 'success');
                } else {
                    log(`⚠️ API响应异常: ${response.status} ${response.statusText}`, 'error');
                    updateStatus('API响应异常', 'error');
                }
            } catch (error) {
                log(`❌ API连接失败: ${error.message}`, 'error');
                updateStatus('API连接失败', 'error');
            }
        }
        
        async function testLogin() {
            log('🔐 开始测试登录...', 'info');
            updateStatus('登录测试中...', 'info');
            
            try {
                log('📤 发送登录请求...', 'info');
                
                const loginData = {
                    username: 'admin',
                    password: 'admin123456'
                };
                
                log(`📋 登录数据: ${JSON.stringify(loginData)}`, 'info');
                
                const response = await fetch(`${API_BASE}/auth/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                log(`📥 收到响应: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 登录成功!`, 'success');
                    log(`🔑 访问令牌: ${data.access ? '已获取' : '未获取'}`, data.access ? 'success' : 'error');
                    log(`👤 用户信息: ${JSON.stringify(data.user || {})}`, 'info');
                    
                    // 保存令牌
                    if (data.access) {
                        localStorage.setItem('authToken', data.access);
                        log('💾 令牌已保存到本地存储', 'success');
                    }
                    
                    updateStatus('登录成功', 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ 登录失败: ${response.status}`, 'error');
                    log(`📄 错误详情: ${errorText}`, 'error');
                    updateStatus('登录失败', 'error');
                }
            } catch (error) {
                log(`💥 登录异常: ${error.message}`, 'error');
                log(`🔍 错误堆栈: ${error.stack}`, 'error');
                updateStatus('登录异常', 'error');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('结果已清除', 'info');
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 调试页面已加载', 'success');
            updateStatus('页面已就绪', 'success');
            
            // 检查本地存储的令牌
            const token = localStorage.getItem('authToken');
            if (token) {
                log(`🔑 发现本地令牌: ${token.substring(0, 20)}...`, 'info');
            } else {
                log('🔒 未发现本地令牌', 'info');
            }
        });
        
        // 全局错误处理
        window.addEventListener('error', function(e) {
            log(`💥 JavaScript错误: ${e.message}`, 'error');
            log(`📍 错误位置: ${e.filename}:${e.lineno}:${e.colno}`, 'error');
        });
        
        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', function(e) {
            log(`💥 未处理的Promise错误: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
