# MDM测试工具索引

## 📁 完整目录结构

```
scripts/test/
├── README.md                    # 测试工具总览和使用指南
├── INDEX.md                     # 本文件 - 完整文件索引
├── testing_guide.md            # 详细测试方法指南
├── test_report.md              # 当前测试状态报告
│
├── launchers/                   # 🚀 启动器和菜单
│   ├── test_menu.bat           # 主测试菜单 (原根目录test.bat)
│   ├── start_test_env.bat      # 测试环境选择器
│   └── test-frontend.bat       # 前端配置测试
│
├── api/                        # 🔧 API接口测试工具
│   ├── quick_api_test.py       # 快速API功能测试
│   ├── validate_api.py         # 完整API验证脚本
│   ├── test_reports_api.py     # 报表API专项测试
│   ├── test_admin_login.py     # 管理员登录测试
│   ├── test_doc_links.py       # API文档链接验证
│   └── check_api_docs.py       # API文档结构检查
│
├── data/                       # 📊 测试数据管理
│   ├── create_test_data.py     # 创建基础测试数据
│   ├── create_test_user.py     # 创建测试用户
│   └── cleanup_test_data.py    # 清理和重置测试数据
│
├── ui/                         # 🖥️ 用户界面测试
│   ├── test_manual.html        # 可视化手动测试页面
│   └── debug_login.html        # 登录功能调试页面
│
└── config/                     # ⚙️ 测试环境配置
    └── settings_test.py        # Django测试环境配置
```

## 🚀 快速启动指南

### 主要入口点

| 文件 | 用途 | 命令 |
|------|------|------|
| **根目录/test.bat** | 快速启动主菜单 | `test.bat` |
| **launchers/test_menu.bat** | 完整测试菜单 | `scripts\test\launchers\test_menu.bat` |
| **launchers/start_test_env.bat** | 测试环境选择器 | `scripts\test\launchers\start_test_env.bat` |
| **ui/test_manual.html** | 可视化测试界面 | 浏览器打开 |

### 常用测试命令

```bash
# 1. 启动测试菜单
test.bat

# 2. 快速API测试
python scripts\test\api\quick_api_test.py

# 3. 完整API验证
python scripts\test\api\validate_api.py

# 4. 创建测试数据
python scripts\test\data\create_test_data.py

# 5. 清理测试数据
python scripts\test\data\cleanup_test_data.py

# 6. 启动测试环境
scripts\test\launchers\start_test_env.bat
```

## 📋 文件功能详解

### 🚀 启动器 (launchers/)

- **test_menu.bat**: 主测试菜单，提供所有测试功能的统一入口
- **start_test_env.bat**: 测试环境启动器，支持内存数据库、独立测试库等
- **test-frontend.bat**: 前端配置和依赖测试

### 🔧 API测试 (api/)

- **quick_api_test.py**: 快速验证所有API端点基本功能
- **validate_api.py**: 完整的API功能验证，包含详细日志
- **test_reports_api.py**: 专门测试报表和统计API
- **test_admin_login.py**: 验证管理员登录和权限
- **test_doc_links.py**: 检查API文档链接有效性
- **check_api_docs.py**: 分析API文档结构和标签

### 📊 数据管理 (data/)

- **create_test_data.py**: 创建用户、设备、借用记录等测试数据
- **create_test_user.py**: 专门创建测试用户账户
- **cleanup_test_data.py**: 清理测试数据，支持选择性清理和完全重置

### 🖥️ UI测试 (ui/)

- **test_manual.html**: 完整的可视化测试界面，包含API测试和业务流程测试
- **debug_login.html**: 专门用于调试登录功能的简化页面

### ⚙️ 配置 (config/)

- **settings_test.py**: Django测试环境配置，使用内存数据库

## 🎯 测试场景对应工具

### 开发阶段测试
```bash
# 快速验证API功能
python scripts\test\api\quick_api_test.py

# 创建基础测试数据
python scripts\test\data\create_test_data.py

# 使用可视化界面测试
start scripts\test\ui\test_manual.html
```

### 集成测试
```bash
# 启动独立测试环境
scripts\test\launchers\start_test_env.bat
# 选择: 2. 独立测试数据库环境

# 完整API验证
python scripts\test\api\validate_api.py

# 测试完成后清理
python scripts\test\data\cleanup_test_data.py
```

### 生产前测试
```bash
# 使用开发环境进行最终测试
scripts\test\launchers\start_test_env.bat
# 选择: 3. 当前开发环境

# 全面功能测试
start scripts\test\ui\test_manual.html

# 验证文档完整性
python scripts\test\api\test_doc_links.py
```

## 📝 使用注意事项

### 数据安全
- **内存环境**: 数据不持久化，最安全
- **测试环境**: 使用独立数据库，相对安全
- **开发环境**: 数据会保存，需要手动清理

### 路径依赖
- 所有脚本都已更新为相对于项目根目录的路径
- 可以从任何位置运行根目录的 `test.bat`
- 子目录中的脚本需要注意工作目录

### 环境要求
- Python虚拟环境: `backend\venv\`
- 数据库服务: PostgreSQL (localhost:5432)
- 缓存服务: Redis (localhost:6379)
- 前端服务: Node.js + npm

## 🔄 维护和更新

### 添加新测试工具
1. 根据功能类型放入对应目录
2. 更新 `launchers/test_menu.bat` 菜单
3. 更新本索引文件

### 路径更新
- 移动文件后需要更新相关脚本中的路径引用
- 注意Django配置文件的位置要求
- 确保相对路径的正确性

---

**最后更新**: 2025-07-29  
**维护者**: MDM开发团队
