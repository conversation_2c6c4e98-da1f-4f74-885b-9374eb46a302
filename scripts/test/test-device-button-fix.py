#!/usr/bin/env python3
"""
测试设备添加按钮修复效果的脚本
"""

import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class DeviceButtonTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token = None
        
    def login(self, username, password):
        """用户登录"""
        url = f"{self.base_url}/api/auth/login/"
        data = {
            "username": username,
            "password": password
        }
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            
            result = response.json()
            self.access_token = result['access']
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}'
            })
            
            print(f"✅ 登录成功: {username}")
            print(f"   用户角色: {result['user']['role']}")
            print(f"   是否设备管理员: {result['user'].get('is_device_admin', False)}")
            return result['user']
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 登录失败: {e}")
            return None
    
    def test_device_creation_permission(self, user_role):
        """测试设备创建权限"""
        url = f"{self.base_url}/api/devices/"
        test_device = {
            "name": f"测试设备-{user_role}",
            "model": "Test Model",
            "serial_number": f"TEST-{user_role}-001",
            "brand": "Test Brand",
            "status": "in_stock"
        }
        
        try:
            response = self.session.post(url, json=test_device)
            
            if response.status_code == 201:
                print(f"✅ {user_role} 用户可以创建设备")
                # 清理测试数据
                device_id = response.json()['id']
                self.session.delete(f"{url}{device_id}/")
                return True
            elif response.status_code == 403:
                print(f"⚠️  {user_role} 用户无权限创建设备 (符合预期)")
                return False
            else:
                print(f"❌ 意外的响应状态: {response.status_code}")
                print(f"   响应内容: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def test_device_list_access(self):
        """测试设备列表访问"""
        url = f"{self.base_url}/api/devices/"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ 可以访问设备列表，共 {result['count']} 个设备")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 访问设备列表失败: {e}")
            return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("设备添加按钮修复效果测试")
    print("=" * 60)
    
    tester = DeviceButtonTester()
    
    # 测试用例配置
    test_users = [
        {"username": "admin", "password": "admin123", "expected_can_create": True},
        {"username": "testuser", "password": "test123", "expected_can_create": False},
    ]
    
    for user_config in test_users:
        print(f"\n--- 测试用户: {user_config['username']} ---")
        
        # 登录
        user = tester.login(user_config['username'], user_config['password'])
        if not user:
            print(f"跳过用户 {user_config['username']} 的测试")
            continue
        
        # 测试设备列表访问
        tester.test_device_list_access()
        
        # 测试设备创建权限
        can_create = tester.test_device_creation_permission(user['role'])
        expected = user_config['expected_can_create']
        
        if can_create == expected:
            print(f"✅ 权限测试通过")
        else:
            print(f"❌ 权限测试失败: 期望 {expected}, 实际 {can_create}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
