@echo off
setlocal enabledelayedexpansion

REM ===========================================
REM 环境同步检查脚本
REM ===========================================
REM 用途: 检查所有环境的配置一致性
REM 检查项: Celery配置、权限配置、依赖包等
REM ===========================================

echo [INFO] 环境同步检查脚本
echo.

REM 检查requirements.txt中的关键包
echo === 1. 检查依赖包配置 ===
echo [INFO] 检查requirements.txt...
findstr /C:"django-celery-beat" backend\requirements.txt >nul
if not errorlevel 1 (
    echo [✅] requirements.txt 包含 django-celery-beat
) else (
    echo [❌] requirements.txt 缺少 django-celery-beat
)

echo [INFO] 检查requirements-prod.txt...
findstr /C:"django-celery-beat" backend\requirements-prod.txt >nul
if not errorlevel 1 (
    echo [✅] requirements-prod.txt 包含 django-celery-beat
) else (
    echo [❌] requirements-prod.txt 缺少 django-celery-beat
)

echo.

REM 检查Django设置
echo === 2. 检查Django配置 ===
echo [INFO] 检查INSTALLED_APPS...
findstr /C:"django_celery_beat" backend\config\settings.py >nul
if not errorlevel 1 (
    echo [✅] Django settings 包含 django_celery_beat
) else (
    echo [❌] Django settings 缺少 django_celery_beat
)

echo.

REM 检查Docker Compose配置
echo === 3. 检查Docker Compose配置 ===

echo [INFO] 检查开发环境 (docker-compose.dev.yml)...
findstr /C:"celery-beat" docker-compose.dev.yml >nul
if not errorlevel 1 (
    echo [✅] 开发环境包含 celery-beat 服务
    findstr /C:"DatabaseScheduler" docker-compose.dev.yml >nul
    if not errorlevel 1 (
        echo [✅] 开发环境使用 DatabaseScheduler
    ) else (
        echo [⚠️] 开发环境未使用 DatabaseScheduler
    )
) else (
    echo [❌] 开发环境缺少 celery-beat 服务
)

echo [INFO] 检查生产环境 (docker-compose.prod.yml)...
findstr /C:"celery-beat" docker-compose.prod.yml >nul
if not errorlevel 1 (
    echo [✅] 生产环境包含 celery-beat 服务
    findstr /C:"DatabaseScheduler" docker-compose.prod.yml >nul
    if not errorlevel 1 (
        echo [✅] 生产环境使用 DatabaseScheduler
    ) else (
        echo [⚠️] 生产环境未使用 DatabaseScheduler
    )
) else (
    echo [❌] 生产环境缺少 celery-beat 服务
)

echo [INFO] 检查精简生产环境 (docker-compose.prod-lite.yml)...
findstr /C:"celery-beat" docker-compose.prod-lite.yml >nul
if not errorlevel 1 (
    echo [✅] 精简生产环境包含 celery-beat 服务
    findstr /C:"DatabaseScheduler" docker-compose.prod-lite.yml >nul
    if not errorlevel 1 (
        echo [✅] 精简生产环境使用 DatabaseScheduler
    ) else (
        echo [⚠️] 精简生产环境未使用 DatabaseScheduler
    )
) else (
    echo [❌] 精简生产环境缺少 celery-beat 服务
)

echo.

REM 检查迁移脚本
echo === 4. 检查迁移脚本 ===
if exist "scripts\migrate-celery-beat.bat" (
    echo [✅] Windows迁移脚本存在
) else (
    echo [❌] Windows迁移脚本缺失
)

if exist "scripts\migrate-celery-beat.sh" (
    echo [✅] Linux迁移脚本存在
) else (
    echo [❌] Linux迁移脚本缺失
)

echo.

REM 检查部署脚本
echo === 5. 检查部署脚本 ===
findstr /C:"django_celery_beat" scripts\deploy.sh >nul
if not errorlevel 1 (
    echo [✅] 部署脚本包含 django_celery_beat 迁移
) else (
    echo [❌] 部署脚本缺少 django_celery_beat 迁移
)

echo.

REM 检查启动脚本
echo === 6. 检查启动脚本 ===
findstr /C:"django_celery_beat" start-dev.bat >nul
if not errorlevel 1 (
    echo [✅] 开发启动脚本包含 django_celery_beat 迁移
) else (
    echo [❌] 开发启动脚本缺少 django_celery_beat 迁移
)

echo.

REM 检查环境变量文件
echo === 7. 检查环境变量配置 ===
if exist ".env.dev" (
    echo [✅] 开发环境变量文件存在
) else (
    echo [❌] 开发环境变量文件缺失
)

if exist ".env" (
    echo [✅] 生产环境变量文件存在
) else (
    echo [⚠️] 生产环境变量文件缺失 (可能正常)
)

echo.

REM 总结
echo === 检查总结 ===
echo [INFO] 环境同步检查完成
echo.
echo 如果发现问题，请参考以下解决方案：
echo 1. 缺少依赖包: 在requirements.txt中添加 django-celery-beat==2.5.0
echo 2. Django配置: 在settings.py的INSTALLED_APPS中添加 'django_celery_beat'
echo 3. 缺少服务: 在docker-compose文件中添加celery-beat服务配置
echo 4. 缺少迁移: 运行 scripts\migrate-celery-beat.bat ^<environment^>
echo.
echo 详细修复指南请查看: docs/ADMIN_SETUP_GUIDE.md
echo.

pause
