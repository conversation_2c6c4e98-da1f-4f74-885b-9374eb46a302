#!/bin/bash

# ===========================================
# 设备管理系统生产环境部署脚本
# ===========================================
# 使用方法: ./scripts/deploy.sh [环境]
# 环境选项: dev, test, prod

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        log_error "文件不存在: $1"
        exit 1
    fi
}

# 获取环境参数
ENVIRONMENT=${1:-dev}
COMPOSE_FILE="docker-compose.yml"

case $ENVIRONMENT in
    dev|development)
        ENVIRONMENT="development"
        ENV_FILE=".env"
        ;;
    test|testing)
        ENVIRONMENT="testing"
        ENV_FILE=".env.test"
        COMPOSE_FILE="docker-compose.yml:docker-compose.test.yml"
        ;;
    prod|production)
        ENVIRONMENT="production"
        ENV_FILE=".env.production"
        COMPOSE_FILE="docker-compose.yml:docker-compose.prod.yml"
        ;;
    *)
        log_error "无效的环境参数: $ENVIRONMENT"
        log_info "支持的环境: dev, test, prod"
        exit 1
        ;;
esac

log_info "开始部署设备管理系统到 $ENVIRONMENT 环境..."

# 检查必要的命令
log_info "检查系统依赖..."
check_command "docker"
check_command "docker-compose"
check_command "git"

# 检查必要的文件
log_info "检查配置文件..."
check_file "$ENV_FILE"

# 设置环境变量
export COMPOSE_FILE
export ENVIRONMENT

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p logs backups ssl

# 如果是生产环境，创建数据目录
if [ "$ENVIRONMENT" = "production" ]; then
    sudo mkdir -p /opt/mdm/{data/{postgres,redis},static,media,logs,backups}
    sudo chown -R $USER:$USER /opt/mdm
fi

# 备份数据库 (生产环境)
if [ "$ENVIRONMENT" = "production" ] && docker-compose ps | grep -q "mdm_db.*Up"; then
    log_info "备份现有数据库..."
    BACKUP_FILE="backups/backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T db pg_dump -U ${POSTGRES_USER:-mdm_user} ${POSTGRES_DB:-mdm_prod} > $BACKUP_FILE
    log_success "数据库备份完成: $BACKUP_FILE"
fi

# 拉取最新代码 (如果在Git仓库中)
if [ -d ".git" ]; then
    log_info "拉取最新代码..."
    git pull origin main
    log_success "代码更新完成"
fi

# 构建镜像
log_info "构建Docker镜像..."
docker-compose -f $COMPOSE_FILE build --no-cache

# 停止现有服务
log_info "停止现有服务..."
docker-compose -f $COMPOSE_FILE down

# 启动服务
log_info "启动服务..."
if [ "$ENVIRONMENT" = "production" ]; then
    docker-compose -f $COMPOSE_FILE up -d --remove-orphans
else
    docker-compose -f $COMPOSE_FILE up -d
fi

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."
if ! docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
    log_error "服务启动失败"
    docker-compose -f $COMPOSE_FILE logs
    exit 1
fi

# 数据库迁移
log_info "执行数据库迁移..."
docker-compose -f $COMPOSE_FILE exec -T backend python manage.py migrate

# Celery Beat迁移
log_info "执行Celery Beat迁移..."
docker-compose -f $COMPOSE_FILE exec -T backend python manage.py migrate django_celery_beat

# 收集静态文件
log_info "收集静态文件..."
docker-compose -f $COMPOSE_FILE exec -T backend python manage.py collectstatic --noinput

# 创建超级用户 (仅开发环境)
if [ "$ENVIRONMENT" = "development" ]; then
    log_info "创建测试数据..."
    docker-compose -f $COMPOSE_FILE exec -T backend python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('超级用户创建成功')
else:
    print('超级用户已存在')
"
fi

# 健康检查
log_info "执行健康检查..."
HEALTH_CHECK_URL="http://localhost:8000/api/health/"
if [ "$ENVIRONMENT" = "production" ]; then
    HEALTH_CHECK_URL="https://yourdomain.com/api/health/"
fi

# 等待服务完全启动
sleep 10

if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
    log_success "健康检查通过"
else
    log_warning "健康检查失败，检查服务日志"
    docker-compose -f $COMPOSE_FILE logs backend
fi

# 显示服务状态
log_info "服务状态:"
docker-compose -f $COMPOSE_FILE ps

# 显示访问信息
log_success "部署完成！"
echo ""
echo "===========================================  "
echo "🎉 设备管理系统部署成功！"
echo "==========================================="
echo "环境: $ENVIRONMENT"
echo "-------------------------------------------"

if [ "$ENVIRONMENT" = "development" ]; then
    echo "🌐 前端应用: http://localhost:3000"
    echo "🔧 后端API: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/api/docs/"
    echo "🔐 管理后台: http://localhost:8000/admin/"
    echo "📊 监控面板: http://localhost:3001 (admin/admin123)"
elif [ "$ENVIRONMENT" = "production" ]; then
    echo "🌐 网站地址: https://yourdomain.com"
    echo "📚 API文档: https://yourdomain.com/api/docs/"
    echo "🔐 管理后台: https://yourdomain.com/admin/"
    echo "📊 监控面板: https://yourdomain.com:3001"
fi

echo "-------------------------------------------"
echo "📋 管理命令:"
echo "  查看日志: docker-compose -f $COMPOSE_FILE logs -f"
echo "  重启服务: docker-compose -f $COMPOSE_FILE restart"
echo "  停止服务: docker-compose -f $COMPOSE_FILE down"
echo "  进入容器: docker-compose -f $COMPOSE_FILE exec backend bash"
echo "==========================================="

# 生产环境额外提示
if [ "$ENVIRONMENT" = "production" ]; then
    echo ""
    log_warning "生产环境部署完成，请确保:"
    echo "  1. 配置了正确的域名和SSL证书"
    echo "  2. 设置了强密码和安全密钥"
    echo "  3. 配置了防火墙和安全组"
    echo "  4. 设置了监控和告警"
    echo "  5. 配置了定期备份"
    echo "  6. 进行了完整的功能测试"
fi

log_success "部署脚本执行完成！"
