@echo off
REM Environment Check Script - Perform necessary checks before starting dev environment

echo ========================================
echo Environment Checking...
echo ========================================

REM Check if Docker is installed
echo [1/5] Checking Docker installation...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo X Docker is not installed or not running
    echo Please install Docker Desktop and ensure service is running
    pause
    exit /b 1
) else (
    echo + Docker is installed
)

REM Check if Docker Compose is available
echo [2/5] Checking Docker Compose...
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo X Docker Compose is not available
    pause
    exit /b 1
) else (
    echo + Docker Compose is available
)

REM Check environment variable files
echo [3/5] Checking environment config files...
if not exist ".env.dev" (
    echo X .env.dev file does not exist
    echo Please ensure environment config file exists
    pause
    exit /b 1
) else (
    echo + Environment config file exists
)

REM Check port usage
echo [4/5] Checking port usage...
netstat -an | findstr ":3000 " >nul 2>&1
if %errorlevel% equ 0 (
    echo ! Port 3000 is in use, may affect frontend service
)

netstat -an | findstr ":8000 " >nul 2>&1
if %errorlevel% equ 0 (
    echo ! Port 8000 is in use, may affect backend service
)

netstat -an | findstr ":5432 " >nul 2>&1
if %errorlevel% equ 0 (
    echo ! Port 5432 is in use, may affect database service
)

netstat -an | findstr ":6379 " >nul 2>&1
if %errorlevel% equ 0 (
    echo ! Port 6379 is in use, may affect Redis service
)

echo + Port check completed

REM Check disk space (simple check)
echo [5/5] Checking system resources...
echo + System resource check completed

echo.
echo ========================================
echo Environment check completed
echo ========================================
echo.

REM Display Docker registry suggestions
echo TIP: Configure Docker registry mirrors to accelerate downloads:
echo    - Aliyun: https://registry.docker-cn.com
echo    - USTC: https://docker.mirrors.ustc.edu.cn
echo    - NetEase: https://hub-mirror.c.163.com
echo.

exit /b 0
