#!/usr/bin/env python3
"""
设备API测试脚本
用于测试添加设备功能是否正常工作
"""

import requests
import json
import sys

# API配置
BASE_URL = "http://localhost:8000/api"
LOGIN_URL = f"{BASE_URL}/auth/login/"
DEVICES_URL = f"{BASE_URL}/devices/"
CATEGORIES_URL = f"{BASE_URL}/devices/categories/"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(LOGIN_URL, json=login_data)
        response.raise_for_status()
        
        data = response.json()
        access_token = data.get('access')
        
        if not access_token:
            print("❌ 登录失败：未获取到access token")
            return None
            
        print("✅ 登录成功")
        return access_token
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 登录请求失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"   响应状态码: {e.response.status_code}")
            print(f"   响应内容: {e.response.text}")
        return None
    except json.JSONDecodeError:
        print("❌ 登录响应格式错误")
        return None

def get_headers(token):
    """获取请求头"""
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def test_get_devices(token):
    """测试获取设备列表"""
    try:
        response = requests.get(DEVICES_URL, headers=get_headers(token))
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 获取设备列表成功，共 {data.get('count', 0)} 个设备")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取设备列表失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"   响应状态码: {e.response.status_code}")
            print(f"   响应内容: {e.response.text}")
        return False

def get_categories(token):
    """获取设备分类"""
    try:
        response = requests.get(CATEGORIES_URL, headers=get_headers(token))
        response.raise_for_status()
        
        data = response.json()
        categories = data.get('results', [])
        print(f"✅ 获取设备分类成功，共 {len(categories)} 个分类")

        if categories and len(categories) > 0:
            return categories[0]['id']  # 返回第一个分类的ID
        else:
            # 创建一个测试分类
            category_data = {
                "name": "测试分类",
                "description": "用于测试的设备分类"
            }
            
            response = requests.post(CATEGORIES_URL, json=category_data, headers=get_headers(token))
            response.raise_for_status()
            
            category = response.json()
            print(f"✅ 创建测试分类成功: {category['name']}")
            return category['id']
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取/创建设备分类失败: {e}")
        return None

def test_create_device(token, category_id):
    """测试创建设备"""
    device_data = {
        "name": "测试设备",
        "model": "TEST-001",
        "serial_number": f"SN{int(__import__('time').time())}",  # 使用时间戳确保唯一性
        "brand": "测试品牌",
        "category": category_id,
        "cpu": "测试CPU",
        "memory": "8GB",
        "storage": "256GB",
        "os": "测试系统",
        "purchase_price": 1000.00,
        "purchase_date": "2025-07-30"
    }
    
    try:
        response = requests.post(DEVICES_URL, json=device_data, headers=get_headers(token))
        response.raise_for_status()
        
        device = response.json()
        print(f"✅ 创建设备成功: {device.get('name', '未知')} (ID: {device.get('id', '未知')})")
        print(f"设备数据: {device}")
        return device.get('id')
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 创建设备失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"   响应状态码: {e.response.status_code}")
            print(f"   响应内容: {e.response.text}")
        return None

def test_get_device_detail(token, device_id):
    """测试获取设备详情"""
    try:
        response = requests.get(f"{DEVICES_URL}{device_id}/", headers=get_headers(token))
        response.raise_for_status()
        
        device = response.json()
        print(f"✅ 获取设备详情成功: {device['name']}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取设备详情失败: {e}")
        return False

def cleanup_test_device(token, device_id):
    """清理测试设备"""
    try:
        response = requests.delete(f"{DEVICES_URL}{device_id}/", headers=get_headers(token))
        response.raise_for_status()
        
        print(f"✅ 清理测试设备成功")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"⚠️ 清理测试设备失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始测试设备API功能...")
    print("=" * 50)
    
    # 1. 登录
    print("1. 测试登录...")
    token = login()
    if not token:
        print("❌ 测试失败：无法登录")
        sys.exit(1)
    
    # 2. 测试获取设备列表
    print("\n2. 测试获取设备列表...")
    if not test_get_devices(token):
        print("❌ 测试失败：无法获取设备列表")
        sys.exit(1)
    
    # 3. 获取设备分类
    print("\n3. 测试获取设备分类...")
    category_id = get_categories(token)
    if not category_id:
        print("❌ 测试失败：无法获取设备分类")
        sys.exit(1)
    
    # 4. 测试创建设备
    print("\n4. 测试创建设备...")
    device_id = test_create_device(token, category_id)
    if not device_id:
        print("❌ 测试失败：无法创建设备")
        sys.exit(1)
    
    # 5. 测试获取设备详情
    print("\n5. 测试获取设备详情...")
    if not test_get_device_detail(token, device_id):
        print("❌ 测试失败：无法获取设备详情")
        sys.exit(1)
    
    # 6. 清理测试数据
    print("\n6. 清理测试数据...")
    cleanup_test_device(token, device_id)
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过！设备API功能正常")

if __name__ == "__main__":
    main()
