#!/bin/bash

# ===========================================
# Celery Beat数据库迁移脚本
# ===========================================
# 用途: 为django_celery_beat应用创建数据库表
# 适用: 所有环境 (dev/prod/prod-lite)
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    log_error "请指定环境: dev, prod, 或 prod-lite"
    echo "用法: $0 <environment>"
    echo "示例: $0 dev"
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
case $ENVIRONMENT in
    dev|prod|prod-lite)
        log_info "开始为 $ENVIRONMENT 环境执行Celery Beat迁移..."
        ;;
    *)
        log_error "无效的环境参数: $ENVIRONMENT"
        log_error "支持的环境: dev, prod, prod-lite"
        exit 1
        ;;
esac

# 设置容器名称
case $ENVIRONMENT in
    dev)
        CONTAINER_NAME="mdm_backend_dev"
        COMPOSE_FILE="docker-compose.dev.yml"
        ;;
    prod)
        CONTAINER_NAME="mdm_backend_prod"
        COMPOSE_FILE="docker-compose.prod.yml"
        ;;
    prod-lite)
        CONTAINER_NAME="mdm_backend_prod_lite"
        COMPOSE_FILE="docker-compose.prod-lite.yml"
        ;;
esac

# 检查容器是否运行
log_info "检查容器状态..."
if ! docker ps | grep -q $CONTAINER_NAME; then
    log_error "容器 $CONTAINER_NAME 未运行"
    log_info "请先启动环境: start-$ENVIRONMENT.bat"
    exit 1
fi

log_success "容器 $CONTAINER_NAME 正在运行"

# 执行迁移
log_info "执行django_celery_beat数据库迁移..."

# 1. 创建迁移文件
log_info "1. 创建迁移文件..."
docker exec $CONTAINER_NAME python manage.py makemigrations django_celery_beat

# 2. 应用迁移
log_info "2. 应用数据库迁移..."
docker exec $CONTAINER_NAME python manage.py migrate django_celery_beat

# 3. 验证迁移
log_info "3. 验证迁移结果..."
docker exec $CONTAINER_NAME python manage.py shell -c "
from django_celery_beat.models import PeriodicTask, IntervalSchedule, CrontabSchedule
print('✅ django_celery_beat表已创建:')
print(f'  - PeriodicTask: {PeriodicTask.objects.count()} 个任务')
print(f'  - IntervalSchedule: {IntervalSchedule.objects.count()} 个间隔调度')
print(f'  - CrontabSchedule: {CrontabSchedule.objects.count()} 个Cron调度')
"

# 4. 重启celery-beat容器
log_info "4. 重启celery-beat容器..."
case $ENVIRONMENT in
    dev)
        BEAT_CONTAINER="mdm_celery_beat_dev"
        ;;
    prod)
        BEAT_CONTAINER="mdm_celery_beat_prod"
        ;;
    prod-lite)
        BEAT_CONTAINER="mdm_prod_lite_celery_beat"
        ;;
esac

if docker ps | grep -q $BEAT_CONTAINER; then
    log_info "重启 $BEAT_CONTAINER 容器..."
    docker restart $BEAT_CONTAINER
    sleep 5
    
    # 检查容器状态
    if docker ps | grep -q $BEAT_CONTAINER; then
        log_success "$BEAT_CONTAINER 重启成功"
    else
        log_warning "$BEAT_CONTAINER 重启后未运行，请检查日志"
        log_info "查看日志: docker logs $BEAT_CONTAINER"
    fi
else
    log_warning "$BEAT_CONTAINER 容器未运行，请手动启动"
fi

log_success "Celery Beat迁移完成！"
log_info "现在可以使用DatabaseScheduler进行定时任务管理"

# 显示使用说明
echo ""
echo "=== 使用说明 ==="
echo "1. 访问Django管理后台: http://localhost:8000/admin/ (dev) 或 http://localhost/admin/ (prod)"
echo "2. 在 'DJANGO CELERY BEAT' 部分可以管理定时任务"
echo "3. 创建周期性任务、间隔调度、Cron调度等"
echo "4. 查看celery-beat日志: docker logs $BEAT_CONTAINER"
echo ""
