#!/usr/bin/env python
"""
创建超级管理员用户的脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def create_superuser():
    """创建超级管理员用户"""
    username = 'admin'
    email = '<EMAIL>'
    password = 'admin123456'
    
    # 检查用户是否已存在
    if User.objects.filter(username=username).exists():
        print(f"超级用户 '{username}' 已存在")
        user = User.objects.get(username=username)
        print(f"用户信息: {user.username} ({user.email})")
        return user
    
    # 创建超级用户
    try:
        user = User.objects.create_superuser(
            username=username,
            email=email,
            password=password
        )
        print(f"超级用户创建成功!")
        print(f"用户名: {username}")
        print(f"邮箱: {email}")
        print(f"密码: {password}")
        print(f"管理后台地址: http://localhost:8000/admin/")
        return user
    except Exception as e:
        print(f"创建超级用户失败: {e}")
        return None

if __name__ == '__main__':
    create_superuser()
