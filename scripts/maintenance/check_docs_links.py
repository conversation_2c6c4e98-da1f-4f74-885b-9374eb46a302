#!/usr/bin/env python3
"""
文档链接检查脚本

用于检查 docs 目录下所有 Markdown 文件中的内部链接是否有效。

使用方法:
    python scripts/maintenance/check_docs_links.py

功能:
1. 扫描所有 .md 文件
2. 提取内部链接
3. 检查链接目标是否存在
4. 生成检查报告
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
from urllib.parse import unquote


class DocsLinkChecker:
    def __init__(self, docs_root: str = "docs"):
        self.docs_root = Path(docs_root)
        self.base_path = Path.cwd()
        self.broken_links = []
        self.valid_links = []
        
    def find_markdown_files(self) -> List[Path]:
        """查找所有 Markdown 文件"""
        md_files = []
        for root, dirs, files in os.walk(self.docs_root):
            for file in files:
                if file.endswith('.md'):
                    md_files.append(Path(root) / file)
        return md_files
    
    def extract_links(self, file_path: Path) -> List[Tuple[str, int]]:
        """从 Markdown 文件中提取内部链接"""
        links = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 匹配 Markdown 链接格式: [text](link)
            link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
            matches = re.finditer(link_pattern, content)
            
            for match in matches:
                link_text = match.group(1)
                link_url = match.group(2)
                line_num = content[:match.start()].count('\n') + 1
                
                # 只检查内部链接（不以 http:// 或 https:// 开头）
                if not link_url.startswith(('http://', 'https://', 'mailto:')):
                    # 移除锚点
                    link_url = link_url.split('#')[0]
                    if link_url:  # 忽略纯锚点链接
                        links.append((link_url, line_num))
                        
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
            
        return links
    
    def resolve_link_path(self, file_path: Path, link_url: str) -> Path:
        """解析链接的绝对路径"""
        # URL 解码
        link_url = unquote(link_url)
        
        # 获取当前文件所在目录
        current_dir = file_path.parent
        
        # 解析相对路径
        if link_url.startswith('./'):
            # 当前目录
            target_path = current_dir / link_url[2:]
        elif link_url.startswith('../'):
            # 上级目录
            target_path = current_dir / link_url
        elif link_url.startswith('/'):
            # 根目录（相对于项目根目录）
            target_path = self.base_path / link_url[1:]
        else:
            # 相对于当前目录
            target_path = current_dir / link_url
            
        return target_path.resolve()
    
    def check_link_exists(self, target_path: Path) -> bool:
        """检查链接目标是否存在"""
        if target_path.exists():
            return True
            
        # 如果是目录，检查是否有 README.md
        if target_path.is_dir():
            readme_path = target_path / 'README.md'
            return readme_path.exists()
            
        # 如果没有扩展名，尝试添加 .md
        if not target_path.suffix:
            md_path = target_path.with_suffix('.md')
            return md_path.exists()
            
        return False
    
    def check_file_links(self, file_path: Path) -> Dict:
        """检查单个文件的所有链接"""
        result = {
            'file': str(file_path),
            'total_links': 0,
            'valid_links': 0,
            'broken_links': [],
            'valid_link_list': []
        }
        
        links = self.extract_links(file_path)
        result['total_links'] = len(links)
        
        for link_url, line_num in links:
            target_path = self.resolve_link_path(file_path, link_url)
            
            if self.check_link_exists(target_path):
                result['valid_links'] += 1
                result['valid_link_list'].append({
                    'url': link_url,
                    'line': line_num,
                    'target': str(target_path)
                })
                self.valid_links.append({
                    'file': str(file_path),
                    'url': link_url,
                    'line': line_num,
                    'target': str(target_path)
                })
            else:
                broken_link = {
                    'url': link_url,
                    'line': line_num,
                    'target': str(target_path)
                }
                result['broken_links'].append(broken_link)
                self.broken_links.append({
                    'file': str(file_path),
                    **broken_link
                })
                
        return result
    
    def run_check(self) -> Dict:
        """运行完整的链接检查"""
        print("🔍 开始检查文档链接...")
        print(f"📁 扫描目录: {self.docs_root}")
        
        md_files = self.find_markdown_files()
        print(f"📄 找到 {len(md_files)} 个 Markdown 文件")
        
        results = []
        total_links = 0
        total_valid = 0
        total_broken = 0
        
        for file_path in md_files:
            print(f"🔍 检查: {file_path}")
            result = self.check_file_links(file_path)
            results.append(result)
            
            total_links += result['total_links']
            total_valid += result['valid_links']
            total_broken += len(result['broken_links'])
            
        summary = {
            'total_files': len(md_files),
            'total_links': total_links,
            'valid_links': total_valid,
            'broken_links': total_broken,
            'success_rate': (total_valid / total_links * 100) if total_links > 0 else 100,
            'file_results': results
        }
        
        return summary
    
    def print_report(self, summary: Dict):
        """打印检查报告"""
        print("\n" + "="*60)
        print("📊 文档链接检查报告")
        print("="*60)
        
        print(f"📁 检查文件数: {summary['total_files']}")
        print(f"🔗 总链接数: {summary['total_links']}")
        print(f"✅ 有效链接: {summary['valid_links']}")
        print(f"❌ 失效链接: {summary['broken_links']}")
        print(f"📈 成功率: {summary['success_rate']:.1f}%")
        
        if summary['broken_links'] > 0:
            print("\n❌ 失效链接详情:")
            print("-" * 60)
            for link in self.broken_links:
                print(f"📄 文件: {link['file']}")
                print(f"🔗 链接: {link['url']}")
                print(f"📍 行号: {link['line']}")
                print(f"🎯 目标: {link['target']}")
                print("-" * 40)
        
        if summary['broken_links'] == 0:
            print("\n🎉 所有链接都有效！")
        else:
            print(f"\n⚠️  发现 {summary['broken_links']} 个失效链接，请及时修复。")
    
    def save_report(self, summary: Dict, output_file: str = "docs_link_check_report.md"):
        """保存检查报告到文件"""
        report_content = f"""# 文档链接检查报告

**检查时间**: {Path.cwd()}
**检查目录**: {self.docs_root}

## 📊 检查统计

- **检查文件数**: {summary['total_files']}
- **总链接数**: {summary['total_links']}
- **有效链接**: {summary['valid_links']}
- **失效链接**: {summary['broken_links']}
- **成功率**: {summary['success_rate']:.1f}%

## 📄 文件详情

"""
        
        for result in summary['file_results']:
            report_content += f"### {result['file']}\n\n"
            report_content += f"- 总链接: {result['total_links']}\n"
            report_content += f"- 有效链接: {result['valid_links']}\n"
            report_content += f"- 失效链接: {len(result['broken_links'])}\n\n"
            
            if result['broken_links']:
                report_content += "**失效链接**:\n"
                for link in result['broken_links']:
                    report_content += f"- 第{link['line']}行: `{link['url']}` → `{link['target']}`\n"
                report_content += "\n"
        
        if summary['broken_links'] > 0:
            report_content += "## ❌ 所有失效链接\n\n"
            for link in self.broken_links:
                report_content += f"- **{link['file']}** (第{link['line']}行): `{link['url']}`\n"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 报告已保存到: {output_file}")


def main():
    """主函数"""
    # 检查是否在项目根目录
    if not Path("docs").exists():
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    checker = DocsLinkChecker()
    summary = checker.run_check()
    checker.print_report(summary)
    
    # 保存报告
    report_file = "scripts/maintenance/docs_link_check_report.md"
    checker.save_report(summary, report_file)
    
    # 如果有失效链接，返回非零退出码
    if summary['broken_links'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
