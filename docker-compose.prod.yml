# 生产环境 Docker Compose 配置
services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_prod_data:/data
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=587
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - EMAIL_USE_TLS=False
      - SECURE_SSL_REDIRECT=False
      - SECURE_HSTS_SECONDS=0
      - SECURE_HSTS_INCLUDE_SUBDOMAINS=False
      - SECURE_HSTS_PRELOAD=False
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 4 --worker-class gevent --worker-connections 1000 --max-requests 1000 --max-requests-jitter 100 --timeout 30 --keep-alive 2 --log-level info --access-logfile - --error-logfile -
    volumes:
      - static_prod_volume:/app/staticfiles
      - media_prod_volume:/app/media
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - db
      - redis

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      target: production
      args:
        - NODE_ENV=production
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: mdm_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d/prod.conf:/etc/nginx/conf.d/default.conf
      - static_prod_volume:/var/www/static:ro
      - media_prod_volume:/var/www/media:ro
      - ./ssl:/etc/ssl/certs:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    command: celery -A config worker -l info --concurrency=4 --max-tasks-per-child=1000
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - db
      - redis

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      target: production
    command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${POSTGRES_DB}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "python", "/app/scripts/celery_beat_healthcheck.py"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    depends_on:
      - db
      - redis

  # 数据库备份服务
  db-backup:
    image: postgres:14-alpine
    container_name: mdm_db_backup
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
    command: |
      sh -c '
        while true; do
          echo "Starting database backup at $$(date)"
          pg_dump -h db -U ${POSTGRES_USER} ${POSTGRES_DB} > /backups/backup_$$(date +%Y%m%d_%H%M%S).sql
          echo "Backup completed at $$(date)"
          find /backups -name "backup_*.sql" -mtime +7 -delete
          sleep 86400
        done
      '
    depends_on:
      - db
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data:
  static_prod_volume:
  media_prod_volume:
