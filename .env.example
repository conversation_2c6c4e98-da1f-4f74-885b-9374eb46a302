# ===========================================
# 设备管理系统环境变量配置文件
# ===========================================
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# 基础配置
# ===========================================
# 环境类型: development, testing, production
NODE_ENV=development
DEBUG=True

# 应用密钥 (生产环境必须修改)
SECRET_KEY=dev-secret-key-change-in-production-environment

# 允许的主机 (生产环境必须配置)
ALLOWED_HOSTS=localhost,127.0.0.1,backend

# CORS允许的源 (前端域名)
CORS_ALLOWED_ORIGINS=http://localhost:5174,http://127.0.0.1:5174

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL配置
POSTGRES_DB=mdm_dev
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
DATABASE_URL=**************************************/mdm_dev

# ===========================================
# 缓存配置
# ===========================================
# Redis配置
REDIS_PASSWORD=redis123
REDIS_URL=redis://:redis123@redis:6379/0

# ===========================================
# 前端配置
# ===========================================
# API基础URL
VITE_API_BASE_URL=http://localhost:8000

# ===========================================
# 邮件配置
# ===========================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>

# ===========================================
# 安全配置 (生产环境)
# ===========================================
# SSL重定向
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
SECURE_HSTS_INCLUDE_SUBDOMAINS=False
SECURE_HSTS_PRELOAD=False

# ===========================================
# 监控配置
# ===========================================
# Grafana管理员密码
GRAFANA_PASSWORD=admin123

# Elasticsearch配置 (日志收集)
ELASTICSEARCH_HOSTS=http://elasticsearch:9200

# ===========================================
# 文件存储配置
# ===========================================
# 媒体文件存储路径
MEDIA_ROOT=/app/media
MEDIA_URL=/media/

# 静态文件存储路径
STATIC_ROOT=/app/staticfiles
STATIC_URL=/static/

# ===========================================
# 第三方服务配置
# ===========================================
# 短信服务配置 (可选)
SMS_API_KEY=your-sms-api-key
SMS_API_SECRET=your-sms-api-secret

# 对象存储配置 (可选，用于文件上传)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# ===========================================
# 开发工具配置
# ===========================================
# Django调试工具栏
INTERNAL_IPS=127.0.0.1,localhost

# 日志级别
LOG_LEVEL=INFO

# ===========================================
# Celery配置
# ===========================================
CELERY_BROKER_URL=redis://:redis123@redis:6379/0
CELERY_RESULT_BACKEND=redis://:redis123@redis:6379/0

# ===========================================
# 备份配置
# ===========================================
# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份存储路径
BACKUP_PATH=/opt/mdm/backups

# ===========================================
# 性能配置
# ===========================================
# Gunicorn工作进程数
GUNICORN_WORKERS=4

# 数据库连接池大小
DB_CONN_MAX_AGE=300

# 缓存超时时间 (秒)
CACHE_TIMEOUT=3600

# ===========================================
# 生产环境示例配置
# ===========================================
# 取消注释并修改以下配置用于生产环境

# NODE_ENV=production
# DEBUG=False
# SECRET_KEY=your-super-secret-production-key-here
# ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
# CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# # 生产数据库
# POSTGRES_DB=mdm_prod
# POSTGRES_USER=mdm_user
# POSTGRES_PASSWORD=your-strong-database-password

# # 生产Redis
# REDIS_PASSWORD=your-strong-redis-password

# # 生产API URL
# VITE_API_BASE_URL=https://api.yourdomain.com

# # 生产邮件配置
# EMAIL_HOST=smtp.yourdomain.com
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-email-password

# # 生产安全配置
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True

# ===========================================
# 注意事项
# ===========================================
# 1. 生产环境必须修改所有密码和密钥
# 2. 确保数据库和Redis密码足够强壮
# 3. 配置正确的域名和CORS设置
# 4. 启用HTTPS和安全头
# 5. 定期备份数据库和重要文件
# 6. 监控系统资源使用情况
# 7. 设置日志轮转和清理策略
