@echo off
REM Device Management Platform - Stop All Environments Script
REM Stop all environment services

echo ========================================
echo Device Management Platform - Stop All Environments
echo ========================================
echo.

echo [1/4] Stopping development environment...
docker compose -f docker-compose.dev.yml down

echo [2/4] Stopping production environment...
docker compose -f docker-compose.prod.yml down

echo [3/4] Stopping lite production environment...
docker compose -f docker-compose.prod-lite.yml down

echo [4/4] Cleaning Docker resources...
docker system prune -f

echo.
echo ========================================
echo All environments stopped and cleaned
echo ========================================

pause
